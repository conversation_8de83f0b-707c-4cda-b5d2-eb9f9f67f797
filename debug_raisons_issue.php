<?php
/**
 * فحص مشكلة الأسباب التي تسبب التكرار
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // تسجيل دخول تلقائي
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🔍 فحص مشكلة الأسباب</h2>";
    echo "<p>✅ تم تسجيل الدخول كـ: " . $_SESSION['username'] . "</p>";
    
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>📊 1. فحص الدوسيهات الأساسية</h3>";
    
    $sql = "SELECT id, numero_dossier, numero_adherent, nom FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            ORDER BY id ASC";
    $dossiers_basic = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• عدد الدوسيهات: " . count($dossiers_basic) . "<br>";
    foreach ($dossiers_basic as $index => $dossier) {
        echo ($index + 1) . ". ID: " . $dossier['id'] . " - " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . "<br>";
    }
    
    echo "<h3>📊 2. فحص جدول dossier_raisons</h3>";
    
    $sql = "SELECT dr.*, r.raison, d.numero_dossier, d.numero_adherent
            FROM dossier_raisons dr
            JOIN raisons_retour r ON dr.raison_id = r.id
            JOIN dossiers_retournes d ON dr.dossier_id = d.id
            WHERE d.numero_bordereau = ? AND d.cdm_id = ?
            ORDER BY dr.dossier_id ASC";
    $all_raisons = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• عدد الأسباب في الجدول: " . count($all_raisons) . "<br>";
    
    echo "<h4>📋 تفاصيل الأسباب:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='padding: 5px;'>ID السبب</th>";
    echo "<th style='padding: 5px;'>ID الدوسيه</th>";
    echo "<th style='padding: 5px;'>رقم الدوسيه</th>";
    echo "<th style='padding: 5px;'>رقم المنخرط</th>";
    echo "<th style='padding: 5px;'>السبب</th>";
    echo "<th style='padding: 5px;'>تاريخ الإنشاء</th>";
    echo "</tr>";
    
    $raisons_per_dossier = [];
    foreach ($all_raisons as $raison) {
        $dossier_id = $raison['dossier_id'];
        if (!isset($raisons_per_dossier[$dossier_id])) {
            $raisons_per_dossier[$dossier_id] = 0;
        }
        $raisons_per_dossier[$dossier_id]++;
        
        $bg_color = $raisons_per_dossier[$dossier_id] > 1 ? '#ffebee' : '#ffffff';
        
        echo "<tr style='background-color: $bg_color;'>";
        echo "<td style='padding: 5px;'>" . $raison['id'] . "</td>";
        echo "<td style='padding: 5px;'>" . $raison['dossier_id'] . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($raison['numero_dossier']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($raison['numero_adherent']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($raison['raison']) . "</td>";
        echo "<td style='padding: 5px;'>" . $raison['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>📊 3. تحليل المشكلة</h3>";
    
    foreach ($raisons_per_dossier as $dossier_id => $count) {
        if ($count > 1) {
            echo "⚠️ <strong>الدوسيه ID $dossier_id لديه $count أسباب</strong><br>";
        } else {
            echo "✅ الدوسيه ID $dossier_id لديه سبب واحد<br>";
        }
    }
    
    echo "<h3>📊 4. محاكاة كود معالجة الأسباب</h3>";
    
    // جلب الدوسيهات مع الجداول المرتبطة
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• دوسيهات مُسترجعة: " . count($dossiers) . "<br>";
    
    echo "<h4>📋 معالجة كل دوسيه:</h4>";
    foreach ($dossiers as $index => &$dossier) {
        echo "<h5>دوسيه " . ($index + 1) . ": " . $dossier['numero_dossier'] . " (ID: " . $dossier['id'] . ")</h5>";
        
        $sql = "SELECT dr.*, r.raison as raison
                FROM dossier_raisons dr
                JOIN raisons_retour r ON dr.raison_id = r.id
                WHERE dr.dossier_id = ?
                ORDER BY dr.created_at ASC";
        $raisons = $db->all($sql, [$dossier['id']]);
        
        echo "• أسباب من dossier_raisons: " . count($raisons) . "<br>";
        foreach ($raisons as $raison) {
            echo "  - " . $raison['raison'] . "<br>";
        }
        
        // Si aucune raison n'est trouvée dans la nouvelle table, utiliser la raison principale
        if (empty($raisons) && !empty($dossier['raison_retour_nom'])) {
            $dossier['raisons'] = [['raison' => $dossier['raison_retour_nom']]];
            echo "• استخدام السبب الرئيسي: " . $dossier['raison_retour_nom'] . "<br>";
        } else {
            $dossier['raisons'] = $raisons;
            echo "• استخدام الأسباب من dossier_raisons<br>";
        }
        
        // Créer une chaîne de toutes les raisons pour l'affichage
        $raisons_str = [];
        foreach ($dossier['raisons'] as $raison) {
            $raisons_str[] = $raison['raison'];
        }
        $dossier['raisons_str'] = implode(', ', $raisons_str);
        
        echo "• النتيجة النهائية: " . $dossier['raisons_str'] . "<br><br>";
    }
    
    echo "<h3>📊 5. فحص النتيجة النهائية</h3>";
    
    echo "<h4>📋 الدوسيهات النهائية:</h4>";
    $final_unique = [];
    $final_has_duplicates = false;
    
    foreach ($dossiers as $index => $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($final_unique[$key])) {
            $final_has_duplicates = true;
            echo "<span style='color: red; font-weight: bold;'>❌ " . ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " (تكرار)</span><br>";
        } else {
            $final_unique[$key] = true;
            echo "<span style='color: green;'>✅ " . ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . "</span><br>";
        }
    }
    
    if (!$final_has_duplicates) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ لا توجد مشكلة!</h4>";
        echo "<p style='color: #155724;'>جميع الدوسيهات فريدة.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24; margin-top: 0;'>❌ توجد مشكلة!</h4>";
        echo "<p style='color: #721c24;'>هناك تكرارات في النتيجة النهائية.</p>";
        echo "<p style='color: #721c24;'>المشكلة في كود معالجة الأسباب أو في البيانات نفسها.</p>";
        echo "</div>";
        
        echo "<h4>🔧 الحل:</h4>";
        echo "<ul>";
        echo "<li><a href='fix_raisons_issue.php' target='_blank'>🔧 إصلاح مشكلة الأسباب</a></li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>التتبع: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص مشكلة الأسباب - LOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        h2, h3, h4, h5 {
            color: #333;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        table {
            background: white;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            direction: ltr;
        }
    </style>
</head>
<body>
</body>
</html>
