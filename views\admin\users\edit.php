<?php
// Vue pour la modification d'un utilisateur
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-user-edit"></i> Modifier un utilisateur</h1>
        <div>
            <a href="index.php?page=admin/users" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>

    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-warning text-dark">
            <h5 class="mb-0"><i class="fas fa-user-edit"></i> Informations de l'utilisateur</h5>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=admin/users/edit&id=<?php echo $user['id']; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="username" class="form-label">Nom d'utilisateur <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($form_data['username']); ?>" required>
                    </div>

                    <div class="col-md-6">
                        <label for="email" class="form-label">Adresse e-mail <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($form_data['email']); ?>" required>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="password" class="form-label">Mot de passe <small class="text-muted">(Laisser vide pour ne pas modifier)</small></label>
                        <input type="password" class="form-control" id="password" name="password">
                    </div>

                    <div class="col-md-6">
                        <label for="confirm_password" class="form-label">Confirmer le mot de passe</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="role" class="form-label">Rôle <span class="text-danger">*</span></label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="user" <?php echo isset($form_data['role']) && $form_data['role'] === 'user' ? 'selected' : ''; ?>>Utilisateur</option>
                            <option value="admin" <?php echo isset($form_data['role']) && $form_data['role'] === 'admin' ? 'selected' : ''; ?>>Administrateur</option>
                        </select>
                    </div>

                    <div class="col-md-6">
                        <label for="active" class="form-label">Statut</label>
                        <div class="form-check form-switch mt-2">
                            <input class="form-check-input" type="checkbox" id="active" name="active" <?php echo isset($form_data['active']) && $form_data['active'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="active">Actif</label>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="index.php?page=admin/users" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Mettre à jour
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
