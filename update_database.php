<?php
/**
 * <PERSON><PERSON>t de mise à jour de la base de données
 * Ce script ajoute toutes les colonnes et tables manquantes nécessaires au bon fonctionnement de l'application
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'config/database.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Tableau pour stocker les messages
$messages = [];

try {
    // Démarrer une transaction
    $db->beginTransaction();

    // 1. Vérifier et ajouter la colonne 'prix' à la table 'actes'
    $messages[] = checkAndAddColumn('actes', 'prix', "DECIMAL(10,2) DEFAULT 0.00 AFTER description");

    // 2. Vérifier et ajouter la colonne 'active' à la table 'actes'
    $messages[] = checkAndAddColumn('actes', 'active', "TINYINT(1) NOT NULL DEFAULT 1");

    // 3. Vérifier et ajouter la colonne 'active' à la table 'raisons_retour'
    $messages[] = checkAndAddColumn('raisons_retour', 'active', "TINYINT(1) NOT NULL DEFAULT 1");

    // 4. Vérifier et créer la table 'bordereaux_retour'
    $messages[] = checkAndCreateBordereauxTable();

    // 5. Vérifier et ajouter la colonne 'notes' à la table 'bordereaux_retour'
    $messages[] = checkAndAddColumn('bordereaux_retour', 'notes', "TEXT NULL AFTER date_bordereau");

    // 6. Vérifier et créer la table 'dossier_raisons'
    $messages[] = checkAndCreateDossierRaisonsTable();

    // 7. Vérifier et ajouter la colonne 'action_type' à la table 'logs'
    $messages[] = checkAndAddColumn('logs', 'action_type', "VARCHAR(50) DEFAULT 'info' AFTER action");

    // 8. Vérifier et ajouter la colonne 'beneficiaire' à la table 'dossiers_retournes' si elle n'existe pas
    $messages[] = checkAndAddColumn('dossiers_retournes', 'beneficiaire', "VARCHAR(100) AFTER nom");

    // 9. Vérifier et ajouter la colonne 'notes' à la table 'dossiers_retournes' si elle n'existe pas
    $messages[] = checkAndAddColumn('dossiers_retournes', 'notes', "TEXT AFTER corrige");

    // Valider la transaction
    $db->commit();

    // Afficher un message de succès
    $success = true;
    $message = "La base de données a été mise à jour avec succès.";

} catch (Exception $e) {
    // Annuler la transaction en cas d'erreur
    $db->rollBack();
    
    // Afficher un message d'erreur
    $success = false;
    $message = "Erreur lors de la mise à jour de la base de données: " . $e->getMessage();
}

/**
 * Vérifie si une colonne existe dans une table et l'ajoute si elle n'existe pas
 * 
 * @param string $table Nom de la table
 * @param string $column Nom de la colonne
 * @param string $definition Définition de la colonne (type, contraintes, etc.)
 * @return string Message indiquant le résultat de l'opération
 */
function checkAndAddColumn($table, $column, $definition) {
    global $db;
    
    // Vérifier si la table existe
    $sql = "SHOW TABLES LIKE '$table'";
    $result = $db->query($sql);
    
    if ($result->rowCount() === 0) {
        return "La table '$table' n'existe pas.";
    }
    
    // Vérifier si la colonne existe déjà
    $sql = "SHOW COLUMNS FROM $table LIKE '$column'";
    $result = $db->query($sql);
    
    if ($result->rowCount() === 0) {
        // La colonne n'existe pas, l'ajouter
        $sql = "ALTER TABLE $table ADD COLUMN $column $definition";
        $db->query($sql);
        return "La colonne '$column' a été ajoutée à la table '$table'.";
    } else {
        return "La colonne '$column' existe déjà dans la table '$table'.";
    }
}

/**
 * Vérifie si la table bordereaux_retour existe et la crée si elle n'existe pas
 * 
 * @return string Message indiquant le résultat de l'opération
 */
function checkAndCreateBordereauxTable() {
    global $db;
    
    // Vérifier si la table existe
    $sql = "SHOW TABLES LIKE 'bordereaux_retour'";
    $result = $db->query($sql);
    
    if ($result->rowCount() === 0) {
        // Créer la table bordereaux_retour
        $sql = "CREATE TABLE bordereaux_retour (
            id INT AUTO_INCREMENT PRIMARY KEY,
            numero INT NOT NULL,
            cdm_id INT NOT NULL,
            date_bordereau DATE NOT NULL,
            notes TEXT NULL,
            created_by INT NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NULL,
            UNIQUE KEY unique_bordereau (numero, cdm_id),
            FOREIGN KEY (cdm_id) REFERENCES cdm(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )";
        $db->query($sql);
        
        // Migrer les données existantes
        $sql = "INSERT IGNORE INTO bordereaux_retour (numero, cdm_id, date_bordereau, created_by, created_at)
                SELECT DISTINCT numero_bordereau, cdm_id, date_bordereau, created_by, created_at
                FROM dossiers_retournes";
        $db->query($sql);
        
        return "La table 'bordereaux_retour' a été créée et les données ont été migrées.";
    } else {
        return "La table 'bordereaux_retour' existe déjà.";
    }
}

/**
 * Vérifie si la table dossier_raisons existe et la crée si elle n'existe pas
 * 
 * @return string Message indiquant le résultat de l'opération
 */
function checkAndCreateDossierRaisonsTable() {
    global $db;
    
    // Vérifier si la table existe
    $sql = "SHOW TABLES LIKE 'dossier_raisons'";
    $result = $db->query($sql);
    
    if ($result->rowCount() === 0) {
        // Créer la table dossier_raisons
        $sql = "CREATE TABLE dossier_raisons (
            id INT AUTO_INCREMENT PRIMARY KEY,
            dossier_id INT NOT NULL,
            raison_id INT NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_dossier_raison (dossier_id, raison_id),
            FOREIGN KEY (dossier_id) REFERENCES dossiers_retournes(id) ON DELETE CASCADE,
            FOREIGN KEY (raison_id) REFERENCES raisons_retour(id) ON DELETE CASCADE
        )";
        $db->query($sql);
        
        // Migrer les données existantes
        $sql = "INSERT INTO dossier_raisons (dossier_id, raison_id, created_at)
                SELECT id, raison_retour_id, created_at FROM dossiers_retournes 
                WHERE raison_retour_id IS NOT NULL AND raison_retour_id > 0";
        $db->query($sql);
        
        return "La table 'dossier_raisons' a été créée et les données ont été migrées.";
    } else {
        return "La table 'dossier_raisons' existe déjà.";
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mise à jour de la base de données</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
        }
        .container {
            max-width: 800px;
        }
        .message-list {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mise à jour de la base de données</h1>
        
        <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?>">
            <?php echo $message; ?>
        </div>
        
        <div class="message-list">
            <h3>Détails des opérations</h3>
            <ul class="list-group">
                <?php foreach ($messages as $msg): ?>
                    <li class="list-group-item"><?php echo $msg; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <div class="mt-4">
            <a href="index.php" class="btn btn-primary">Retour à l'accueil</a>
        </div>
    </div>
</body>
</html>
