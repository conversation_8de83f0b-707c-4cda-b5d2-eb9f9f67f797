<?php
// Script pour vérifier la structure de la table dossiers_retournes
require_once 'config/config.php';
require_once 'config/database.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

try {
    // Vérifier la structure de la table dossiers_retournes
    $sql = "DESCRIBE dossiers_retournes";
    $result = $db->all($sql);
    
    echo "<h2>Structure de la table dossiers_retournes</h2>";
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    // Vérifier les données existantes
    $sql = "SELECT * FROM dossiers_retournes LIMIT 5";
    $data = $db->all($sql);
    
    echo "<h2>Exemples de données dans dossiers_retournes</h2>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
    
    // Vérifier les contraintes d'unicité
    $sql = "SHOW CREATE TABLE dossiers_retournes";
    $create_table = $db->single($sql);
    
    echo "<h2>Définition de la table dossiers_retournes</h2>";
    echo "<pre>";
    print_r($create_table);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage();
}
?>
