<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        .install-container {
            max-width: 600px;
            width: 100%;
            padding: 20px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            font-size: 2.5rem;
            color: #0d6efd;
        }
        
        .card {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .card-header {
            background-color: #0d6efd;
            color: white;
            font-weight: 600;
        }
        
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="logo">
            <h1><?php echo APP_NAME; ?></h1>
            <p class="lead">Installation</p>
        </div>
        
        <?php displayFlashMessage(); ?>
        
        <?php if (!empty($form_errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Erreurs:</h5>
                <ul class="mb-0">
                    <?php foreach ($form_errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cog"></i> Configuration de l'application</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Cette page vous permet d'installer l'application et de créer un compte administrateur.
                </div>
                
                <form method="post" action="index.php?page=install">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-3">
                        <label for="admin_username" class="form-label">Nom d'utilisateur administrateur</label>
                        <input type="text" class="form-control" id="admin_username" name="admin_username" value="<?php echo $form_data['admin_username']; ?>" required>
                        <div class="form-text">Ce nom d'utilisateur sera utilisé pour vous connecter en tant qu'administrateur.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="admin_password" class="form-label">Mot de passe administrateur</label>
                        <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                        <div class="form-text">Choisissez un mot de passe sécurisé d'au moins 8 caractères.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="admin_password_confirm" class="form-label">Confirmer le mot de passe</label>
                        <input type="password" class="form-control" id="admin_password_confirm" name="admin_password_confirm" required>
                        <div class="form-text">Répétez le mot de passe pour confirmation.</div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check"></i> Installer l'application
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <p class="text-muted">
                <?php echo APP_NAME; ?> v<?php echo APP_VERSION; ?>
            </p>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
