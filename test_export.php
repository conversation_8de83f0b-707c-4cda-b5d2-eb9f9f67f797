<?php
/**
 * Test d'exportation pour vérifier la correction du problème des données manquantes
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // Créer la session admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🧪 Test d'Exportation</h2>";
    echo "<p>✅ Session admin créée</p>";
    
    // Paramètres du test
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>📊 Test des données d'exportation</h3>";
    
    // Récupérer les informations du CDM
    $sql = "SELECT * FROM cdm WHERE id = ?";
    $cdm = $db->single($sql, [$cdm_id]);
    
    if ($cdm) {
        echo "✅ CDM trouvé: " . $cdm['nom'] . "<br>";
    } else {
        echo "❌ CDM non trouvé<br>";
    }
    
    // Récupérer les dossiers avec toutes les informations
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "<h4>📋 Dossiers trouvés: " . count($dossiers) . "</h4>";
    
    // Récupérer les informations du bordereau
    $sql = "SELECT * FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
    $bordereau = $db->single($sql, [$numero_bordereau, $cdm_id]);
    
    if ($bordereau) {
        echo "✅ Bordereau trouvé: " . $bordereau['date_bordereau'] . "<br>";
    } else {
        echo "⚠️ Bordereau non trouvé dans la table bordereaux_retour<br>";
    }
    
    // Enrichir les dossiers avec les raisons multiples
    foreach ($dossiers as &$dossier) {
        // Vérifier si la table dossier_raisons existe
        $sql = "SHOW TABLES LIKE 'dossier_raisons'";
        $result = $db->query($sql);
        $table_exists = $result->rowCount() > 0;
        
        if ($table_exists) {
            $sql = "SELECT r.raison
                    FROM dossier_raisons dr
                    JOIN raisons_retour r ON dr.raison_id = r.id
                    WHERE dr.dossier_id = ?
                    ORDER BY dr.created_at ASC";
            $raisons = $db->all($sql, [$dossier['id']]);
            
            if (!empty($raisons)) {
                $raisons_str = [];
                foreach ($raisons as $raison) {
                    $raisons_str[] = $raison['raison'];
                }
                $dossier['raisons_str'] = implode(', ', $raisons_str);
            } else {
                $dossier['raisons_str'] = $dossier['raison_retour_nom'] ?? '';
            }
        } else {
            $dossier['raisons_str'] = $dossier['raison_retour_nom'] ?? '';
        }
    }
    
    echo "<h4>📊 Aperçu des données d'exportation:</h4>";
    
    // Préparer les en-têtes
    $headers = [
        'N°',
        'N° Bordereau',
        'CDM',
        'Date Bordereau',
        'N° Dossier',
        'N° Adhérent',
        'Nom',
        'Bénéficiaire',
        'Code Acte',
        'Nom Acte',
        'Montant (DH)',
        'Raisons du retour',
        'Date retour',
        'Date correction',
        'Statut',
        'Notes'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    foreach ($headers as $header) {
        echo "<th style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($header) . "</th>";
    }
    echo "</tr>";
    
    // Préparer les données
    foreach ($dossiers as $index => $dossier) {
        $data = [
            $index + 1,
            $numero_bordereau,
            $cdm['nom'] ?? 'N/A',
            formatDate($bordereau['date_bordereau'] ?? $dossier['date_bordereau']),
            $dossier['numero_dossier'] ?? '',
            $dossier['numero_adherent'] ?? '',
            $dossier['nom'] ?? '',
            $dossier['beneficiaire'] ?? '',
            $dossier['acte_code'] ?? '',
            $dossier['acte_nom'] ?? '',
            !empty($dossier['montant']) ? number_format(floatval($dossier['montant']), 2, ',', ' ') : '0,00',
            $dossier['raisons_str'] ?? $dossier['raison_retour_nom'] ?? '',
            formatDate($dossier['date_retour']),
            formatDate($dossier['date_correction']),
            $dossier['corrige'] ? 'Corrigé' : 'Non corrigé',
            $dossier['notes'] ?? ''
        ];
        
        echo "<tr>";
        foreach ($data as $cell) {
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($cell) . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>🔍 Vérifications détaillées:</h3>";
    
    foreach ($dossiers as $index => $dossier) {
        echo "<h4>Dossier " . ($index + 1) . ": " . $dossier['numero_dossier'] . "</h4>";
        echo "<ul>";
        echo "<li><strong>Montant:</strong> " . ($dossier['montant'] ?? 'NULL') . " (" . gettype($dossier['montant']) . ")</li>";
        echo "<li><strong>Acte:</strong> " . ($dossier['acte_nom'] ?? 'NULL') . " (Code: " . ($dossier['acte_code'] ?? 'NULL') . ")</li>";
        echo "<li><strong>Raison principale:</strong> " . ($dossier['raison_retour_nom'] ?? 'NULL') . "</li>";
        echo "<li><strong>Raisons multiples:</strong> " . ($dossier['raisons_str'] ?? 'NULL') . "</li>";
        echo "<li><strong>Notes:</strong> " . ($dossier['notes'] ?? 'NULL') . "</li>";
        echo "<li><strong>Statut:</strong> " . ($dossier['corrige'] ? 'Corrigé' : 'Non corrigé') . "</li>";
        echo "</ul>";
    }
    
    echo "<h3>📥 Test d'exportation réelle:</h3>";
    
    // Simuler l'exportation
    $export_url = "index.php?page=retours/export&numero_bordereau=$numero_bordereau&cdm_id=$cdm_id";
    echo "<p><a href='$export_url' target='_blank' class='btn'>📥 Télécharger l'export Excel</a></p>";
    
    echo "<h3>🔗 Liens de test:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank'>📝 Voir le bordereau</a></li>";
    echo "<li><a href='$export_url' target='_blank'>📥 Export Excel</a></li>";
    echo "<li><a href='test_add_dossier.php' target='_blank'>➕ Test ajout dossier</a></li>";
    echo "<li><a href='maintenance.php?key=LOT2024MAINTENANCE' target='_blank'>🛠️ Maintenance</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Erreur</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
    echo "<p>Trace: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Export - LOT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #17a2b8;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        .btn {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
        }
        
        .btn:hover {
            background: #218838;
            color: white;
            text-decoration: none;
        }
        
        table {
            background: white;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
</body>
</html>
