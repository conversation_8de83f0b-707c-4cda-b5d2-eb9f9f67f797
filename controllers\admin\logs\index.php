<?php
// Contrôleur pour la gestion des journaux d'activité

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Titre de la page
$page_title = 'Journaux d\'activité';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de pagination
$page_num = isset($_GET['page_num']) ? intval($_GET['page_num']) : 1;
$items_per_page = ITEMS_PER_PAGE;
$offset = ($page_num - 1) * $items_per_page;

// Filtres
$search = isset($_GET['search']) ? clean($_GET['search']) : '';
$user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
$action_type = isset($_GET['action_type']) ? clean($_GET['action_type']) : '';
$date_from = isset($_GET['date_from']) ? clean($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? clean($_GET['date_to']) : '';

// Construction de la requête SQL
$sql_count = "SELECT COUNT(*) as total FROM logs l LEFT JOIN users u ON l.user_id = u.id";
$sql = "SELECT l.*, u.username FROM logs l LEFT JOIN users u ON l.user_id = u.id";

$where_clauses = [];
$params = [];

// Appliquer les filtres
if (!empty($search)) {
    $where_clauses[] = "(l.action LIKE ? OR l.details LIKE ? OR u.username LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($user_id > 0) {
    $where_clauses[] = "l.user_id = ?";
    $params[] = $user_id;
}

if (!empty($action_type)) {
    $where_clauses[] = "l.action_type = ?";
    $params[] = $action_type;
}

if (!empty($date_from)) {
    $where_clauses[] = "l.created_at >= ?";
    $params[] = $date_from . ' 00:00:00';
}

if (!empty($date_to)) {
    $where_clauses[] = "l.created_at <= ?";
    $params[] = $date_to . ' 23:59:59';
}

// Ajouter les clauses WHERE à la requête
if (!empty($where_clauses)) {
    $sql_count .= " WHERE " . implode(" AND ", $where_clauses);
    $sql .= " WHERE " . implode(" AND ", $where_clauses);
}

// Obtenir le nombre total de logs
$result = $db->single($sql_count, $params);
$total_items = $result['total'];
$total_pages = ceil($total_items / $items_per_page);

// Ajouter l'ordre et la limite à la requête principale
$sql .= " ORDER BY l.created_at DESC LIMIT ?, ?";
$params[] = $offset;
$params[] = $items_per_page;

// Exécuter la requête
$logs_list = $db->all($sql, $params);

// Liste des utilisateurs pour le filtre
$sql = "SELECT id, username FROM users ORDER BY username ASC";
$users_list = $db->all($sql);

// Liste des types d'actions pour le filtre
$sql = "SELECT DISTINCT action_type FROM logs ORDER BY action_type ASC";
$action_types = $db->all($sql);

// Traitement des actions
$message = '';
$message_type = '';

if (isset($_POST['action'])) {
    $action = $_POST['action'];
    
    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $message = 'Erreur de sécurité. Veuillez réessayer.';
        $message_type = 'danger';
    } else {
        switch ($action) {
            case 'clear':
                // Vider les journaux
                $days = isset($_POST['days']) ? intval($_POST['days']) : 0;
                
                if ($days > 0) {
                    $date_limit = date('Y-m-d H:i:s', strtotime("-$days days"));
                    $sql = "DELETE FROM logs WHERE created_at < ?";
                    $db->query($sql, [$date_limit]);
                    
                    $message = 'Les journaux d\'activité de plus de ' . $days . ' jours ont été supprimés.';
                    $message_type = 'success';
                } else {
                    $sql = "TRUNCATE TABLE logs";
                    $db->query($sql);
                    
                    $message = 'Tous les journaux d\'activité ont été supprimés.';
                    $message_type = 'success';
                }
                
                // Rediriger pour éviter la soumission multiple du formulaire
                redirect('index.php?page=admin/logs', $message, $message_type);
                break;
                
            default:
                $message = 'Action non reconnue.';
                $message_type = 'danger';
                break;
        }
    }
}

// Charger la vue
require_once VIEWS_PATH . 'admin/logs/index.php';
?>
