<?php
// Vue pour le tableau de bord
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-tachometer-alt"></i> Tableau de bord</h1>
        <div>
            <button id="refreshDashboard" class="btn btn-outline-primary">
                <i class="fas fa-sync-alt"></i> Actualiser
            </button>
        </div>
    </div>
    
    <!-- Statistiques générales -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stat-card primary">
                <div class="card-body">
                    <i class="fas fa-layer-group stat-icon"></i>
                    <div class="stat-value"><?php echo $stats['total_lots']; ?></div>
                    <div class="stat-label">Lots au total</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card success">
                <div class="card-body">
                    <i class="fas fa-folder-open stat-icon"></i>
                    <div class="stat-value"><?php echo $stats['total_dossiers']; ?></div>
                    <div class="stat-label">Dossiers au total</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card warning">
                <div class="card-body">
                    <i class="fas fa-clock stat-icon"></i>
                    <div class="stat-value"><?php echo $stats['dossiers_en_attente']; ?></div>
                    <div class="stat-label">Dossiers en attente</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card danger">
                <div class="card-body">
                    <i class="fas fa-undo-alt stat-icon"></i>
                    <div class="stat-value"><?php echo $stats['total_retours']; ?></div>
                    <div class="stat-label">Dossiers retournés</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistiques détaillées -->
    <div class="row mb-4">
        <!-- Statistiques des lots -->
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-layer-group"></i> Statistiques des lots</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <div>Lots en attente:</div>
                        <div><strong><?php echo $stats['lots_en_attente']; ?></strong></div>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <div>Lots reçus:</div>
                        <div><strong><?php echo $stats['lots_recus']; ?></strong></div>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <div>Lots traités:</div>
                        <div><strong><?php echo $stats['lots_traites']; ?></strong></div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <div>Total:</div>
                        <div><strong><?php echo $stats['total_lots']; ?></strong></div>
                    </div>
                    
                    <?php if ($stats['total_lots'] > 0): ?>
                        <div class="mt-3">
                            <div class="progress mb-2" style="height: 20px;">
                                <?php 
                                    $en_attente_percent = ($stats['lots_en_attente'] / $stats['total_lots']) * 100;
                                    $recus_percent = ($stats['lots_recus'] / $stats['total_lots']) * 100;
                                    $traites_percent = ($stats['lots_traites'] / $stats['total_lots']) * 100;
                                ?>
                                <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo $en_attente_percent; ?>%;" aria-valuenow="<?php echo $en_attente_percent; ?>" aria-valuemin="0" aria-valuemax="100" title="En attente">
                                    <?php echo round($en_attente_percent); ?>%
                                </div>
                                <div class="progress-bar bg-info" role="progressbar" style="width: <?php echo $recus_percent; ?>%;" aria-valuenow="<?php echo $recus_percent; ?>" aria-valuemin="0" aria-valuemax="100" title="Reçus">
                                    <?php echo round($recus_percent); ?>%
                                </div>
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $traites_percent; ?>%;" aria-valuenow="<?php echo $traites_percent; ?>" aria-valuemin="0" aria-valuemax="100" title="Traités">
                                    <?php echo round($traites_percent); ?>%
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-warning">En attente</small>
                                <small class="text-info">Reçus</small>
                                <small class="text-success">Traités</small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer bg-light">
                    <a href="index.php?page=lots" class="btn btn-sm btn-primary">
                        <i class="fas fa-list"></i> Voir tous les lots
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Statistiques des dossiers -->
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-folder-open"></i> Statistiques des dossiers</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <div>Dossiers reçus:</div>
                        <div><strong><?php echo $stats['dossiers_recus']; ?></strong></div>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <div>Dossiers en attente:</div>
                        <div><strong><?php echo $stats['dossiers_en_attente']; ?></strong></div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <div>Total:</div>
                        <div><strong><?php echo $stats['total_dossiers']; ?></strong></div>
                    </div>
                    
                    <?php if ($stats['total_dossiers'] > 0): ?>
                        <div class="mt-3">
                            <div class="progress mb-2" style="height: 20px;">
                                <?php 
                                    $recus_percent = ($stats['dossiers_recus'] / $stats['total_dossiers']) * 100;
                                    $en_attente_percent = ($stats['dossiers_en_attente'] / $stats['total_dossiers']) * 100;
                                ?>
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $recus_percent; ?>%;" aria-valuenow="<?php echo $recus_percent; ?>" aria-valuemin="0" aria-valuemax="100" title="Reçus">
                                    <?php echo round($recus_percent); ?>%
                                </div>
                                <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo $en_attente_percent; ?>%;" aria-valuenow="<?php echo $en_attente_percent; ?>" aria-valuemin="0" aria-valuemax="100" title="En attente">
                                    <?php echo round($en_attente_percent); ?>%
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-success">Reçus</small>
                                <small class="text-warning">En attente</small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer bg-light">
                    <a href="index.php?page=lots/search" class="btn btn-sm btn-success">
                        <i class="fas fa-search"></i> Rechercher des dossiers
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Statistiques des retours -->
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-undo-alt"></i> Statistiques des retours</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <div>Dossiers corrigés:</div>
                        <div><strong><?php echo $stats['retours_corriges']; ?></strong></div>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <div>Dossiers non corrigés:</div>
                        <div><strong><?php echo $stats['retours_non_corriges']; ?></strong></div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <div>Total:</div>
                        <div><strong><?php echo $stats['total_retours']; ?></strong></div>
                    </div>
                    
                    <?php if ($stats['total_retours'] > 0): ?>
                        <div class="mt-3">
                            <div class="progress mb-2" style="height: 20px;">
                                <?php 
                                    $corriges_percent = ($stats['retours_corriges'] / $stats['total_retours']) * 100;
                                    $non_corriges_percent = ($stats['retours_non_corriges'] / $stats['total_retours']) * 100;
                                ?>
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $corriges_percent; ?>%;" aria-valuenow="<?php echo $corriges_percent; ?>" aria-valuemin="0" aria-valuemax="100" title="Corrigés">
                                    <?php echo round($corriges_percent); ?>%
                                </div>
                                <div class="progress-bar bg-danger" role="progressbar" style="width: <?php echo $non_corriges_percent; ?>%;" aria-valuenow="<?php echo $non_corriges_percent; ?>" aria-valuemin="0" aria-valuemax="100" title="Non corrigés">
                                    <?php echo round($non_corriges_percent); ?>%
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-success">Corrigés</small>
                                <small class="text-danger">Non corrigés</small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer bg-light">
                    <a href="index.php?page=retours" class="btn btn-sm btn-danger">
                        <i class="fas fa-list"></i> Voir tous les retours
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Graphiques -->
    <div class="row mb-4">
        <!-- Activité par CDM -->
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Activité par CDM</h5>
                </div>
                <div class="card-body">
                    <?php if (count($cdm_activity) > 0): ?>
                        <canvas id="cdmActivityChart" class="dashboard-chart"></canvas>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Aucune donnée disponible.
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer bg-light">
                    <a href="index.php?page=reports/cdm" class="btn btn-sm btn-primary">
                        <i class="fas fa-chart-line"></i> Voir l'analyse complète
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Évolution mensuelle des lots -->
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Évolution mensuelle des lots</h5>
                </div>
                <div class="card-body">
                    <?php if (count($evolution_lots) > 0): ?>
                        <canvas id="evolutionChart" class="dashboard-chart"></canvas>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Aucune donnée disponible.
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer bg-light">
                    <a href="index.php?page=reports/actes" class="btn btn-sm btn-success">
                        <i class="fas fa-chart-pie"></i> Voir l'analyse des actes
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Dernières activités -->
    <div class="row">
        <!-- Lots récents -->
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-layer-group"></i> Lots récents</h5>
                </div>
                <div class="card-body">
                    <?php if (count($lots_recents) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>N° Lot</th>
                                        <th>CDM</th>
                                        <th>Date demande</th>
                                        <th>Statut</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($lots_recents as $lot): ?>
                                        <tr>
                                            <td><?php echo $lot['numero_lot']; ?></td>
                                            <td><?php echo $lot['cdm_nom']; ?></td>
                                            <td><?php echo formatDate($lot['date_demande']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $lot['statut'] === 'en_attente' ? 'warning' : 
                                                        ($lot['statut'] === 'recu' ? 'info' : 
                                                        ($lot['statut'] === 'traite' ? 'success' : 'secondary')); 
                                                ?>">
                                                    <?php 
                                                        echo $lot['statut'] === 'en_attente' ? 'En attente' : 
                                                            ($lot['statut'] === 'recu' ? 'Reçu' : 
                                                            ($lot['statut'] === 'traite' ? 'Traité' : 'Archivé')); 
                                                    ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <a href="index.php?page=lots/view&id=<?php echo $lot['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Aucun lot récent.
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer bg-light">
                    <a href="index.php?page=lots" class="btn btn-sm btn-primary">
                        <i class="fas fa-list"></i> Voir tous les lots
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Dossiers retournés récents -->
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-undo-alt"></i> Dossiers retournés récents</h5>
                </div>
                <div class="card-body">
                    <?php if (count($retours_recents) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>N° Dossier</th>
                                        <th>CDM</th>
                                        <th>Raison</th>
                                        <th>Statut</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($retours_recents as $retour): ?>
                                        <tr>
                                            <td><?php echo $retour['numero_dossier']; ?></td>
                                            <td><?php echo $retour['cdm_nom']; ?></td>
                                            <td><?php echo $retour['raison_retour_nom']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $retour['corrige'] ? 'success' : 'danger'; ?>">
                                                    <?php echo $retour['corrige'] ? 'Corrigé' : 'Non corrigé'; ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <a href="index.php?page=retours/view&numero=<?php echo $retour['numero_bordereau']; ?>&cdm_id=<?php echo $retour['cdm_id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Aucun dossier retourné récent.
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer bg-light">
                    <a href="index.php?page=retours" class="btn btn-sm btn-danger">
                        <i class="fas fa-list"></i> Voir tous les retours
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Actualiser le tableau de bord
    document.getElementById('refreshDashboard').addEventListener('click', function() {
        window.location.reload();
    });
    
    // Graphique d'activité par CDM
    <?php if (count($cdm_activity) > 0): ?>
        var cdmActivityCtx = document.getElementById('cdmActivityChart').getContext('2d');
        var cdmActivityChart = new Chart(cdmActivityCtx, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode($cdm_labels); ?>,
                datasets: [{
                    label: 'Nombre de lots',
                    data: <?php echo json_encode($cdm_data); ?>,
                    backgroundColor: 'rgba(13, 110, 253, 0.7)',
                    borderColor: 'rgba(13, 110, 253, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    <?php endif; ?>
    
    // Graphique d'évolution mensuelle des lots
    <?php if (count($evolution_lots) > 0): ?>
        var evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
        var evolutionChart = new Chart(evolutionCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($evolution_labels); ?>,
                datasets: [{
                    label: 'Nombre de lots',
                    data: <?php echo json_encode($evolution_data); ?>,
                    backgroundColor: 'rgba(25, 135, 84, 0.2)',
                    borderColor: 'rgba(25, 135, 84, 1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    <?php endif; ?>
});
</script>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
