<?php
// Vue pour la gestion de la base de données
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-database"></i> Gestion de la base de données</h1>
        <div>
            <form method="post" action="index.php?page=admin/database" class="d-inline">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                <input type="hidden" name="action" value="backup">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-download"></i> Créer une sauvegarde
                </button>
            </form>
        </div>
    </div>
    
    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
    <?php endif; ?>
    
    <!-- Informations sur la base de données -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Informations générales</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <div>Nom de la base :</div>
                        <div><strong><?php echo DB_NAME; ?></strong></div>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <div>Taille totale :</div>
                        <div><strong><?php echo $db_info['size']; ?> MB</strong></div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <div>Nombre de tables :</div>
                        <div><strong><?php echo $db_info['tables']; ?></strong></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8 mb-3">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-download"></i> Sauvegardes disponibles</h5>
                </div>
                <div class="card-body">
                    <?php if (count($backups) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>Nom du fichier</th>
                                        <th>Taille</th>
                                        <th>Date de création</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($backups as $backup): ?>
                                        <tr>
                                            <td><?php echo $backup['name']; ?></td>
                                            <td><?php echo $backup['size']; ?> MB</td>
                                            <td><?php echo formatDate($backup['date'], DATETIME_FORMAT); ?></td>
                                            <td class="text-end">
                                                <a href="backups/<?php echo $backup['name']; ?>" class="btn btn-sm btn-primary btn-action" download data-bs-toggle="tooltip" title="Télécharger">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Aucune sauvegarde disponible.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Liste des tables -->
    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-table"></i> Tables de la base de données</h5>
                <span class="badge bg-primary"><?php echo count($tables); ?> table(s)</span>
            </div>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=admin/database">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                <input type="hidden" name="action" value="optimize">
                
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll">
                                        <label class="form-check-label" for="selectAll">Tout</label>
                                    </div>
                                </th>
                                <th>Nom de la table</th>
                                <th>Taille</th>
                                <th>Lignes</th>
                                <th>Création</th>
                                <th>Dernière mise à jour</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tables as $table): ?>
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input table-checkbox" type="checkbox" name="tables[]" value="<?php echo $table['table']; ?>" id="table_<?php echo $table['table']; ?>">
                                            <label class="form-check-label" for="table_<?php echo $table['table']; ?>"></label>
                                        </div>
                                    </td>
                                    <td><?php echo $table['table']; ?></td>
                                    <td><?php echo $table['size_mb']; ?> MB</td>
                                    <td><?php echo $table['rows']; ?></td>
                                    <td><?php echo formatDate($table['created'], DATETIME_FORMAT); ?></td>
                                    <td><?php echo $table['updated'] ? formatDate($table['updated'], DATETIME_FORMAT) : 'N/A'; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-tools"></i> Optimiser les tables sélectionnées
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sélectionner/désélectionner toutes les tables
    document.getElementById('selectAll').addEventListener('change', function() {
        var checkboxes = document.querySelectorAll('.table-checkbox');
        checkboxes.forEach(function(checkbox) {
            checkbox.checked = document.getElementById('selectAll').checked;
        });
    });
});
</script>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
