<?php
/**
 * Script de maintenance pour corriger la base de données
 * Accessible sans connexion pour les cas d'urgence
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';

// Fonction simple pour nettoyer les entrées
function clean_input($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// Vérification de sécurité simple
$maintenance_key = 'LOT2024MAINTENANCE';
$provided_key = $_GET['key'] ?? '';

if ($provided_key !== $maintenance_key) {
    die('
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <title>Maintenance - Accès Sécurisé</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; background: #f5f5f5; }
            .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
            h2 { color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 10px; }
            .form-group { margin: 20px 0; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input[type="text"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .info { background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h2>🔧 Maintenance de la Base de Données</h2>
            <div class="info">
                <strong>Accès Sécurisé Requis</strong><br>
                Veuillez entrer la clé de maintenance pour accéder aux outils de réparation.
            </div>
            <form method="get">
                <div class="form-group">
                    <label for="key">Clé de Maintenance:</label>
                    <input type="text" id="key" name="key" placeholder="Entrez la clé de maintenance" required>
                </div>
                <button type="submit">Accéder aux Outils</button>
            </form>
            <div class="info">
                <strong>Actions Disponibles:</strong><br>
                • Correction de la structure de la base de données<br>
                • Création des tables manquantes<br>
                • Migration des données existantes<br>
                • Nettoyage des données orphelines<br>
                • Réinitialisation complète (optionnel)
            </div>
        </div>
    </body>
    </html>
    ');
}

// Action à effectuer
$action = $_GET['action'] ?? 'menu';

try {
    // Obtenir la connexion à la base de données
    $db = Database::getInstance();
    
    if ($action === 'fix') {
        // Correction de la base de données
        echo "<h2>🔧 Correction de la Base de Données</h2>\n";
        echo "<pre>\n";
        
        // 1. Créer la table bordereaux_retour si elle n'existe pas
        echo "1. Vérification de la table bordereaux_retour...\n";
        $sql = "SHOW TABLES LIKE 'bordereaux_retour'";
        $result = $db->query($sql);
        
        if ($result->rowCount() === 0) {
            echo "   ⚠️ Table bordereaux_retour manquante, création en cours...\n";
            $sql = "CREATE TABLE bordereaux_retour (
                id INT AUTO_INCREMENT PRIMARY KEY,
                numero INT NOT NULL,
                cdm_id INT NOT NULL,
                date_bordereau DATE NOT NULL,
                notes TEXT NULL,
                created_by INT NOT NULL DEFAULT 1,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_bordereau (numero, cdm_id),
                INDEX idx_cdm (cdm_id),
                INDEX idx_numero (numero)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $db->query($sql);
            echo "   ✅ Table bordereaux_retour créée\n";
            
            // Migrer les données existantes
            echo "   📦 Migration des données...\n";
            $sql = "INSERT IGNORE INTO bordereaux_retour (numero, cdm_id, date_bordereau, created_by, created_at)
                    SELECT DISTINCT numero_bordereau, cdm_id, date_bordereau, 
                           COALESCE(created_by, 1), COALESCE(created_at, NOW())
                    FROM dossiers_retournes
                    WHERE numero_bordereau IS NOT NULL AND cdm_id IS NOT NULL";
            $db->query($sql);
            echo "   ✅ Données migrées\n";
        } else {
            echo "   ✅ Table bordereaux_retour existe\n";
        }
        
        // 2. Créer la table dossier_raisons si elle n'existe pas
        echo "\n2. Vérification de la table dossier_raisons...\n";
        $sql = "SHOW TABLES LIKE 'dossier_raisons'";
        $result = $db->query($sql);
        
        if ($result->rowCount() === 0) {
            echo "   ⚠️ Table dossier_raisons manquante, création en cours...\n";
            $sql = "CREATE TABLE dossier_raisons (
                id INT AUTO_INCREMENT PRIMARY KEY,
                dossier_id INT NOT NULL,
                raison_id INT NOT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_dossier (dossier_id),
                INDEX idx_raison (raison_id),
                UNIQUE KEY unique_dossier_raison (dossier_id, raison_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $db->query($sql);
            echo "   ✅ Table dossier_raisons créée\n";
            
            // Migrer les raisons existantes
            echo "   📦 Migration des raisons...\n";
            $sql = "INSERT IGNORE INTO dossier_raisons (dossier_id, raison_id, created_at)
                    SELECT id, raison_retour_id, COALESCE(created_at, NOW())
                    FROM dossiers_retournes
                    WHERE raison_retour_id IS NOT NULL";
            $db->query($sql);
            echo "   ✅ Raisons migrées\n";
        } else {
            echo "   ✅ Table dossier_raisons existe\n";
        }
        
        // 3. Vérifier et créer l'utilisateur admin par défaut
        echo "\n3. Vérification de l'utilisateur administrateur...\n";
        $sql = "SELECT COUNT(*) as count FROM users WHERE role = 'admin'";
        $result = $db->single($sql);
        if ($result['count'] == 0) {
            echo "   📦 Création de l'utilisateur admin par défaut...\n";
            $sql = "INSERT INTO users (username, password, email, nom, prenom, role, active, created_at) VALUES
                    ('admin', '$2y$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Administrateur', 'Système', 'admin', 1, NOW())";
            $db->query($sql);
            echo "   ✅ Utilisateur admin créé (login: admin, password: admin123)\n";
        } else {
            echo "   ✅ Utilisateur admin existant\n";
        }

        // 4. Vérifier et ajouter des données de base si nécessaire
        echo "\n4. Vérification des données de base...\n";

        // Vérifier les CDM
        $sql = "SELECT COUNT(*) as count FROM cdm";
        $result = $db->single($sql);
        if ($result['count'] == 0) {
            echo "   📦 Ajout de CDM d'exemple...\n";
            $sql = "INSERT INTO cdm (nom, adresse, telephone, email, contact_nom, active) VALUES
                    ('CDM Centre-Ville', '123 Avenue Mohammed V, Casablanca', '0522-123456', '<EMAIL>', 'Dr. Ahmed Benali', 1),
                    ('CDM Maarif', '456 Boulevard Zerktouni, Casablanca', '0522-234567', '<EMAIL>', 'Dr. Fatima Alami', 1)";
            $db->query($sql);
            echo "   ✅ CDM ajoutés\n";
        } else {
            echo "   ✅ CDM existants: " . $result['count'] . "\n";
        }
        
        // Vérifier les raisons de retour
        $sql = "SELECT COUNT(*) as count FROM raisons_retour";
        $result = $db->single($sql);
        if ($result['count'] == 0) {
            echo "   📦 Ajout de raisons de retour...\n";
            $sql = "INSERT INTO raisons_retour (raison, description) VALUES
                    ('Document manquant', 'Un ou plusieurs documents requis sont manquants'),
                    ('Signature manquante', 'La signature du patient ou du médecin est manquante'),
                    ('Informations incomplètes', 'Les informations fournies sont incomplètes')";
            $db->query($sql);
            echo "   ✅ Raisons de retour ajoutées\n";
        } else {
            echo "   ✅ Raisons de retour existantes: " . $result['count'] . "\n";
        }
        
        echo "\n🎉 Correction terminée avec succès!\n";
        echo "</pre>\n";
        
    } elseif ($action === 'reset') {
        // Réinitialisation complète
        echo "<h2>🔄 Réinitialisation Complète</h2>\n";
        echo "<pre>\n";
        echo "⚠️ Cette action va supprimer toutes les données sauf les utilisateurs!\n\n";
        
        // Confirmation requise
        if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
            echo "Pour confirmer, ajoutez &confirm=yes à l'URL\n";
            echo "</pre>\n";
        } else {
            // Exécuter la réinitialisation
            $sql_file = 'sql/reset_database.sql';
            if (file_exists($sql_file)) {
                $sql_content = file_get_contents($sql_file);
                $queries = explode(';', $sql_content);
                
                foreach ($queries as $query) {
                    $query = trim($query);
                    if (!empty($query) && strpos($query, '--') !== 0) {
                        try {
                            $db->query($query);
                            echo "✅ " . substr($query, 0, 50) . "...\n";
                        } catch (Exception $e) {
                            echo "❌ Erreur: " . $e->getMessage() . "\n";
                        }
                    }
                }
                echo "\n🎉 Réinitialisation terminée!\n";
            } else {
                echo "❌ Fichier SQL introuvable: $sql_file\n";
            }
            echo "</pre>\n";
        }
        
    } else {
        // Menu principal
        echo '
        <!DOCTYPE html>
        <html lang="fr">
        <head>
            <meta charset="UTF-8">
            <title>Maintenance - Base de Données</title>
            <style>
                body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
                .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
                h2 { color: #28a745; border-bottom: 2px solid #28a745; padding-bottom: 10px; }
                .action-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 15px 0; }
                .action-card h3 { margin-top: 0; color: #495057; }
                .btn { display: inline-block; padding: 12px 24px; margin: 10px 5px 0 0; text-decoration: none; border-radius: 5px; font-weight: bold; }
                .btn-primary { background: #007bff; color: white; }
                .btn-warning { background: #ffc107; color: #212529; }
                .btn-danger { background: #dc3545; color: white; }
                .btn:hover { opacity: 0.8; }
                .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h2>🔧 Outils de Maintenance</h2>
                
                <div class="action-card">
                    <h3>🛠️ Correction de la Base de Données</h3>
                    <p>Corrige les problèmes de structure sans supprimer les données existantes:</p>
                    <ul>
                        <li>Crée les tables manquantes</li>
                        <li>Migre les données existantes</li>
                        <li>Ajoute les données de base si nécessaire</li>
                    </ul>
                    <a href="?key=' . $maintenance_key . '&action=fix" class="btn btn-primary">Corriger la Base</a>
                </div>
                
                <div class="action-card">
                    <h3>🔄 Réinitialisation Complète</h3>
                    <div class="warning">
                        <strong>⚠️ Attention:</strong> Cette action supprime toutes les données sauf les utilisateurs!
                    </div>
                    <p>Remet la base de données à zéro avec des données d\'exemple:</p>
                    <ul>
                        <li>Supprime toutes les données (sauf utilisateurs)</li>
                        <li>Recrée la structure complète</li>
                        <li>Ajoute des données d\'exemple</li>
                    </ul>
                    <a href="?key=' . $maintenance_key . '&action=reset" class="btn btn-warning">Voir les Options</a>
                    <a href="?key=' . $maintenance_key . '&action=reset&confirm=yes" class="btn btn-danger" onclick="return confirm(\'Êtes-vous sûr de vouloir réinitialiser complètement la base de données?\')">Réinitialiser Maintenant</a>
                </div>
                
                <div class="action-card">
                    <h3>📊 Informations</h3>
                    <p><strong>Clé de maintenance:</strong> ' . $maintenance_key . '</p>
                    <p><strong>URL d\'accès:</strong> maintenance.php?key=' . $maintenance_key . '</p>
                </div>
            </div>
        </body>
        </html>';
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Erreur</h2>\n";
    echo "<pre>Erreur: " . $e->getMessage() . "</pre>\n";
}
?>
