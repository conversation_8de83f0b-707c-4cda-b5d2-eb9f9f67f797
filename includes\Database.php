<?php
/**
 * Classe Database
 * 
 * Cette classe gère les connexions à la base de données
 * et fournit des méthodes pour exécuter des requêtes.
 */
class Database {
    /**
     * Instance unique de la classe (pattern Singleton)
     * 
     * @var Database
     */
    private static $instance = null;
    
    /**
     * Objet PDO pour la connexion à la base de données
     * 
     * @var PDO
     */
    private $pdo;
    
    /**
     * Constructeur privé (pattern Singleton)
     */
    private function __construct() {
        $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=' . DB_CHARSET;
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ];
        
        try {
            $this->pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            throw new Exception('Erreur de connexion à la base de données: ' . $e->getMessage());
        }
    }
    
    /**
     * Empêche le clonage de l'instance (pattern Singleton)
     */
    private function __clone() {}
    
    /**
     * Récupère l'instance unique de la classe (pattern Singleton)
     * 
     * @return Database
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    /**
     * Exécute une requête SQL
     * 
     * @param string $sql Requête SQL
     * @param array $params Paramètres de la requête
     * @return PDOStatement
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception('Erreur SQL: ' . $e->getMessage());
        }
    }
    
    /**
     * Récupère une seule ligne de résultat
     * 
     * @param string $sql Requête SQL
     * @param array $params Paramètres de la requête
     * @return array|null
     */
    public function single($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Récupère toutes les lignes de résultat
     * 
     * @param string $sql Requête SQL
     * @param array $params Paramètres de la requête
     * @return array
     */
    public function all($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Récupère une seule valeur
     * 
     * @param string $sql Requête SQL
     * @param array $params Paramètres de la requête
     * @return mixed
     */
    public function value($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchColumn();
    }
    
    /**
     * Récupère l'ID de la dernière ligne insérée
     * 
     * @return string
     */
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Démarre une transaction
     * 
     * @return bool
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * Valide une transaction
     * 
     * @return bool
     */
    public function commit() {
        return $this->pdo->commit();
    }
    
    /**
     * Annule une transaction
     *
     * @return bool
     */
    public function rollBack() {
        return $this->pdo->rollBack();
    }


    
    /**
     * Vérifie si une transaction est en cours
     * 
     * @return bool
     */
    public function inTransaction() {
        return $this->pdo->inTransaction();
    }
}
?>
