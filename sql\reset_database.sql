-- Script de réinitialisation de la base de données
-- Supprime toutes les données sauf les utilisateurs

-- Désactiver les vérifications de clés étrangères temporairement
SET FOREIGN_KEY_CHECKS = 0;

-- Sa<PERSON>garder les utilisateurs
CREATE TEMPORARY TABLE temp_users AS SELECT * FROM users;

-- Créer un utilisateur admin par défaut s'il n'existe pas
INSERT IGNORE INTO users (username, password, email, nom, prenom, role, active, created_at) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Administrateur', 'Système', 'admin', 1, NOW());

-- Supprimer toutes les données des tables (sauf users)
TRUNCATE TABLE logs;
TRUNCATE TABLE dossiers_retournes;
TRUNCATE TABLE dossiers;
TRUNCATE TABLE lots;
TRUNCATE TABLE raisons_retour;
TRUNCATE TABLE actes;
TRUNCATE TABLE cdm;

-- Réactiver les vérifications de clés étrangères
SET FOREIGN_KEY_CHECKS = 1;

-- Réinsérer les données par défaut

-- CDM par défaut
INSERT INTO cdm (nom, adresse, telephone, email, contact_nom, active) VALUES
('CDM Centre-Ville', '123 Avenue Mohammed V, Casablanca', '0522-123456', '<EMAIL>', 'Dr. Ahmed Benali', 1),
('CDM Maarif', '456 Boulevard Zerktouni, Casablanca', '0522-234567', '<EMAIL>', 'Dr. Fatima Alami', 1),
('CDM Ain Sebaa', '789 Rue des Palmiers, Ain Sebaa', '0522-345678', '<EMAIL>', 'Dr. Mohamed Tazi', 1),
('CDM Hay Mohammadi', '321 Avenue Hassan II, Hay Mohammadi', '0522-456789', '<EMAIL>', 'Dr. Aicha Bennani', 1),
('CDM Sidi Bernoussi', '654 Rue de la Liberté, Sidi Bernoussi', '0522-567890', '<EMAIL>', 'Dr. Youssef Idrissi', 1);

-- Motifs de retour par défaut
INSERT INTO raisons_retour (raison, description) VALUES
('Document manquant', 'Un ou plusieurs documents requis sont manquants dans le dossier'),
('Signature manquante', 'La signature du patient ou du médecin est manquante'),
('Informations incomplètes', 'Les informations fournies sont incomplètes ou erronées'),
('Erreur d\'identification', 'Erreur dans le numéro d\'adhérent ou les informations d\'identification'),
('Document illisible', 'Un ou plusieurs documents sont illisibles ou de mauvaise qualité'),
('Acte non couvert', 'L\'acte médical n\'est pas couvert par l\'assurance ou nécessite une autorisation'),
('Date expirée', 'La date de validité du document est expirée ou antérieure à la période de couverture'),
('Doublon', 'Le dossier est un doublon d\'un dossier déjà traité ou en cours de traitement'),
('Montant incorrect', 'Le montant facturé ne correspond pas au tarif conventionné'),
('Prescription manquante', 'L\'ordonnance ou la prescription médicale est manquante'),
('Cachet médical absent', 'Le cachet du médecin traitant est absent ou illisible'),
('Formulaire incomplet', 'Le formulaire de remboursement est incomplet ou mal rempli');

-- Actes médicaux par défaut
INSERT INTO actes (code, nom, description) VALUES
('CONS-GEN', 'Consultation générale', 'Consultation médicale générale chez un médecin généraliste'),
('CONS-SPEC', 'Consultation spécialiste', 'Consultation avec un médecin spécialiste'),
('RADIO-STD', 'Radiographie standard', 'Examen radiographique standard (thorax, membres, etc.)'),
('RADIO-CONT', 'Radiographie avec contraste', 'Examen radiographique avec produit de contraste'),
('ECHO-ABD', 'Échographie abdominale', 'Examen échographique de l\'abdomen'),
('ECHO-CARD', 'Échocardiographie', 'Examen échographique du cœur'),
('ECHO-OBST', 'Échographie obstétricale', 'Examen échographique pendant la grossesse'),
('LABO-STD', 'Analyses standard', 'Analyses biologiques de routine (NFS, glycémie, etc.)'),
('LABO-SPEC', 'Analyses spécialisées', 'Analyses biologiques spécialisées (hormones, marqueurs, etc.)'),
('DENT-CONS', 'Consultation dentaire', 'Consultation chez un chirurgien-dentiste'),
('DENT-SOIN', 'Soins dentaires', 'Soins dentaires conservateurs (plombages, détartrage, etc.)'),
('DENT-CHIR', 'Chirurgie dentaire', 'Extractions dentaires et chirurgie buccale'),
('KINE-SEANCE', 'Séance de kinésithérapie', 'Séance individuelle de rééducation fonctionnelle'),
('OPHTA-CONS', 'Consultation ophtalmologique', 'Consultation chez un ophtalmologue'),
('OPHTA-EXAM', 'Examens ophtalmologiques', 'Examens spécialisés (fond d\'œil, champ visuel, etc.)'),
('HOSP-JOUR', 'Hospitalisation de jour', 'Hospitalisation de moins de 24 heures'),
('HOSP-COMP', 'Hospitalisation complète', 'Hospitalisation de plus de 24 heures'),
('CHIR-AMB', 'Chirurgie ambulatoire', 'Intervention chirurgicale en ambulatoire'),
('CHIR-HOSP', 'Chirurgie avec hospitalisation', 'Intervention chirurgicale nécessitant une hospitalisation'),
('URGENCE', 'Soins d\'urgence', 'Soins médicaux d\'urgence'),
('TRANSPORT', 'Transport sanitaire', 'Transport en ambulance ou véhicule sanitaire'),
('PHARMA', 'Médicaments', 'Remboursement de médicaments prescrits'),
('OPTIQUE', 'Optique médicale', 'Lunettes et lentilles de contact sur prescription'),
('PROTHESE', 'Prothèses et orthèses', 'Appareillage médical et prothèses'),
('PSYCHO', 'Consultation psychologique', 'Consultation chez un psychologue ou psychiatre');

-- Créer la table bordereaux_retour si elle n'existe pas
CREATE TABLE IF NOT EXISTS bordereaux_retour (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero INT NOT NULL,
    cdm_id INT NOT NULL,
    date_bordereau DATE NOT NULL,
    notes TEXT NULL,
    created_by INT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_bordereau (numero, cdm_id),
    FOREIGN KEY (cdm_id) REFERENCES cdm(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Créer la table dossier_raisons pour les motifs multiples
CREATE TABLE IF NOT EXISTS dossier_raisons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    dossier_id INT NOT NULL,
    raison_id INT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dossier_id) REFERENCES dossiers_retournes(id) ON DELETE CASCADE,
    FOREIGN KEY (raison_id) REFERENCES raisons_retour(id) ON DELETE CASCADE,
    UNIQUE KEY unique_dossier_raison (dossier_id, raison_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Créer quelques lots d'exemple pour les tests
INSERT INTO lots (numero_lot, cdm_id, date_demande, date_reception, statut, notes, created_by) VALUES
('LOT-2024-001', 1, '2024-01-15', '2024-01-16', 'recu', 'Premier lot de test', 1),
('LOT-2024-002', 2, '2024-01-20', '2024-01-22', 'traite', 'Lot traité avec succès', 1),
('LOT-2024-003', 3, '2024-01-25', NULL, 'en_attente', 'En attente de réception', 1);

-- Créer quelques dossiers d'exemple
INSERT INTO dossiers (lot_id, numero_dossier, numero_adherent, nom, beneficiaire, acte_id, montant, numero_bon, recu, notes) VALUES
(1, 'DOS-2024-001', 'ADH-123456', 'ALAMI Mohammed', 'ALAMI Mohammed', 1, 150.00, 'BON-001', 1, 'Consultation de routine'),
(1, 'DOS-2024-002', 'ADH-234567', 'BENNANI Fatima', 'BENNANI Fatima', 3, 200.00, 'BON-002', 1, 'Radiographie thorax'),
(1, 'DOS-2024-003', 'ADH-345678', 'TAZI Ahmed', 'TAZI Aicha', 5, 300.00, 'BON-003', 0, 'Échographie abdominale'),
(2, 'DOS-2024-004', 'ADH-456789', 'IDRISSI Youssef', 'IDRISSI Youssef', 2, 250.00, 'BON-004', 1, 'Consultation spécialiste'),
(2, 'DOS-2024-005', 'ADH-567890', 'ALAOUI Khadija', 'ALAOUI Khadija', 8, 180.00, 'BON-005', 1, 'Analyses de laboratoire');

-- Créer quelques bordereaux d'exemple
INSERT INTO bordereaux_retour (numero, cdm_id, date_bordereau, notes, created_by) VALUES
(10001, 1, '2024-01-15', 'Premier bordereau de test', 1),
(10002, 2, '2024-01-20', 'Deuxième bordereau de test', 1);

-- Créer quelques dossiers retournés d'exemple
INSERT INTO dossiers_retournes (numero_bordereau, cdm_id, date_bordereau, numero_dossier, numero_adherent, nom, beneficiaire, acte_id, montant, raison_retour_id, date_retour, date_correction, corrige, notes, created_by) VALUES
(10001, 1, '2024-01-15', 'DOS-RET-001', 'ADH-123456', 'ALAMI Mohammed', 'ALAMI Mohammed', 1, 150.00, 1, '2024-01-15', NULL, 0, 'Document manquant', 1),
(10001, 1, '2024-01-15', 'DOS-RET-002', 'ADH-234567', 'BENNANI Fatima', 'BENNANI Fatima', 3, 200.00, 2, '2024-01-15', '2024-01-16', 1, 'Signature ajoutée', 1),
(10002, 2, '2024-01-20', 'DOS-RET-003', 'ADH-345678', 'TAZI Ahmed', 'TAZI Aicha', 5, 300.00, 3, '2024-01-20', NULL, 0, 'Informations à compléter', 1);

-- Message de confirmation
SELECT 'Base de données réinitialisée avec succès. Les utilisateurs ont été conservés.' AS message;
