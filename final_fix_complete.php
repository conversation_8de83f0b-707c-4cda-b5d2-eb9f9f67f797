<?php
/**
 * الإصلاح النهائي الكامل مع معرفات صحيحة
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // تسجيل دخول تلقائي
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🔧 الإصلاح النهائي الكامل</h2>";
    echo "<p>✅ تم تسجيل الدخول كـ: " . $_SESSION['username'] . "</p>";
    
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>📊 1. جلب المعرفات الصحيحة</h3>";
    
    // جلب الأفعال المتاحة
    $sql = "SELECT id, nom, code FROM actes ORDER BY id LIMIT 5";
    $actes = $db->all($sql);
    echo "• أفعال متاحة: " . count($actes) . "<br>";
    foreach ($actes as $acte) {
        echo "  - ID: " . $acte['id'] . " - " . $acte['nom'] . " (" . $acte['code'] . ")<br>";
    }
    
    // جلب الأسباب المتاحة
    $sql = "SELECT id, raison FROM raisons_retour ORDER BY id LIMIT 5";
    $raisons = $db->all($sql);
    echo "• أسباب متاحة: " . count($raisons) . "<br>";
    foreach ($raisons as $raison) {
        echo "  - ID: " . $raison['id'] . " - " . $raison['raison'] . "<br>";
    }
    
    if (count($actes) < 5 || count($raisons) < 5) {
        echo "❌ لا توجد أفعال أو أسباب كافية<br>";
        exit;
    }
    
    echo "<h3>🗑️ 2. تنظيف البيانات الحالية</h3>";
    
    // حذف جميع البيانات الحالية
    $sql = "DELETE dr FROM dossier_raisons dr
            INNER JOIN dossiers_retournes d ON dr.dossier_id = d.id
            WHERE d.numero_bordereau = ? AND d.cdm_id = ?";
    $db->query($sql, [$numero_bordereau, $cdm_id]);
    echo "• تم حذف الأسباب<br>";
    
    $sql = "DELETE FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $db->query($sql, [$numero_bordereau, $cdm_id]);
    echo "• تم حذف الدوسيهات<br>";
    
    echo "<h3>📦 3. إنشاء دوسيهات جديدة فريدة</h3>";
    
    // إنشاء 5 دوسيهات فريدة تماماً
    $timestamp = date('His');
    $unique_dossiers = [
        [
            'numero_dossier' => 'CLEAN-001-' . $timestamp,
            'numero_adherent' => 'ADH-CLEAN-001',
            'nom' => 'ALAMI Mohammed',
            'beneficiaire' => 'ALAMI Mohammed',
            'acte_id' => $actes[0]['id'],
            'montant' => 150.00,
            'raison_retour_id' => $raisons[0]['id'],
            'notes' => 'Consultation générale - ' . $actes[0]['nom']
        ],
        [
            'numero_dossier' => 'CLEAN-002-' . $timestamp,
            'numero_adherent' => 'ADH-CLEAN-002',
            'nom' => 'BENNANI Fatima',
            'beneficiaire' => 'BENNANI Fatima',
            'acte_id' => $actes[1]['id'],
            'montant' => 200.00,
            'raison_retour_id' => $raisons[1]['id'],
            'notes' => 'Consultation spécialisée - ' . $actes[1]['nom']
        ],
        [
            'numero_dossier' => 'CLEAN-003-' . $timestamp,
            'numero_adherent' => 'ADH-CLEAN-003',
            'nom' => 'TAZI Ahmed',
            'beneficiaire' => 'TAZI Aicha',
            'acte_id' => $actes[2]['id'],
            'montant' => 300.00,
            'raison_retour_id' => $raisons[2]['id'],
            'notes' => 'Radiographie - ' . $actes[2]['nom']
        ],
        [
            'numero_dossier' => 'CLEAN-004-' . $timestamp,
            'numero_adherent' => 'ADH-CLEAN-004',
            'nom' => 'IDRISSI Youssef',
            'beneficiaire' => 'IDRISSI Youssef',
            'acte_id' => $actes[3]['id'],
            'montant' => 120.00,
            'raison_retour_id' => $raisons[3]['id'],
            'notes' => 'Échographie - ' . $actes[3]['nom']
        ],
        [
            'numero_dossier' => 'CLEAN-005-' . $timestamp,
            'numero_adherent' => 'ADH-CLEAN-005',
            'nom' => 'BENNANI Aicha',
            'beneficiaire' => 'BENNANI Aicha',
            'acte_id' => $actes[4]['id'],
            'montant' => 180.00,
            'raison_retour_id' => $raisons[4]['id'],
            'notes' => 'Soins dentaires - ' . $actes[4]['nom']
        ]
    ];
    
    $new_dossier_ids = [];
    foreach ($unique_dossiers as $index => $dossier) {
        echo "<h4>إنشاء دوسيه " . ($index + 1) . ":</h4>";
        
        $sql = "INSERT INTO dossiers_retournes (numero_bordereau, cdm_id, date_bordereau, numero_dossier,
                                              numero_adherent, nom, beneficiaire, acte_id, montant,
                                              raison_retour_id, date_retour, corrige, notes, created_by, created_at)
                VALUES (?, ?, '2024-01-15', ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 1, NOW())";
        
        $params = [
            $numero_bordereau,
            $cdm_id,
            $dossier['numero_dossier'],
            $dossier['numero_adherent'],
            $dossier['nom'],
            $dossier['beneficiaire'],
            $dossier['acte_id'],
            $dossier['montant'],
            $dossier['raison_retour_id'],
            date('Y-m-d'),
            $dossier['notes']
        ];
        
        $db->query($sql, $params);
        $dossier_id = $db->lastInsertId();
        $new_dossier_ids[] = $dossier_id;
        
        echo "• ✅ " . $dossier['numero_dossier'] . " (ID: $dossier_id)<br>";
        echo "• فعل: " . $actes[$index]['nom'] . " (ID: " . $actes[$index]['id'] . ")<br>";
        echo "• سبب: " . $raisons[$index]['raison'] . " (ID: " . $raisons[$index]['id'] . ")<br>";
        
        // إضافة سبب واحد فقط لكل دوسيه
        $sql = "INSERT INTO dossier_raisons (dossier_id, raison_id, created_at) VALUES (?, ?, NOW())";
        $db->query($sql, [$dossier_id, $dossier['raison_retour_id']]);
        echo "• تم إضافة السبب في dossier_raisons<br><br>";
    }
    
    echo "<h3>✅ 4. التحقق النهائي</h3>";
    
    // فحص البيانات النهائية
    $sql = "SELECT id, numero_dossier, numero_adherent, nom FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            ORDER BY id ASC";
    $final_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• عدد الدوسيهات النهائية: " . count($final_dossiers) . "<br>";
    
    echo "<h4>📋 الدوسيهات النهائية:</h4>";
    foreach ($final_dossiers as $index => $dossier) {
        echo ($index + 1) . ". ID: " . $dossier['id'] . " - " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - " . $dossier['nom'] . "<br>";
    }
    
    // فحص التكرارات
    $final_unique = [];
    $final_duplicates = [];
    foreach ($final_dossiers as $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($final_unique[$key])) {
            $final_duplicates[] = $key;
        } else {
            $final_unique[$key] = true;
        }
    }
    
    if (empty($final_duplicates)) {
        echo "✅ <strong>لا توجد تكرارات في البيانات النهائية</strong><br>";
    } else {
        echo "❌ <strong>لا تزال هناك تكرارات:</strong><br>";
        foreach ($final_duplicates as $dup) {
            echo "• $dup<br>";
        }
    }
    
    echo "<h3>🧪 5. اختبار صفحة الطباعة الكامل</h3>";
    
    // تنفيذ نفس الكود الموجود في print.php بالضبط
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $test_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• دوسيهات مُسترجعة للاختبار: " . count($test_dossiers) . "<br>";
    
    // إضافة الأسباب (نفس الكود في print.php)
    foreach ($test_dossiers as &$dossier) {
        $sql = "SELECT dr.*, r.raison as raison
                FROM dossier_raisons dr
                JOIN raisons_retour r ON dr.raison_id = r.id
                WHERE dr.dossier_id = ?
                ORDER BY dr.created_at ASC";
        $raisons_test = $db->all($sql, [$dossier['id']]);
        
        if (empty($raisons_test) && !empty($dossier['raison_retour_nom'])) {
            $dossier['raisons'] = [['raison' => $dossier['raison_retour_nom']]];
        } else {
            $dossier['raisons'] = $raisons_test;
        }
        
        $raisons_str = [];
        foreach ($dossier['raisons'] as $raison) {
            $raisons_str[] = $raison['raison'];
        }
        $dossier['raisons_str'] = implode(', ', $raisons_str);
    }
    
    echo "<h4>📋 نتيجة اختبار الطباعة النهائي:</h4>";
    $test_unique = [];
    $test_has_duplicates = false;
    
    foreach ($test_dossiers as $index => $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($test_unique[$key])) {
            $test_has_duplicates = true;
            echo "<span style='color: red; font-weight: bold;'>❌ " . ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " (تكرار)</span><br>";
        } else {
            $test_unique[$key] = true;
            echo "<span style='color: green;'>✅ " . ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - " . $dossier['nom'] . " - " . $dossier['raisons_str'] . "</span><br>";
        }
    }
    
    if (!$test_has_duplicates) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>🎉 تم الإصلاح بنجاح كامل!</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ تم إنشاء " . count($final_dossiers) . " دوسيه فريد تماماً</li>";
        echo "<li>✅ لا توجد تكرارات في قاعدة البيانات</li>";
        echo "<li>✅ لا توجد تكرارات في اختبار الطباعة</li>";
        echo "<li>✅ جميع الأسباب صحيحة ومرتبطة</li>";
        echo "<li>✅ صفحة الطباعة ستعمل بشكل مثالي</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24; margin-top: 0;'>❌ لا تزال هناك مشكلة</h4>";
        echo "<p style='color: #721c24;'>توجد تكرارات في اختبار الطباعة.</p>";
        echo "</div>";
    }
    
    echo "<h3>🔗 اختبار النتائج النهائية:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank' style='color: #28a745; font-weight: bold; font-size: 16px;'>🖨️ صفحة الطباعة النهائية</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank' style='color: #17a2b8; font-weight: bold;'>📝 صفحة التحرير</a></li>";
    echo "<li><a href='index.php?page=retours/export&numero_bordereau=10001&cdm_id=1' target='_blank' style='color: #ffc107; font-weight: bold;'>📊 تصدير Excel</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>التتبع: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإصلاح النهائي الكامل - LOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
