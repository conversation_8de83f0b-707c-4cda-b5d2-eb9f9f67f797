<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .install-container {
            max-width: 800px;
            margin: 50px auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            color: #0d6efd;
        }
        .step-indicator {
            display: flex;
            margin-bottom: 30px;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 10px;
            position: relative;
        }
        .step.active {
            font-weight: bold;
            color: #0d6efd;
        }
        .step:not(:last-child):after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0;
            width: 100%;
            height: 2px;
            background: #dee2e6;
            z-index: -1;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            border-radius: 50%;
            background: #dee2e6;
            margin-right: 5px;
        }
        .step.active .step-number {
            background: #0d6efd;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="logo">
                <h1><?php echo APP_NAME; ?></h1>
                <p class="text-muted">Version <?php echo APP_VERSION; ?></p>
            </div>
            
            <div class="step-indicator">
                <div class="step active">
                    <span class="step-number">1</span>
                    <span class="step-text">Configuration</span>
                </div>
                <div class="step">
                    <span class="step-number">2</span>
                    <span class="step-text">Installation</span>
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    <span class="step-text">Finalisation</span>
                </div>
            </div>
            
            <?php displayFlashMessage(); ?>
            
            <?php if (!empty($install_errors)): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle"></i> Erreurs:</h5>
                    <ul>
                        <?php foreach ($install_errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form method="post" action="index.php?page=install">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-database"></i> Configuration de la base de données</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="db_host" class="form-label">Hôte</label>
                                <input type="text" class="form-control" id="db_host" name="db_host" value="<?php echo $install_data['db_host']; ?>" required>
                                <div class="form-text">Généralement "localhost"</div>
                            </div>
                            <div class="col-md-6">
                                <label for="db_name" class="form-label">Nom de la base de données</label>
                                <input type="text" class="form-control" id="db_name" name="db_name" value="<?php echo $install_data['db_name']; ?>" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="db_user" class="form-label">Utilisateur</label>
                                <input type="text" class="form-control" id="db_user" name="db_user" value="<?php echo $install_data['db_user']; ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="db_pass" class="form-label">Mot de passe</label>
                                <input type="password" class="form-control" id="db_pass" name="db_pass">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-user-shield"></i> Compte administrateur</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="admin_username" class="form-label">Nom d'utilisateur</label>
                                <input type="text" class="form-control" id="admin_username" name="admin_username" value="<?php echo $install_data['admin_username']; ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="admin_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="admin_email" name="admin_email" value="<?php echo $install_data['admin_email']; ?>" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="admin_password" class="form-label">Mot de passe</label>
                                <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                            </div>
                            <div class="col-md-6">
                                <label for="admin_password_confirm" class="form-label">Confirmer le mot de passe</label>
                                <input type="password" class="form-control" id="admin_password_confirm" name="admin_password_confirm" required>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-cogs"></i> Installer l'application
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Validation du formulaire
        document.querySelector('form').addEventListener('submit', function(e) {
            const password = document.getElementById('admin_password').value;
            const confirmPassword = document.getElementById('admin_password_confirm').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Les mots de passe ne correspondent pas.');
            }
        });
    </script>
</body>
</html>
