<?php
// Contrôleur pour l'ajout d'un dossier à un bordereau de retour existant

// Vérifier si la méthode est POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    redirect('index.php?page=retours', 'Méthode non autorisée.', 'danger');
}

// Vérifier le jeton CSRF
if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
    redirect('index.php?page=retours', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
}

// Récupérer les paramètres du bordereau
$numero_bordereau = isset($_POST['numero_bordereau']) ? intval($_POST['numero_bordereau']) : 0;
$cdm_id = isset($_POST['cdm_id']) ? intval($_POST['cdm_id']) : 0;
$date_bordereau = isset($_POST['date_bordereau']) ? clean($_POST['date_bordereau']) : '';

// Vérifier si les paramètres sont valides
if ($numero_bordereau <= 0 || $cdm_id <= 0 || empty($date_bordereau)) {
    redirect('index.php?page=retours', 'Paramètres du bordereau invalides.', 'danger');
}

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Vérifier si le bordereau existe
// Nous devons vérifier dans la table des bordereaux, pas dans dossiers_retournes
$sql = "SELECT COUNT(*) as count FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
$result = $db->single($sql, [$numero_bordereau, $cdm_id]);

if ($result['count'] === 0) {
    // Si le bordereau n'existe pas dans la table des bordereaux, vérifions s'il existe des dossiers
    // avec ce numéro de bordereau (pour la rétrocompatibilité)
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $result = $db->single($sql, [$numero_bordereau, $cdm_id]);

    if ($result['count'] === 0) {
        redirect('index.php?page=retours', 'Bordereau non trouvé.', 'danger');
    }
}

// Récupérer et nettoyer les données du formulaire
$numero_dossier = clean($_POST['numero_dossier'] ?? '');
$numero_adherent = clean($_POST['numero_adherent'] ?? '');
$nom = clean($_POST['nom'] ?? '');
$beneficiaire = clean($_POST['beneficiaire'] ?? '');
$acte_id = isset($_POST['acte_id']) ? intval($_POST['acte_id']) : 0;
$montant = isset($_POST['montant']) ? floatval($_POST['montant']) : 0;
$raison_retour_id = isset($_POST['raison_retour_id']) ? intval($_POST['raison_retour_id']) : 0;
$date_retour = clean($_POST['date_retour'] ?? '');
$date_correction = clean($_POST['date_correction'] ?? '');
$corrige = isset($_POST['corrige']) ? intval($_POST['corrige']) : 0;
$notes = clean($_POST['notes'] ?? '');

// Valider les données
$errors = [];

if (empty($numero_dossier)) {
    $errors[] = 'Le numéro de dossier est requis.';
}

if (empty($numero_adherent)) {
    $errors[] = 'Le numéro d\'adhérent est requis.';
}

if (empty($nom)) {
    $errors[] = 'Le nom est requis.';
}

if ($raison_retour_id <= 0) {
    $errors[] = 'La raison du retour est requise.';
}

if (empty($date_retour)) {
    $errors[] = 'La date de retour est requise.';
} else {
    // Convertir la date au format MySQL (YYYY-MM-DD)
    $date_retour = date('Y-m-d', strtotime(str_replace('/', '-', $date_retour)));
}

// Convertir la date de correction si elle est fournie
if (!empty($date_correction)) {
    $date_correction = date('Y-m-d', strtotime(str_replace('/', '-', $date_correction)));
}

// S'il y a des erreurs, rediriger vers la page de visualisation avec les erreurs
if (!empty($errors)) {
    $_SESSION['form_errors'] = $errors;
    $_SESSION['form_data'] = $_POST;
    redirect('index.php?page=retours/view&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Veuillez corriger les erreurs.', 'danger');
}

try {
    // Insérer le dossier dans la base de données
    $sql = "INSERT INTO dossiers_retournes (numero_bordereau, cdm_id, date_bordereau, numero_dossier,
                                          numero_adherent, nom, beneficiaire, acte_id, montant,
                                          raison_retour_id, date_retour, date_correction, corrige,
                                          notes, created_by, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $params = [$numero_bordereau, $cdm_id, $date_bordereau, $numero_dossier, $numero_adherent,
              $nom, $beneficiaire, $acte_id ?: null, $montant, $raison_retour_id,
              $date_retour, $date_correction ?: null, $corrige, $notes, $_SESSION['user_id']];
    $db->query($sql, $params);

    // Journaliser l'action
    logActivity($_SESSION['user_id'], 'Ajout de dossier retourné', "Dossier #$numero_dossier ajouté au bordereau #$numero_bordereau");

    // Rediriger vers la page de visualisation du bordereau
    redirect('index.php?page=retours/view&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Le dossier a été ajouté avec succès.', 'success');

} catch (Exception $e) {
    redirect('index.php?page=retours/view&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Erreur lors de l\'ajout du dossier: ' . $e->getMessage(), 'danger');
}
?>
