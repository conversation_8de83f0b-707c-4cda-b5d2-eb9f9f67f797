<?php
// Script pour ajouter la colonne 'active' à la table 'actes'
require_once 'config/config.php';
require_once 'config/database.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

try {
    // Vérifier si la colonne existe déjà
    $sql = "SHOW COLUMNS FROM actes LIKE 'active'";
    $result = $db->query($sql);
    $column_exists = $result->rowCount() > 0;
    
    if (!$column_exists) {
        // Ajouter la colonne 'active' à la table 'actes'
        $sql = "ALTER TABLE actes ADD COLUMN active TINYINT(1) NOT NULL DEFAULT 1";
        $db->query($sql);
        echo "La colonne 'active' a été ajoutée avec succès à la table 'actes'.";
    } else {
        echo "La colonne 'active' existe déjà dans la table 'actes'.";
    }
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage();
}
?>
