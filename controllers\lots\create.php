<?php
// Contrôleur pour la création d'un nouveau lot

// Titre de la page
$page_title = 'Nouveau lot';
$page = 'lots/create';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=lots/create', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupérer et nettoyer les données du formulaire
    $numero_lot = isset($_POST['numero_lot']) ? clean($_POST['numero_lot']) : '';
    $cdm_id = isset($_POST['cdm_id']) ? intval($_POST['cdm_id']) : 0;
    $date_demande = isset($_POST['date_demande']) ? clean($_POST['date_demande']) : '';
    $notes = isset($_POST['notes']) ? clean($_POST['notes']) : '';

    // Valider les données
    $errors = [];

    if (empty($numero_lot)) {
        $errors[] = 'Le numéro de lot est obligatoire.';
    } else {
        // Vérifier si le numéro de lot existe déjà
        $sql = "SELECT COUNT(*) as count FROM lots WHERE numero_lot = ?";
        $result = $db->single($sql, [$numero_lot]);

        if ($result['count'] > 0) {
            $errors[] = 'Ce numéro de lot existe déjà. Veuillez en choisir un autre.';
        }
    }

    if ($cdm_id <= 0) {
        $errors[] = 'Veuillez sélectionner un CDM.';
    }

    if (empty($date_demande)) {
        $errors[] = 'La date de demande est requise.';
    } else {
        // Convertir la date au format MySQL (YYYY-MM-DD)
        $date_demande = date('Y-m-d', strtotime(str_replace('/', '-', $date_demande)));
    }

    // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = $_POST;
        redirect('index.php?page=lots/create', 'Veuillez corriger les erreurs.', 'danger');
    }

    try {
        // Insérer le lot dans la base de données avec le numéro saisi manuellement
        $sql = "INSERT INTO lots (numero_lot, cdm_id, date_demande, statut, notes, created_by, created_at)
                VALUES (?, ?, ?, 'en_attente', ?, ?, NOW())";
        $params = [$numero_lot, $cdm_id, $date_demande, $notes, $_SESSION['user_id']];
        $db->query($sql, $params);

        // Récupérer l'ID du lot inséré
        $lot_id = $db->lastInsertId();

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Création de lot', "Lot #$numero_lot créé");

        // Rediriger vers la page de détails du lot
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Le lot a été créé avec succès.', 'success');

    } catch (Exception $e) {
        redirect('index.php?page=lots/create', 'Erreur lors de la création du lot: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'numero_lot' => '',
    'cdm_id' => '',
    'date_demande' => date('d/m/Y'),
    'notes' => ''
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Obtenir la liste des CDM
$cdm_list = getCDMList();

// Charger la vue
require_once VIEWS_PATH . 'lots/create.php';
?>
