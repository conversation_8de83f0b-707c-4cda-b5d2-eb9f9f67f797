<?php
// Vue pour la modification d'un dossier retourné
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-edit"></i> Modifier un dossier retourné</h1>
        <div>
            <a href="index.php?page=retours/edit&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour au bordereau
            </a>
        </div>
    </div>

    <?php if (!empty($form_errors)): ?>
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle"></i> Erreurs:</h5>
            <ul class="mb-0">
                <?php foreach ($form_errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-folder"></i> Informations du dossier</h5>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=dossiers/edit_retour&id=<?php echo $dossier_id; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="numero_dossier" class="form-label required">Numéro de dossier</label>
                        <input type="text" class="form-control" id="numero_dossier" name="numero_dossier" value="<?php echo $form_data['numero_dossier']; ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="numero_adherent" class="form-label required">Numéro d'adhérent</label>
                        <input type="text" class="form-control" id="numero_adherent" name="numero_adherent" value="<?php echo $form_data['numero_adherent']; ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="raisons_retour" class="form-label required">Motifs de retour</label>
                        <select class="form-select" id="raisons_retour" name="raisons_retour[]" multiple required>
                            <?php foreach ($raisons_list as $raison): ?>
                                <option value="<?php echo $raison['id']; ?>" <?php echo (isset($form_data['raisons_retour']) && in_array($raison['id'], $form_data['raisons_retour'])) ? 'selected' : ''; ?>>
                                    <?php echo $raison['raison']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Double-cliquez sur un motif pour l'ajouter ou le supprimer</div>
                        <div id="selected-raisons" class="mt-2"></div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="nom" class="form-label required">Nom</label>
                        <input type="text" class="form-control" id="nom" name="nom" value="<?php echo $form_data['nom']; ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="beneficiaire" class="form-label">Bénéficiaire</label>
                        <input type="text" class="form-control" id="beneficiaire" name="beneficiaire" value="<?php echo $form_data['beneficiaire']; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="actes" class="form-label">Actes</label>
                        <div class="input-group mb-2">
                            <select class="form-select" id="actes">
                                <option value="">Sélectionnez un acte</option>
                                <?php foreach ($actes_list as $acte): ?>
                                    <option value="<?php echo $acte['id']; ?>" data-prix="<?php echo isset($acte['prix']) ? $acte['prix'] : '0.00'; ?>">
                                        <?php echo $acte['nom']; ?> <?php if(isset($acte['prix'])): ?>(<?php echo number_format(floatval($acte['prix']), 2, ',', ' '); ?> DH)<?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button class="btn btn-outline-secondary" type="button" id="add-acte">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="btn btn-outline-danger" type="button" id="remove-acte">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <div class="form-text">Double-cliquez sur un acte pour l'ajouter</div>
                        <div id="selected-actes" class="mt-2"></div>
                        <input type="hidden" id="acte_ids" name="acte_ids" value="<?php echo isset($form_data['acte_ids']) && !empty($form_data['acte_ids']) ? $form_data['acte_ids'] : (isset($form_data['acte_id']) ? $form_data['acte_id'] : ''); ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="montant" class="form-label">Montant</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="montant" name="montant" value="<?php echo $form_data['montant']; ?>">
                            <span class="input-group-text">DH</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="date_retour" class="form-label required">Date de retour</label>
                        <input type="text" class="form-control datepicker" id="date_retour" name="date_retour" value="<?php echo $form_data['date_retour']; ?>" required placeholder="jj/mm/aaaa">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="date_correction" class="form-label">Date de correction</label>
                        <input type="text" class="form-control datepicker" id="date_correction" name="date_correction" value="<?php echo $form_data['date_correction']; ?>" placeholder="jj/mm/aaaa">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Statut de correction</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="corrige" id="corrige_non" value="0" <?php echo $form_data['corrige'] == 0 ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="corrige_non">
                                <span class="badge bg-danger">Non corrigé</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="corrige" id="corrige_oui" value="1" <?php echo $form_data['corrige'] == 1 ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="corrige_oui">
                                <span class="badge bg-success">Corrigé</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"><?php echo $form_data['notes']; ?></textarea>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="index.php?page=retours/edit&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Fonction pour mettre à jour l'affichage des motifs sélectionnés
    function updateSelectedRaisons() {
        const select = document.getElementById('raisons_retour');
        const selectedRaisons = document.getElementById('selected-raisons');
        selectedRaisons.innerHTML = '';

        for (let i = 0; i < select.options.length; i++) {
            if (select.options[i].selected) {
                const badge = document.createElement('span');
                badge.className = 'badge bg-primary me-1 mb-1';
                badge.textContent = select.options[i].text;

                const removeBtn = document.createElement('span');
                removeBtn.className = 'ms-1 text-white';
                removeBtn.innerHTML = '&times;';
                removeBtn.style.cursor = 'pointer';
                removeBtn.onclick = function() {
                    select.options[i].selected = false;
                    updateSelectedRaisons();
                };

                badge.appendChild(removeBtn);
                selectedRaisons.appendChild(badge);
            }
        }
    }

    // Initialiser l'affichage des motifs sélectionnés
    updateSelectedRaisons();

    // Mettre à jour l'affichage lorsque la sélection change
    document.getElementById('raisons_retour').addEventListener('change', updateSelectedRaisons);

    // Ajouter un gestionnaire d'événements pour le double-clic sur les options
    document.getElementById('raisons_retour').addEventListener('dblclick', function(e) {
        if (e.target.tagName === 'OPTION') {
            e.target.selected = !e.target.selected;
            updateSelectedRaisons();
        }
    });

    // Gestion des actes multiples
    const actesSelect = document.getElementById('actes');
    const addActeBtn = document.getElementById('add-acte');
    const removeActeBtn = document.getElementById('remove-acte');
    const selectedActesDiv = document.getElementById('selected-actes');
    const acteIdsInput = document.getElementById('acte_ids');
    const montantInput = document.getElementById('montant');

    // Tableau pour stocker les actes sélectionnés
    let selectedActes = [];

    // Si un acte est déjà sélectionné, l'ajouter au tableau
    if (acteIdsInput.value) {
        // Gérer le cas où il y a plusieurs actes (séparés par des virgules)
        const acteIds = acteIdsInput.value.split(',');

        acteIds.forEach(acteId => {
            if (acteId && acteId.trim()) {
                // Vérifier si l'ID est un nombre
                const cleanId = acteId.trim();
                if (!isNaN(cleanId)) {
                    const acteOption = Array.from(actesSelect.options).find(option => option.value === cleanId);
                    if (acteOption) {
                        // Extraire le nom sans le prix
                        let acteName = acteOption.textContent;
                        if (acteName.includes('(')) {
                            acteName = acteName.split('(')[0].trim();
                        }
                        const actePrix = parseFloat(acteOption.getAttribute('data-prix')) || 0;
                        selectedActes.push({
                            id: cleanId,
                            nom: acteName,
                            prix: actePrix
                        });
                    }
                }
            }
        });
    }

    // Fonction pour mettre à jour l'affichage des actes sélectionnés
    function updateSelectedActes() {
        // Vider la div
        selectedActesDiv.innerHTML = '';

        // Mettre à jour le champ caché avec les IDs
        acteIdsInput.value = selectedActes.map(acte => acte.id).join(',');

        // Calculer le montant total
        let totalMontant = 0;

        // Ajouter chaque acte à la div
        selectedActes.forEach(acte => {
            const acteElement = document.createElement('div');
            acteElement.className = 'badge bg-primary me-1 mb-1';
            const prix = parseFloat(acte.prix) || 0;
            acteElement.textContent = acte.nom + ' (' + prix.toFixed(2).replace('.', ',') + ' DH)';
            selectedActesDiv.appendChild(acteElement);

            // Ajouter au montant total
            totalMontant += prix;
        });

        // Mettre à jour le champ montant
        montantInput.value = totalMontant.toFixed(2).replace('.', ',');
    }

    // Ajouter un acte à la sélection
    addActeBtn.addEventListener('click', function() {
        const selectedOption = actesSelect.options[actesSelect.selectedIndex];
        if (selectedOption && selectedOption.value) {
            const acteId = selectedOption.value;
            // Extraire le nom sans le prix
            let acteName = selectedOption.textContent;
            if (acteName.includes('(')) {
                acteName = acteName.split('(')[0].trim();
            }
            const actePrix = parseFloat(selectedOption.getAttribute('data-prix')) || 0;

            // Vérifier si l'acte n'est pas déjà sélectionné
            if (!selectedActes.some(acte => acte.id === acteId)) {
                selectedActes.push({
                    id: acteId,
                    nom: acteName,
                    prix: actePrix
                });
            }

            updateSelectedActes();
        }
    });

    // Supprimer tous les actes sélectionnés
    removeActeBtn.addEventListener('click', function() {
        selectedActes = [];
        updateSelectedActes();
    });

    // Double-clic sur un acte pour l'ajouter directement
    actesSelect.addEventListener('dblclick', function() {
        addActeBtn.click();
    });

    // Initialiser l'affichage des actes sélectionnés
    updateSelectedActes();
});
</script>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
