<?php
// Script pour corriger la structure de la table logs
require_once 'config/config.php';
require_once 'config/database.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

try {
    // Vérifier si la colonne action_type existe déjà
    $sql = "SHOW COLUMNS FROM logs LIKE 'action_type'";
    $result = $db->query($sql);
    
    if ($result->rowCount() === 0) {
        // La colonne n'existe pas, l'ajouter
        $sql = "ALTER TABLE logs ADD COLUMN action_type VARCHAR(50) DEFAULT 'info' AFTER action";
        $db->query($sql);
        echo "<h2>Colonne 'action_type' ajoutée avec succès à la table 'logs'.</h2>";
        
        // Mettre à jour les enregistrements existants
        $sql = "UPDATE logs SET action_type = 'info'";
        $db->query($sql);
        echo "<p>Les enregistrements existants ont été mis à jour.</p>";
    } else {
        echo "<h2>La colonne 'action_type' existe déjà dans la table 'logs'.</h2>";
    }
    
    echo "<p>Problème résolu !</p>";
    echo "<p><a href='index.php?page=admin/logs' class='btn btn-primary'>Retour à la page des journaux</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Erreur:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
