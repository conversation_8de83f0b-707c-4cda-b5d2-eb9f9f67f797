<?php
/**
 * محاكاة صفحة الطباعة بالضبط لتحديد مصدر المشكلة
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // تسجيل دخول تلقائي
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🎭 محاكاة صفحة الطباعة</h2>";
    echo "<p>✅ تم تسجيل الدخول كـ: " . $_SESSION['username'] . "</p>";
    
    // نفس المتغيرات الموجودة في print.php
    $numero_bordereau = 10001;
    $cdm_id = 1;
    $page_title = 'Impression - Bordereau #' . $numero_bordereau;
    
    echo "<h3>🔍 تنفيذ نفس الكود الموجود في controllers/retours/print.php</h3>";
    
    // نفس الكود بالضبط من print.php
    
    // Vérifier si le bordereau existe
    $sql = "SELECT COUNT(*) as count FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
    $result = $db->single($sql, [$numero_bordereau, $cdm_id]);
    
    if ($result['count'] === 0) {
        $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
        $result = $db->single($sql, [$numero_bordereau, $cdm_id]);
        
        if ($result['count'] === 0) {
            echo "❌ Bordereau non trouvé.<br>";
            exit;
        }
    }
    
    // Récupérer la date du bordereau, les notes et les informations de l'utilisateur qui l'a créé
    $sql = "SELECT br.date_bordereau, br.notes, u.username as created_by_username
            FROM bordereaux_retour br
            JOIN users u ON br.created_by = u.id
            WHERE br.numero = ? AND br.cdm_id = ?
            LIMIT 1";
    $bordereau_info = $db->single($sql, [$numero_bordereau, $cdm_id]);
    
    if (!$bordereau_info) {
        $sql = "SELECT dr.date_bordereau, '' as notes, u.username as created_by_username
                FROM dossiers_retournes dr
                JOIN users u ON dr.created_by = u.id
                WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
                LIMIT 1";
        $bordereau_info = $db->single($sql, [$numero_bordereau, $cdm_id]);
    }
    
    // Récupérer les informations du CDM
    $sql = "SELECT * FROM cdm WHERE id = ?";
    $cdm = $db->single($sql, [$cdm_id]);
    
    if (!$cdm) {
        echo "❌ CDM non trouvé.<br>";
        exit;
    }
    
    echo "✅ معلومات البوردرو والـ CDM تم جلبها بنجاح<br>";
    
    // Récupérer les dossiers associés à ce bordereau
    echo "<h4>📊 جلب الدوسيهات (نفس الاستعلام في print.php):</h4>";
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• عدد الدوسيهات المُسترجعة: " . count($dossiers) . "<br>";
    
    // طباعة تفاصيل الدوسيهات قبل معالجة الأسباب
    echo "<h5>📋 الدوسيهات قبل معالجة الأسباب:</h5>";
    foreach ($dossiers as $index => $dossier) {
        echo ($index + 1) . ". ID: " . $dossier['id'] . " - " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . "<br>";
    }
    
    // Récupérer toutes les raisons de retour pour chaque dossier
    echo "<h4>📊 إضافة الأسباب (نفس الكود في print.php):</h4>";
    foreach ($dossiers as &$dossier) {
        $sql = "SELECT dr.*, r.raison as raison
                FROM dossier_raisons dr
                JOIN raisons_retour r ON dr.raison_id = r.id
                WHERE dr.dossier_id = ?
                ORDER BY dr.created_at ASC";
        $raisons = $db->all($sql, [$dossier['id']]);
        
        // Si aucune raison n'est trouvée dans la nouvelle table, utiliser la raison principale
        if (empty($raisons) && !empty($dossier['raison_retour_nom'])) {
            $dossier['raisons'] = [['raison' => $dossier['raison_retour_nom']]];
        } else {
            $dossier['raisons'] = $raisons;
        }
        
        // Créer une chaîne de toutes les raisons pour l'affichage
        $raisons_str = [];
        foreach ($dossier['raisons'] as $raison) {
            $raisons_str[] = $raison['raison'];
        }
        $dossier['raisons_str'] = implode(', ', $raisons_str);
    }
    
    // طباعة تفاصيل الدوسيهات بعد معالجة الأسباب
    echo "<h5>📋 الدوسيهات بعد معالجة الأسباب:</h5>";
    foreach ($dossiers as $index => $dossier) {
        echo ($index + 1) . ". ID: " . $dossier['id'] . " - " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - أسباب: " . $dossier['raisons_str'] . "<br>";
    }
    
    // Statistiques du bordereau
    $total_dossiers = count($dossiers);
    $dossiers_corriges = 0;
    $dossiers_non_corriges = 0;
    $montant_total = 0;
    
    foreach ($dossiers as $dossier) {
        if ($dossier['corrige']) {
            $dossiers_corriges++;
        } else {
            $dossiers_non_corriges++;
        }
        
        $montant_total += $dossier['montant'];
    }
    
    echo "<h4>📊 الإحصائيات:</h4>";
    echo "• إجمالي الدوسيهات: $total_dossiers<br>";
    echo "• الدوسيهات المُصححة: $dossiers_corriges<br>";
    echo "• الدوسيهات في الانتظار: $dossiers_non_corriges<br>";
    echo "• المبلغ الإجمالي: " . number_format($montant_total, 2, ',', ' ') . " DH<br>";
    
    echo "<h3>🎭 محاكاة العرض (نفس الكود في views/retours/print.php)</h3>";
    
    echo "<h4>📋 جدول الدوسيهات كما سيظهر في الطباعة:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>#</th>";
    echo "<th style='padding: 8px;'>N° Dossier</th>";
    echo "<th style='padding: 8px;'>N° Adhérent</th>";
    echo "<th style='padding: 8px;'>Nom</th>";
    echo "<th style='padding: 8px;'>Bénéficiaire</th>";
    echo "<th style='padding: 8px;'>Montant</th>";
    echo "<th style='padding: 8px;'>Motifs de retour</th>";
    echo "<th style='padding: 8px;'>Statut</th>";
    echo "</tr>";
    
    // نفس الكود الموجود في views/retours/print.php
    $display_unique = [];
    $display_has_duplicates = false;
    
    foreach ($dossiers as $index => $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($display_unique[$key])) {
            $display_has_duplicates = true;
            $bg_color = '#ffebee';
            echo "⚠️ <strong>تكرار في العرض:</strong> " . $key . "<br>";
        } else {
            $display_unique[$key] = true;
            $bg_color = '#ffffff';
        }
        
        echo "<tr style='background-color: $bg_color;'>";
        echo "<td style='padding: 8px; text-align: center; font-weight: 600;'>" . ($index + 1) . "</td>";
        echo "<td style='padding: 8px; font-weight: 500;'>" . htmlspecialchars($dossier['numero_dossier']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($dossier['numero_adherent']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($dossier['nom']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($dossier['beneficiaire']) . "</td>";
        echo "<td style='padding: 8px; text-align: right; font-weight: 500;'>" . number_format($dossier['montant'], 2, ',', ' ') . " DH</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($dossier['raisons_str']) . "</td>";
        echo "<td style='padding: 8px; text-align: center; font-weight: 600;'>" . ($dossier['corrige'] ? '✓ Corrigé' : '✗ En attente') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🎯 النتيجة النهائية:</h3>";
    
    if (!$display_has_duplicates) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ لا توجد مشكلة!</h4>";
        echo "<p style='color: #155724;'>صفحة الطباعة ستعمل بشكل صحيح.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24; margin-top: 0;'>❌ توجد مشكلة!</h4>";
        echo "<p style='color: #721c24;'>هناك تكرارات في العرض.</p>";
        echo "</div>";
    }
    
    echo "<h3>🔗 اختبار الصفحة الفعلية:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank' style='color: #28a745; font-weight: bold;'>🖨️ صفحة الطباعة الفعلية</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>التتبع: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محاكاة صفحة الطباعة - LOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        h2, h3, h4, h5 {
            color: #333;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        table {
            background: white;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            direction: ltr;
        }
    </style>
</head>
<body>
</body>
</html>
