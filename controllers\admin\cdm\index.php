<?php
// Contrôleur pour la gestion des CDM

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Titre de la page
$page_title = 'Gestion des CDM';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de pagination
$page_num = isset($_GET['page_num']) ? intval($_GET['page_num']) : 1;
$items_per_page = ITEMS_PER_PAGE;
$offset = ($page_num - 1) * $items_per_page;

// Recherche
$search = isset($_GET['search']) ? clean($_GET['search']) : '';

// Construction de la requête SQL
$sql_count = "SELECT COUNT(*) as total FROM cdm";
$sql = "SELECT * FROM cdm";

$params = [];

// Appliquer le filtre de recherche
if (!empty($search)) {
    $sql_count .= " WHERE nom LIKE ? OR adresse LIKE ? OR contact_nom LIKE ?";
    $sql .= " WHERE nom LIKE ? OR adresse LIKE ? OR contact_nom LIKE ?";
    $search_param = "%$search%";
    $params = [$search_param, $search_param, $search_param];
}

// Obtenir le nombre total de CDM
$result = $db->single($sql_count, $params);
$total_items = $result['total'];
$total_pages = ceil($total_items / $items_per_page);

// Ajouter l'ordre et la limite à la requête principale
$sql .= " ORDER BY nom ASC LIMIT ?, ?";
$params[] = $offset;
$params[] = $items_per_page;

// Exécuter la requête
$cdm_list = $db->all($sql, $params);

// Charger la vue
require_once VIEWS_PATH . 'admin/cdm/index.php';
?>
