<?php
// Fonctions utilitaires pour l'application

// Nettoyer les entrées utilisateur
function clean($data) {
    if (is_null($data) || !is_string($data)) {
        return '';
    }
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Générer un jeton CSRF
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

// Vérifier un jeton CSRF
function verifyCSRFToken($token) {
    if (!isset($_SESSION[CSRF_TOKEN_NAME]) || empty($token) || $token !== $_SESSION[CSRF_TOKEN_NAME]) {
        return false;
    }
    return true;
}

// Rediriger avec un message
function redirect($url, $message = '', $type = 'info') {
    if (!empty($message)) {
        $_SESSION['flash'] = [
            'message' => $message,
            'type' => $type
        ];
    }
    header("Location: $url");
    exit;
}

// Afficher un message flash
function displayFlashMessage() {
    if (isset($_SESSION['flash'])) {
        $message = $_SESSION['flash']['message'];
        $type = $_SESSION['flash']['type'];

        echo "<div class='alert alert-{$type} alert-dismissible fade show' role='alert'>
                {$message}
                <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Fermer'></button>
              </div>";

        unset($_SESSION['flash']);
    }
}

// Vérifier si l'utilisateur est authentifié
function isAuthenticated() {
    return isset($_SESSION['user_id']);
}

// Vérifier si l'utilisateur est administrateur
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// Vérifier les permissions
function hasPermission($permission) {
    if (isAdmin()) {
        return true;
    }

    if (!isset($_SESSION['user_permissions'])) {
        return false;
    }

    return in_array($permission, $_SESSION['user_permissions']);
}

// Formater une date
function formatDate($date, $format = null) {
    if (empty($date) || $date == '0000-00-00') {
        return '';
    }

    if ($format === null) {
        $format = DATE_FORMAT;
    }

    $dateObj = new DateTime($date);
    return $dateObj->format($format);
}

// Journaliser une action
function logActivity($user_id, $action, $details = '', $action_type = 'info') {
    $db = Database::getInstance();

    try {
        // Vérifier si l'utilisateur existe
        if ($user_id > 0) {
            $check_user = "SELECT COUNT(*) as count FROM users WHERE id = ?";
            $user_exists = $db->single($check_user, [$user_id]);

            if ($user_exists['count'] == 0) {
                // L'utilisateur n'existe pas, utiliser NULL pour user_id
                $user_id = null;
            }
        }

        // Vérifier si la colonne action_type existe
        $check_column = "SHOW COLUMNS FROM logs LIKE 'action_type'";
        $column_exists = $db->query($check_column)->rowCount() > 0;

        if ($column_exists) {
            $sql = "INSERT INTO logs (user_id, action, action_type, details, ip_address, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())";
            $params = [$user_id, $action, $action_type, $details, $_SERVER['REMOTE_ADDR']];
        } else {
            $sql = "INSERT INTO logs (user_id, action, details, ip_address, created_at)
                    VALUES (?, ?, ?, ?, NOW())";
            $params = [$user_id, $action, $details, $_SERVER['REMOTE_ADDR']];
        }

        $db->query($sql, $params);
    } catch (Exception $e) {
        // Ignorer les erreurs de journalisation
        // Nous ne voulons pas que les erreurs de journalisation perturbent l'application
    }
}

// Générer un numéro de lot unique
function generateLotNumber() {
    $prefix = date('Ymd');
    $db = Database::getInstance();

    $sql = "SELECT MAX(SUBSTRING(numero_lot, 9)) as max_num FROM lots WHERE numero_lot LIKE ?";
    $result = $db->single($sql, [$prefix . '%']);

    $next_num = 1;
    if ($result && !empty($result['max_num'])) {
        $next_num = intval($result['max_num']) + 1;
    }

    return $prefix . str_pad($next_num, 4, '0', STR_PAD_LEFT);
}

// Générer un numéro de bordereau unique pour les dossiers retournés
function generateBordereauNumber() {
    $db = Database::getInstance();

    $sql = "SELECT MAX(numero_bordereau) as max_num FROM dossiers_retournes";
    $result = $db->single($sql);

    $next_num = 10001; // Commencer à 10001 pour avoir un numéro à 5 chiffres
    if ($result && !empty($result['max_num'])) {
        $next_num = intval($result['max_num']) + 1;
    }

    return $next_num;
}

// Vérifier si l'installation est nécessaire
function needsInstallation() {
    try {
        $db = Database::getInstance();
        return !$db->databaseExists();
    } catch (Exception $e) {
        return true;
    }
}

// Obtenir la liste des CDM
function getCDMList() {
    $db = Database::getInstance();
    $sql = "SELECT id, nom FROM cdm ORDER BY nom";
    return $db->all($sql);
}

// Obtenir la liste des actes
function getActesList() {
    $db = Database::getInstance();
    $sql = "SELECT id, nom FROM actes ORDER BY nom";
    return $db->all($sql);
}

// Obtenir la liste des raisons de retour
function getRaisonsRetourList() {
    $db = Database::getInstance();
    $sql = "SELECT id, raison FROM raisons_retour ORDER BY raison";
    return $db->all($sql);
}

// Générer un export Excel
function generateExcel($data, $headers, $filename) {
    // En-têtes pour forcer le téléchargement
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="' . $filename . '.xls"');
    header('Cache-Control: max-age=0');

    // Créer le fichier Excel
    echo '<table border="1">';

    // En-têtes
    echo '<tr>';
    foreach ($headers as $header) {
        echo '<th>' . $header . '</th>';
    }
    echo '</tr>';

    // Données
    foreach ($data as $row) {
        echo '<tr>';
        foreach ($row as $cell) {
            echo '<td>' . $cell . '</td>';
        }
        echo '</tr>';
    }

    echo '</table>';
    exit;
}
?>
