<?php
/**
 * Script pour corriger les problèmes de la base de données
 * Ajoute les tables manquantes et corrige les structures
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Vérifier si l'utilisateur est connecté et est admin
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    die('Accès refusé. Seuls les administrateurs peuvent corriger la base de données.');
}

try {
    // Obtenir la connexion à la base de données
    $db = Database::getInstance();
    
    echo "<h2>Correction de la base de données en cours...</h2>\n";
    echo "<pre>\n";
    
    // 1. Vérifier et créer la table bordereaux_retour
    echo "1. Vérification de la table bordereaux_retour...\n";
    $sql = "SHOW TABLES LIKE 'bordereaux_retour'";
    $result = $db->query($sql);
    
    if ($result->rowCount() === 0) {
        echo "   ⚠️ Table bordereaux_retour manquante, création en cours...\n";
        $sql = "CREATE TABLE bordereaux_retour (
            id INT AUTO_INCREMENT PRIMARY KEY,
            numero INT NOT NULL,
            cdm_id INT NOT NULL,
            date_bordereau DATE NOT NULL,
            notes TEXT NULL,
            created_by INT NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_bordereau (numero, cdm_id),
            FOREIGN KEY (cdm_id) REFERENCES cdm(id) ON DELETE RESTRICT,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->query($sql);
        echo "   ✅ Table bordereaux_retour créée avec succès\n";
        
        // Migrer les données existantes
        echo "   📦 Migration des données existantes...\n";
        $sql = "INSERT IGNORE INTO bordereaux_retour (numero, cdm_id, date_bordereau, created_by, created_at)
                SELECT DISTINCT numero_bordereau, cdm_id, date_bordereau, created_by, created_at
                FROM dossiers_retournes
                WHERE numero_bordereau IS NOT NULL AND cdm_id IS NOT NULL";
        $result = $db->query($sql);
        echo "   ✅ Données migrées avec succès\n";
    } else {
        echo "   ✅ Table bordereaux_retour existe déjà\n";
    }
    
    // 2. Vérifier et créer la table dossier_raisons
    echo "\n2. Vérification de la table dossier_raisons...\n";
    $sql = "SHOW TABLES LIKE 'dossier_raisons'";
    $result = $db->query($sql);
    
    if ($result->rowCount() === 0) {
        echo "   ⚠️ Table dossier_raisons manquante, création en cours...\n";
        $sql = "CREATE TABLE dossier_raisons (
            id INT AUTO_INCREMENT PRIMARY KEY,
            dossier_id INT NOT NULL,
            raison_id INT NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (dossier_id) REFERENCES dossiers_retournes(id) ON DELETE CASCADE,
            FOREIGN KEY (raison_id) REFERENCES raisons_retour(id) ON DELETE CASCADE,
            UNIQUE KEY unique_dossier_raison (dossier_id, raison_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->query($sql);
        echo "   ✅ Table dossier_raisons créée avec succès\n";
        
        // Migrer les raisons existantes
        echo "   📦 Migration des raisons existantes...\n";
        $sql = "INSERT IGNORE INTO dossier_raisons (dossier_id, raison_id, created_at)
                SELECT id, raison_retour_id, created_at
                FROM dossiers_retournes
                WHERE raison_retour_id IS NOT NULL";
        $result = $db->query($sql);
        echo "   ✅ Raisons migrées avec succès\n";
    } else {
        echo "   ✅ Table dossier_raisons existe déjà\n";
    }
    
    // 3. Vérifier les données de base
    echo "\n3. Vérification des données de base...\n";
    
    // Vérifier les CDM
    $sql = "SELECT COUNT(*) as count FROM cdm";
    $result = $db->single($sql);
    echo "   📊 CDM dans la base: " . $result['count'] . "\n";
    
    // Vérifier les actes
    $sql = "SELECT COUNT(*) as count FROM actes";
    $result = $db->single($sql);
    echo "   📊 Actes dans la base: " . $result['count'] . "\n";
    
    // Vérifier les raisons de retour
    $sql = "SELECT COUNT(*) as count FROM raisons_retour";
    $result = $db->single($sql);
    echo "   📊 Raisons de retour dans la base: " . $result['count'] . "\n";
    
    // Vérifier les utilisateurs
    $sql = "SELECT COUNT(*) as count FROM users";
    $result = $db->single($sql);
    echo "   📊 Utilisateurs dans la base: " . $result['count'] . "\n";
    
    // 4. Nettoyer les données orphelines
    echo "\n4. Nettoyage des données orphelines...\n";
    
    // Supprimer les dossiers retournés sans CDM valide
    $sql = "DELETE dr FROM dossiers_retournes dr 
            LEFT JOIN cdm c ON dr.cdm_id = c.id 
            WHERE c.id IS NULL";
    $result = $db->query($sql);
    $affected = $result->rowCount();
    if ($affected > 0) {
        echo "   🧹 $affected dossiers retournés avec CDM invalide supprimés\n";
    }
    
    // Supprimer les raisons de dossier orphelines
    $sql = "DELETE dr FROM dossier_raisons dr 
            LEFT JOIN dossiers_retournes d ON dr.dossier_id = d.id 
            WHERE d.id IS NULL";
    $result = $db->query($sql);
    $affected = $result->rowCount();
    if ($affected > 0) {
        echo "   🧹 $affected raisons de dossier orphelines supprimées\n";
    }
    
    // 5. Optimiser les tables
    echo "\n5. Optimisation des tables...\n";
    $tables = ['users', 'cdm', 'actes', 'raisons_retour', 'lots', 'dossiers', 'dossiers_retournes', 'bordereaux_retour', 'dossier_raisons', 'logs'];
    
    foreach ($tables as $table) {
        try {
            $sql = "OPTIMIZE TABLE $table";
            $db->query($sql);
            echo "   ✅ Table $table optimisée\n";
        } catch (Exception $e) {
            echo "   ⚠️ Impossible d'optimiser la table $table: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n=== RÉSUMÉ ===\n";
    echo "✅ Structure de la base de données corrigée\n";
    echo "✅ Tables manquantes créées\n";
    echo "✅ Données migrées\n";
    echo "✅ Données orphelines nettoyées\n";
    echo "✅ Tables optimisées\n";
    
    echo "\n🎉 Base de données corrigée avec succès!\n";
    echo "\n<a href='index.php'>🏠 Retour à l'accueil</a>\n";
    echo "<a href='reset_database.php'>🔄 Réinitialiser complètement</a>\n";
    
    echo "</pre>\n";
    
} catch (Exception $e) {
    echo "<h2>❌ Erreur lors de la correction</h2>\n";
    echo "<pre>\n";
    echo "Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
    echo "</pre>\n";
    echo "\n<a href='index.php'>🏠 Retour à l'accueil</a>\n";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correction de la base de données</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
        }
        
        pre {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            line-height: 1.4;
        }
        
        a {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 10px 0 0;
        }
        
        a:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
</body>
</html>
