<?php
// Contrôleur pour le rapport d'analyse des actes médicaux

// Titre de la page
$page_title = 'Analyse des actes';
$page = 'reports/actes';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de filtrage
$periode = isset($_GET['periode']) ? clean($_GET['periode']) : '30';
$acte_id = isset($_GET['acte_id']) ? intval($_GET['acte_id']) : 0;
$cdm_id = isset($_GET['cdm_id']) ? intval($_GET['cdm_id']) : 0;
$date_debut = isset($_GET['date_debut']) ? clean($_GET['date_debut']) : '';
$date_fin = isset($_GET['date_fin']) ? clean($_GET['date_fin']) : '';

// Déterminer les dates de début et de fin
if (!empty($date_debut) && !empty($date_fin)) {
    $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
    $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
} else {
    switch ($periode) {
        case '7':
            $date_debut_formatted = date('Y-m-d', strtotime('-7 days'));
            break;
        case '30':
            $date_debut_formatted = date('Y-m-d', strtotime('-30 days'));
            break;
        case '90':
            $date_debut_formatted = date('Y-m-d', strtotime('-90 days'));
            break;
        case '365':
            $date_debut_formatted = date('Y-m-d', strtotime('-365 days'));
            break;
        default:
            $date_debut_formatted = date('Y-m-d', strtotime('-30 days'));
    }
    $date_fin_formatted = date('Y-m-d');
}

// Obtenir la liste des actes pour le filtre
$actes_list = getActesList();

// Obtenir la liste des CDM pour le filtre
$cdm_list = getCDMList();

// Distribution des actes
$sql = "SELECT a.id, a.nom, COUNT(d.id) as nb_dossiers, SUM(d.montant) as montant_total
        FROM actes a
        LEFT JOIN dossiers d ON a.id = d.acte_id
        LEFT JOIN lots l ON d.lot_id = l.id
        WHERE l.date_demande BETWEEN ? AND ?";

$params = [$date_debut_formatted, $date_fin_formatted];

if ($acte_id > 0) {
    $sql .= " AND a.id = ?";
    $params[] = $acte_id;
}

if ($cdm_id > 0) {
    $sql .= " AND l.cdm_id = ?";
    $params[] = $cdm_id;
}

$sql .= " GROUP BY a.id, a.nom
          ORDER BY nb_dossiers DESC";

$actes_distribution = $db->all($sql, $params);

// Actes par CDM
$sql = "SELECT c.id, c.nom as cdm_nom, a.id as acte_id, a.nom as acte_nom, 
               COUNT(d.id) as nb_dossiers, SUM(d.montant) as montant_total
        FROM cdm c
        JOIN lots l ON c.id = l.cdm_id
        JOIN dossiers d ON l.id = d.lot_id
        JOIN actes a ON d.acte_id = a.id
        WHERE l.date_demande BETWEEN ? AND ?";

$params = [$date_debut_formatted, $date_fin_formatted];

if ($acte_id > 0) {
    $sql .= " AND a.id = ?";
    $params[] = $acte_id;
}

if ($cdm_id > 0) {
    $sql .= " AND c.id = ?";
    $params[] = $cdm_id;
}

$sql .= " GROUP BY c.id, c.nom, a.id, a.nom
          ORDER BY c.nom, nb_dossiers DESC";

$actes_by_cdm = $db->all($sql, $params);

// Regrouper les actes par CDM
$cdm_actes = [];
foreach ($actes_by_cdm as $row) {
    if (!isset($cdm_actes[$row['id']])) {
        $cdm_actes[$row['id']] = [
            'nom' => $row['cdm_nom'],
            'actes' => []
        ];
    }
    $cdm_actes[$row['id']]['actes'][] = [
        'id' => $row['acte_id'],
        'nom' => $row['acte_nom'],
        'nb_dossiers' => $row['nb_dossiers'],
        'montant_total' => $row['montant_total']
    ];
}

// Évolution mensuelle des actes
$sql = "SELECT a.id, a.nom, 
               DATE_FORMAT(l.date_demande, '%Y-%m') as mois,
               COUNT(d.id) as nb_dossiers
        FROM actes a
        JOIN dossiers d ON a.id = d.acte_id
        JOIN lots l ON d.lot_id = l.id
        WHERE l.date_demande BETWEEN ? AND ?";

$params = [$date_debut_formatted, $date_fin_formatted];

if ($acte_id > 0) {
    $sql .= " AND a.id = ?";
    $params[] = $acte_id;
}

if ($cdm_id > 0) {
    $sql .= " AND l.cdm_id = ?";
    $params[] = $cdm_id;
}

$sql .= " GROUP BY a.id, a.nom, DATE_FORMAT(l.date_demande, '%Y-%m')
          ORDER BY a.nom, mois";

$evolution_mensuelle = $db->all($sql, $params);

// Regrouper l'évolution par acte
$evolution_by_acte = [];
$all_months = [];

foreach ($evolution_mensuelle as $row) {
    if (!isset($evolution_by_acte[$row['id']])) {
        $evolution_by_acte[$row['id']] = [
            'nom' => $row['nom'],
            'data' => []
        ];
    }
    $evolution_by_acte[$row['id']]['data'][$row['mois']] = $row['nb_dossiers'];
    if (!in_array($row['mois'], $all_months)) {
        $all_months[] = $row['mois'];
    }
}

// Trier les mois
sort($all_months);

// Compléter les mois manquants
foreach ($evolution_by_acte as $acte_id => $acte_data) {
    foreach ($all_months as $month) {
        if (!isset($acte_data['data'][$month])) {
            $evolution_by_acte[$acte_id]['data'][$month] = 0;
        }
    }
    // Trier par mois
    ksort($evolution_by_acte[$acte_id]['data']);
}

// Formater les mois pour l'affichage
$formatted_months = [];
foreach ($all_months as $month) {
    $date = DateTime::createFromFormat('Y-m', $month);
    $formatted_months[$month] = $date->format('M Y');
}

// Charger la vue
require_once VIEWS_PATH . 'reports/actes.php';
?>
