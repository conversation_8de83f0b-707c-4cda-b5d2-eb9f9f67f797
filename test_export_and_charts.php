<?php
/**
 * اختبار التصدير والمخططات المحسنة
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // تسجيل دخول تلقائي
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h1>🧪 اختبار التصدير والمخططات المحسنة</h1>";
    echo "<p>✅ تم تسجيل الدخول كـ: " . $_SESSION['username'] . "</p>";
    
    echo "<h2>📊 1. اختبار التصدير</h2>";
    
    // فحص وجود الكلاس ExcelExporter
    if (class_exists('ExcelExporter')) {
        echo "✅ كلاس ExcelExporter موجود<br>";
    } else {
        echo "❌ كلاس ExcelExporter غير موجود<br>";
        
        // محاولة تضمينه
        if (file_exists('includes/ExcelExporter.php')) {
            require_once 'includes/ExcelExporter.php';
            if (class_exists('ExcelExporter')) {
                echo "✅ تم تحميل كلاس ExcelExporter بنجاح<br>";
            } else {
                echo "❌ فشل في تحميل كلاس ExcelExporter<br>";
            }
        } else {
            echo "❌ ملف ExcelExporter.php غير موجود<br>";
        }
    }
    
    // فحص البيانات للتصدير
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $result = $db->single($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• عدد الدوسيهات للتصدير: " . $result['count'] . "<br>";
    
    if ($result['count'] > 0) {
        echo "✅ توجد بيانات للتصدير<br>";
    } else {
        echo "❌ لا توجد بيانات للتصدير<br>";
    }
    
    echo "<h2>📈 2. اختبار صفحة التقارير</h2>";
    
    // فحص وجود ملف التقارير
    $reports_file = 'views/reports/retours.php';
    if (file_exists($reports_file)) {
        echo "✅ ملف التقارير موجود<br>";
        
        // فحص حجم الملف
        $file_size = filesize($reports_file);
        echo "• حجم ملف التقارير: " . number_format($file_size) . " بايت<br>";
        
        if ($file_size > 10000) {
            echo "✅ ملف التقارير يحتوي على محتوى كافي<br>";
        } else {
            echo "⚠️ ملف التقارير صغير قد يكون ناقص<br>";
        }
    } else {
        echo "❌ ملف التقارير غير موجود<br>";
    }
    
    // فحص وجود ملف controller التقارير
    $controller_file = 'controllers/reports/retours.php';
    if (file_exists($controller_file)) {
        echo "✅ controller التقارير موجود<br>";
    } else {
        echo "❌ controller التقارير غير موجود<br>";
    }
    
    echo "<h2>🎯 3. الروابط للاختبار</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; border: 2px solid #28a745;'>";
    echo "<h3 style='color: #28a745;'>🔗 اختبر الميزات المحسنة:</h3>";
    echo "<ul style='font-size: 16px;'>";
    
    // روابط التصدير
    echo "<li><strong>اختبار التصدير:</strong></li>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/export&numero=10001&cdm_id=1&format=csv' target='_blank' style='color: #28a745; font-weight: bold;'>📊 تصدير CSV</a></li>";
    echo "<li><a href='index.php?page=retours/export&numero=10001&cdm_id=1&format=xlsx' target='_blank' style='color: #17a2b8; font-weight: bold;'>📊 تصدير Excel</a></li>";
    echo "</ul>";
    
    // روابط التقارير
    echo "<li><strong>اختبار المخططات المحسنة:</strong></li>";
    echo "<ul>";
    echo "<li><a href='index.php?page=reports/retours' target='_blank' style='color: #dc3545; font-weight: bold; font-size: 18px;'>📈 صفحة التقارير المحسنة</a></li>";
    echo "<li><a href='index.php?page=reports/retours&periode=30' target='_blank' style='color: #6f42c1; font-weight: bold;'>📊 تقرير 30 يوم</a></li>";
    echo "<li><a href='index.php?page=reports/retours&periode=90' target='_blank' style='color: #fd7e14; font-weight: bold;'>📊 تقرير 90 يوم</a></li>";
    echo "</ul>";
    
    // روابط أخرى
    echo "<li><strong>روابط أخرى:</strong></li>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank' style='color: #198754; font-weight: bold;'>🖨️ صفحة الطباعة</a></li>";
    echo "<li><a href='index.php?page=dashboard' target='_blank' style='color: #0d6efd; font-weight: bold;'>📈 لوحة التحكم</a></li>";
    echo "</ul>";
    
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🔧 4. معلومات تقنية</h2>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
    echo "<h3 style='color: #856404;'>📋 التحسينات المطبقة:</h3>";
    echo "<ul style='color: #856404;'>";
    echo "<li>✅ <strong>إصلاح مشكلة ExcelExporter:</strong> تم إضافة require_once في ملف التصدير</li>";
    echo "<li>✅ <strong>مخطط تفاعلي:</strong> 3 أنواع مخططات (خطوط، أعمدة، مناطق)</li>";
    echo "<li>✅ <strong>إحصائيات سريعة:</strong> أعلى قيمة، أفضل تصحيح، متوسط، اتجاه</li>";
    echo "<li>✅ <strong>تفاصيل محسنة:</strong> tooltips تفاعلية مع معلومات شاملة</li>";
    echo "<li>✅ <strong>محاور مزدوجة:</strong> عدد الدوسيهات + نسبة التصحيح</li>";
    echo "<li>✅ <strong>ألوان متدرجة:</strong> تصميم أكثر جاذبية ووضوحاً</li>";
    echo "<li>✅ <strong>أزرار تبديل:</strong> تغيير نوع المخطط بسهولة</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🎉 5. النتيجة النهائية</h2>";
    
    $all_good = true;
    $issues = [];
    
    // فحص الملفات المطلوبة
    if (!class_exists('ExcelExporter')) {
        $all_good = false;
        $issues[] = "كلاس ExcelExporter غير متاح";
    }
    
    if (!file_exists($reports_file)) {
        $all_good = false;
        $issues[] = "ملف التقارير غير موجود";
    }
    
    if (!file_exists($controller_file)) {
        $all_good = false;
        $issues[] = "controller التقارير غير موجود";
    }
    
    if ($result['count'] == 0) {
        $all_good = false;
        $issues[] = "لا توجد بيانات للاختبار";
    }
    
    if ($all_good) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 30px; margin: 20px 0; text-align: center;'>";
        echo "<h3 style='color: #155724; margin-top: 0; font-size: 24px;'>🎉 جميع التحسينات جاهزة!</h3>";
        echo "<ul style='color: #155724; text-align: right; font-size: 16px;'>";
        echo "<li>✅ <strong>التصدير يعمل:</strong> CSV و Excel</li>";
        echo "<li>✅ <strong>المخططات محسنة:</strong> تفاعلية ومفصلة</li>";
        echo "<li>✅ <strong>الإحصائيات متوفرة:</strong> معلومات شاملة</li>";
        echo "<li>✅ <strong>التصميم محسن:</strong> أكثر وضوحاً وجاذبية</li>";
        echo "</ul>";
        echo "<h4 style='color: #155724; font-size: 20px;'>🚀 جرب الميزات الجديدة الآن!</h4>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ توجد مشاكل تحتاج إصلاح</h3>";
        echo "<ul style='color: #721c24;'>";
        foreach ($issues as $issue) {
            echo "<li>❌ $issue</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>التتبع: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصدير والمخططات - LOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        h1, h2, h3 {
            color: #333;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
