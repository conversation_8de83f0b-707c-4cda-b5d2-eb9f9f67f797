<?php
// Contrôleur pour l'exportation des bordereaux de retour vers Excel

// Vérifier si les paramètres du bordereau sont fournis
if (!isset($_GET['numero']) || empty($_GET['numero']) || !isset($_GET['cdm_id']) || empty($_GET['cdm_id'])) {
    redirect('index.php?page=retours', 'Paramètres du bordereau non spécifiés.', 'danger');
}

$numero_bordereau = intval($_GET['numero']);
$cdm_id = intval($_GET['cdm_id']);

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Vérifier si le bordereau existe
// Vérifier d'abord dans la table bordereaux_retour
$sql = "SELECT COUNT(*) as count FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
$result = $db->single($sql, [$numero_bordereau, $cdm_id]);

if ($result['count'] === 0) {
    // Si le bordereau n'existe pas dans la table bordereaux_retour, vérifier dans dossiers_retournes
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $result = $db->single($sql, [$numero_bordereau, $cdm_id]);
    
    if ($result['count'] === 0) {
        redirect('index.php?page=retours', 'Bordereau non trouvé.', 'danger');
    }
}

// Récupérer les informations du CDM
$sql = "SELECT * FROM cdm WHERE id = ?";
$cdm = $db->single($sql, [$cdm_id]);

if (!$cdm) {
    redirect('index.php?page=retours', 'CDM non trouvé.', 'danger');
}

// Récupérer les dossiers associés à ce bordereau avec toutes les informations
$sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
        FROM dossiers_retournes dr
        LEFT JOIN actes a ON dr.acte_id = a.id
        LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
        WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
        ORDER BY dr.created_at ASC";
$dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);

// Récupérer tous les motifs de retour pour chaque dossier
foreach ($dossiers as &$dossier) {
    // Vérifier si la table dossier_raisons existe
    $sql = "SHOW TABLES LIKE 'dossier_raisons'";
    $result = $db->query($sql);
    $table_exists = $result->rowCount() > 0;

    if ($table_exists) {
        $sql = "SELECT r.raison
                FROM dossier_raisons dr
                JOIN raisons_retour r ON dr.raison_id = r.id
                WHERE dr.dossier_id = ?
                ORDER BY dr.created_at ASC";
        $raisons = $db->all($sql, [$dossier['id']]);

        if (!empty($raisons)) {
            $raisons_str = [];
            foreach ($raisons as $raison) {
                $raisons_str[] = $raison['raison'];
            }
            $dossier['raisons_str'] = implode(', ', $raisons_str);
        } else {
            $dossier['raisons_str'] = $dossier['raison_retour_nom'] ?? '';
        }
    } else {
        $dossier['raisons_str'] = $dossier['raison_retour_nom'] ?? '';
    }
}

// Récupérer les informations du bordereau
$sql = "SELECT * FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
$bordereau = $db->single($sql, [$numero_bordereau, $cdm_id]);

// Préparer les données pour l'exportation
$headers = [
    'N°',
    'N° Bordereau',
    'CDM',
    'Date Bordereau',
    'N° Dossier',
    'N° Adhérent',
    'Nom',
    'Bénéficiaire',
    'Code Acte',
    'Nom Acte',
    'Montant (DH)',
    'Raisons du retour',
    'Date retour',
    'Date correction',
    'Statut',
    'Notes'
];

$data = [];
foreach ($dossiers as $index => $dossier) {
    $data[] = [
        $index + 1,
        $numero_bordereau,
        $cdm['nom'],
        formatDate($bordereau['date_bordereau'] ?? $dossier['date_bordereau']),
        $dossier['numero_dossier'] ?? '',
        $dossier['numero_adherent'] ?? '',
        $dossier['nom'] ?? '',
        $dossier['beneficiaire'] ?? '',
        $dossier['acte_code'] ?? '',
        $dossier['acte_nom'] ?? '',
        !empty($dossier['montant']) ? number_format(floatval($dossier['montant']), 2, ',', ' ') : '0,00',
        $dossier['raisons_str'] ?? $dossier['raison_retour_nom'] ?? '',
        formatDate($dossier['date_retour']),
        formatDate($dossier['date_correction']),
        $dossier['corrige'] ? 'Corrigé' : 'Non corrigé',
        $dossier['notes'] ?? ''
    ];
}

// Nom du fichier
$filename = 'Bordereau_' . $numero_bordereau . '_' . $cdm['nom'] . '_' . date('Ymd');

// Format d'exportation (csv par défaut)
$format = isset($_GET['format']) && $_GET['format'] === 'xlsx' ? 'xlsx' : 'csv';

// Journaliser l'action
logActivity($_SESSION['user_id'], 'Exportation d\'un bordereau', "Bordereau #$numero_bordereau au format $format");

// Exporter les données
ExcelExporter::export($data, $headers, $filename, $format);
?>
