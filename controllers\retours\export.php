<?php
// Contrôleur pour l'exportation des bordereaux de retour vers Excel

// Vérifier si les paramètres du bordereau sont fournis
if (!isset($_GET['numero']) || empty($_GET['numero']) || !isset($_GET['cdm_id']) || empty($_GET['cdm_id'])) {
    redirect('index.php?page=retours', 'Paramètres du bordereau non spécifiés.', 'danger');
}

$numero_bordereau = intval($_GET['numero']);
$cdm_id = intval($_GET['cdm_id']);

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Vérifier si le bordereau existe
// Vérifier d'abord dans la table bordereaux_retour
$sql = "SELECT COUNT(*) as count FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
$result = $db->single($sql, [$numero_bordereau, $cdm_id]);

if ($result['count'] === 0) {
    // Si le bordereau n'existe pas dans la table bordereaux_retour, vérifier dans dossiers_retournes
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $result = $db->single($sql, [$numero_bordereau, $cdm_id]);
    
    if ($result['count'] === 0) {
        redirect('index.php?page=retours', 'Bordereau non trouvé.', 'danger');
    }
}

// Récupérer les informations du CDM
$sql = "SELECT * FROM cdm WHERE id = ?";
$cdm = $db->single($sql, [$cdm_id]);

if (!$cdm) {
    redirect('index.php?page=retours', 'CDM non trouvé.', 'danger');
}

// Récupérer les dossiers associés à ce bordereau
$sql = "SELECT dr.*, a.nom as acte_nom, r.raison as raison_retour_nom
        FROM dossiers_retournes dr
        LEFT JOIN actes a ON dr.acte_id = a.id
        LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
        WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
        ORDER BY dr.created_at ASC";
$dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);

// Préparer les données pour l'exportation
$headers = [
    'N°',
    'N° Dossier',
    'N° Adhérent',
    'Nom',
    'Bénéficiaire',
    'Acte',
    'Montant (DH)',
    'Raison du retour',
    'Date retour',
    'Date correction',
    'Statut'
];

$data = [];
foreach ($dossiers as $index => $dossier) {
    $data[] = [
        $index + 1,
        $dossier['numero_dossier'],
        $dossier['numero_adherent'],
        $dossier['nom'],
        $dossier['beneficiaire'],
        $dossier['acte_nom'],
        !empty($dossier['montant']) ? number_format($dossier['montant'], 2, ',', ' ') : '',
        $dossier['raison_retour_nom'],
        formatDate($dossier['date_retour']),
        formatDate($dossier['date_correction']),
        $dossier['corrige'] ? 'Corrigé' : 'Non corrigé'
    ];
}

// Nom du fichier
$filename = 'Bordereau_' . $numero_bordereau . '_' . $cdm['nom'] . '_' . date('Ymd');

// Format d'exportation (csv par défaut)
$format = isset($_GET['format']) && $_GET['format'] === 'xlsx' ? 'xlsx' : 'csv';

// Journaliser l'action
logActivity($_SESSION['user_id'], 'Exportation d\'un bordereau', "Bordereau #$numero_bordereau au format $format");

// Exporter les données
ExcelExporter::export($data, $headers, $filename, $format);
?>
