<?php
// Vue pour le rapport d'analyse des actes médicaux
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-procedures"></i> Analyse des actes</h1>
        <div>
            <button id="printPage" class="btn btn-outline-secondary">
                <i class="fas fa-print"></i> Imprimer
            </button>
            <a href="index.php?page=reports/export&type=actes&periode=<?php echo $periode; ?>&acte_id=<?php echo $acte_id; ?>&cdm_id=<?php echo $cdm_id; ?>&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Exporter
            </a>
        </div>
    </div>
    
    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter"></i> Filtres</h5>
        </div>
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="reports/actes">
                
                <div class="col-md-3">
                    <label for="periode" class="form-label">Période prédéfinie</label>
                    <select class="form-select" id="periode" name="periode">
                        <option value="7" <?php echo $periode === '7' ? 'selected' : ''; ?>>7 derniers jours</option>
                        <option value="30" <?php echo $periode === '30' ? 'selected' : ''; ?>>30 derniers jours</option>
                        <option value="90" <?php echo $periode === '90' ? 'selected' : ''; ?>>90 derniers jours</option>
                        <option value="365" <?php echo $periode === '365' ? 'selected' : ''; ?>>365 derniers jours</option>
                        <option value="custom" <?php echo (!empty($date_debut) && !empty($date_fin)) ? 'selected' : ''; ?>>Personnalisée</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="acte_id" class="form-label">Acte</label>
                    <select class="form-select" id="acte_id" name="acte_id">
                        <option value="0">Tous les actes</option>
                        <?php foreach ($actes_list as $acte): ?>
                            <option value="<?php echo $acte['id']; ?>" <?php echo $acte_id == $acte['id'] ? 'selected' : ''; ?>>
                                <?php echo $acte['nom']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="cdm_id" class="form-label">CDM</label>
                    <select class="form-select" id="cdm_id" name="cdm_id">
                        <option value="0">Tous les CDM</option>
                        <?php foreach ($cdm_list as $cdm): ?>
                            <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm_id == $cdm['id'] ? 'selected' : ''; ?>>
                                <?php echo $cdm['nom']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2 date-range" style="<?php echo (!empty($date_debut) && !empty($date_fin)) ? '' : 'display: none;'; ?>">
                    <label for="date_debut" class="form-label">Date début</label>
                    <input type="text" class="form-control datepicker" id="date_debut" name="date_debut" value="<?php echo $date_debut; ?>" placeholder="jj/mm/aaaa">
                </div>
                
                <div class="col-md-2 date-range" style="<?php echo (!empty($date_debut) && !empty($date_fin)) ? '' : 'display: none;'; ?>">
                    <label for="date_fin" class="form-label">Date fin</label>
                    <input type="text" class="form-control datepicker" id="date_fin" name="date_fin" value="<?php echo $date_fin; ?>" placeholder="jj/mm/aaaa">
                </div>
                
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Appliquer les filtres
                    </button>
                    <a href="index.php?page=reports/actes" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Période analysée -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle"></i> Période analysée: <strong>
            <?php 
                echo formatDate($date_debut_formatted) . ' au ' . formatDate($date_fin_formatted);
                if ($acte_id > 0) {
                    foreach ($actes_list as $acte) {
                        if ($acte['id'] == $acte_id) {
                            echo ' - Acte: ' . $acte['nom'];
                            break;
                        }
                    }
                }
                if ($cdm_id > 0) {
                    foreach ($cdm_list as $cdm) {
                        if ($cdm['id'] == $cdm_id) {
                            echo ' - CDM: ' . $cdm['nom'];
                            break;
                        }
                    }
                }
            ?>
        </strong>
    </div>
    
    <!-- Distribution des actes -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Distribution des actes</h5>
        </div>
        <div class="card-body">
            <?php if (count($actes_distribution) > 0 && array_sum(array_column($actes_distribution, 'nb_dossiers')) > 0): ?>
                <div class="row">
                    <div class="col-md-8">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Acte</th>
                                        <th class="text-end">Dossiers</th>
                                        <th class="text-end">Montant total</th>
                                        <th>Pourcentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                        $total_dossiers = array_sum(array_column($actes_distribution, 'nb_dossiers'));
                                        $total_montant = array_sum(array_column($actes_distribution, 'montant_total'));
                                    ?>
                                    <?php foreach ($actes_distribution as $acte): ?>
                                        <?php if ($acte['nb_dossiers'] > 0): ?>
                                            <tr>
                                                <td><?php echo $acte['nom']; ?></td>
                                                <td class="text-end"><?php echo $acte['nb_dossiers']; ?></td>
                                                <td class="text-end"><?php echo number_format($acte['montant_total'], 2, ',', ' '); ?> DH</td>
                                                <td>
                                                    <?php 
                                                        $pourcentage = ($total_dossiers > 0) ? 
                                                                    ($acte['nb_dossiers'] / $total_dossiers) * 100 : 0;
                                                    ?>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar bg-primary" role="progressbar" 
                                                             style="width: <?php echo $pourcentage; ?>%;" 
                                                             aria-valuenow="<?php echo $pourcentage; ?>" 
                                                             aria-valuemin="0" aria-valuemax="100">
                                                            <?php echo round($pourcentage, 1); ?>%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-primary">
                                        <th>Total</th>
                                        <th class="text-end"><?php echo $total_dossiers; ?></th>
                                        <th class="text-end"><?php echo number_format($total_montant, 2, ',', ' '); ?> DH</th>
                                        <th>100%</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <canvas id="actesDistributionChart" height="300"></canvas>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune donnée disponible pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Actes par CDM -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-hospital"></i> Actes par CDM</h5>
        </div>
        <div class="card-body">
            <?php if (count($cdm_actes) > 0): ?>
                <div class="accordion" id="accordionCDM">
                    <?php foreach ($cdm_actes as $cdm_id => $cdm_data): ?>
                        <?php if (count($cdm_data['actes']) > 0): ?>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingCDM<?php echo $cdm_id; ?>">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseCDM<?php echo $cdm_id; ?>" aria-expanded="false" aria-controls="collapseCDM<?php echo $cdm_id; ?>">
                                        <?php echo $cdm_data['nom']; ?> - <?php echo count($cdm_data['actes']); ?> acte(s)
                                    </button>
                                </h2>
                                <div id="collapseCDM<?php echo $cdm_id; ?>" class="accordion-collapse collapse" aria-labelledby="headingCDM<?php echo $cdm_id; ?>" data-bs-parent="#accordionCDM">
                                    <div class="accordion-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Acte</th>
                                                        <th class="text-end">Dossiers</th>
                                                        <th class="text-end">Montant total</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php 
                                                        $cdm_total_dossiers = array_sum(array_column($cdm_data['actes'], 'nb_dossiers'));
                                                        $cdm_total_montant = array_sum(array_column($cdm_data['actes'], 'montant_total'));
                                                    ?>
                                                    <?php foreach ($cdm_data['actes'] as $acte): ?>
                                                        <tr>
                                                            <td><?php echo $acte['nom']; ?></td>
                                                            <td class="text-end"><?php echo $acte['nb_dossiers']; ?></td>
                                                            <td class="text-end"><?php echo number_format($acte['montant_total'], 2, ',', ' '); ?> DH</td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                                <tfoot>
                                                    <tr class="table-success">
                                                        <th>Total</th>
                                                        <th class="text-end"><?php echo $cdm_total_dossiers; ?></th>
                                                        <th class="text-end"><?php echo number_format($cdm_total_montant, 2, ',', ' '); ?> DH</th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune donnée disponible pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Évolution mensuelle des actes -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-chart-line"></i> Évolution mensuelle des actes</h5>
        </div>
        <div class="card-body">
            <?php if (count($evolution_by_acte) > 0 && count($all_months) > 0): ?>
                <!-- Graphique d'évolution mensuelle -->
                <canvas id="evolutionChart" height="300"></canvas>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune donnée disponible pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Afficher/masquer les champs de date personnalisée
    document.getElementById('periode').addEventListener('change', function() {
        var dateRangeFields = document.querySelectorAll('.date-range');
        if (this.value === 'custom') {
            dateRangeFields.forEach(function(field) {
                field.style.display = '';
            });
        } else {
            dateRangeFields.forEach(function(field) {
                field.style.display = 'none';
            });
        }
    });
    
    // Graphique de distribution des actes
    <?php if (count($actes_distribution) > 0 && array_sum(array_column($actes_distribution, 'nb_dossiers')) > 0): ?>
        var actesDistributionCtx = document.getElementById('actesDistributionChart').getContext('2d');
        var actesDistributionChart = new Chart(actesDistributionCtx, {
            type: 'pie',
            data: {
                labels: [
                    <?php 
                        $acte_names = [];
                        foreach ($actes_distribution as $acte) {
                            if ($acte['nb_dossiers'] > 0) {
                                $acte_names[] = $acte['nom'];
                            }
                        }
                        echo "'" . implode("', '", $acte_names) . "'"; 
                    ?>
                ],
                datasets: [{
                    data: [
                        <?php 
                            $acte_data = [];
                            foreach ($actes_distribution as $acte) {
                                if ($acte['nb_dossiers'] > 0) {
                                    $acte_data[] = $acte['nb_dossiers'];
                                }
                            }
                            echo implode(', ', $acte_data); 
                        ?>
                    ],
                    backgroundColor: [
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(25, 135, 84, 0.7)',
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(13, 202, 240, 0.7)',
                        'rgba(108, 117, 125, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(214, 51, 132, 0.7)',
                        'rgba(32, 201, 151, 0.7)',
                        'rgba(253, 126, 20, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    <?php endif; ?>
    
    // Graphique d'évolution mensuelle
    <?php if (count($evolution_by_acte) > 0 && count($all_months) > 0): ?>
        var evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
        var evolutionChart = new Chart(evolutionCtx, {
            type: 'line',
            data: {
                labels: [<?php echo "'" . implode("', '", array_values($formatted_months)) . "'"; ?>],
                datasets: [
                    <?php 
                        $colors = [
                            'rgba(13, 110, 253, 1)', // blue
                            'rgba(25, 135, 84, 1)',  // green
                            'rgba(220, 53, 69, 1)',  // red
                            'rgba(255, 193, 7, 1)',  // yellow
                            'rgba(13, 202, 240, 1)', // cyan
                            'rgba(108, 117, 125, 1)', // gray
                            'rgba(111, 66, 193, 1)',  // purple
                            'rgba(214, 51, 132, 1)'   // pink
                        ];
                        
                        $datasets = [];
                        $color_index = 0;
                        
                        foreach ($evolution_by_acte as $acte_id => $acte_data) {
                            $color = $colors[$color_index % count($colors)];
                            $data_values = [];
                            
                            foreach ($all_months as $month) {
                                $data_values[] = $acte_data['data'][$month];
                            }
                            
                            $datasets[] = "{
                                label: '{$acte_data['nom']}',
                                data: [" . implode(', ', $data_values) . "],
                                backgroundColor: '{$color}',
                                borderColor: '{$color}',
                                borderWidth: 2,
                                fill: false,
                                tension: 0.1
                            }";
                            
                            $color_index++;
                        }
                        
                        echo implode(', ', $datasets);
                    ?>
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    <?php endif; ?>
});
</script>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
