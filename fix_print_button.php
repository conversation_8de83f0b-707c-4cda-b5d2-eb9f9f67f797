<?php
// Script pour corriger le problème du bouton d'impression
require_once 'config/config.php';
require_once 'config/database.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

try {
    // Vérifier si la table bordereaux_retour existe
    $sql = "SHOW TABLES LIKE 'bordereaux_retour'";
    $result = $db->query($sql);
    $table_exists = $result->rowCount() > 0;
    
    if (!$table_exists) {
        echo "<h2>Création de la table 'bordereaux_retour'...</h2>";
        
        // Créer la table bordereaux_retour
        $sql = "CREATE TABLE bordereaux_retour (
            id INT AUTO_INCREMENT PRIMARY KEY,
            numero INT NOT NULL,
            cdm_id INT NOT NULL,
            date_bordereau DATE NOT NULL,
            created_by INT NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NULL,
            UNIQUE KEY unique_bordereau (numero, cdm_id),
            FOREIGN KEY (cdm_id) REFERENCES cdm(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )";
        
        $db->query($sql);
        echo "<p>Table 'bordereaux_retour' créée avec succès.</p>";
        
        // Migrer les données existantes
        echo "<h2>Migration des données existantes...</h2>";
        
        $sql = "INSERT IGNORE INTO bordereaux_retour (numero, cdm_id, date_bordereau, created_by, created_at)
                SELECT DISTINCT numero_bordereau, cdm_id, date_bordereau, created_by, created_at
                FROM dossiers_retournes";
        
        $result = $db->query($sql);
        $count = $result->rowCount();
        
        echo "<p>$count bordereaux migrés avec succès.</p>";
    } else {
        echo "<h2>La table 'bordereaux_retour' existe déjà.</h2>";
        
        // Vérifier si tous les bordereaux existants ont été migrés
        $sql = "SELECT DISTINCT dr.numero_bordereau, dr.cdm_id
                FROM dossiers_retournes dr
                LEFT JOIN bordereaux_retour br ON dr.numero_bordereau = br.numero AND dr.cdm_id = br.cdm_id
                WHERE br.id IS NULL";
        
        $missing_bordereaux = $db->all($sql);
        
        if (!empty($missing_bordereaux)) {
            echo "<h3>Migration des bordereaux manquants...</h3>";
            
            $count = 0;
            foreach ($missing_bordereaux as $bordereau) {
                $sql = "SELECT date_bordereau, created_by, created_at
                        FROM dossiers_retournes
                        WHERE numero_bordereau = ? AND cdm_id = ?
                        LIMIT 1";
                $info = $db->single($sql, [$bordereau['numero_bordereau'], $bordereau['cdm_id']]);
                
                if ($info) {
                    $sql = "INSERT INTO bordereaux_retour (numero, cdm_id, date_bordereau, created_by, created_at)
                            VALUES (?, ?, ?, ?, ?)";
                    $db->query($sql, [
                        $bordereau['numero_bordereau'],
                        $bordereau['cdm_id'],
                        $info['date_bordereau'],
                        $info['created_by'],
                        $info['created_at']
                    ]);
                    $count++;
                }
            }
            
            echo "<p>$count bordereaux supplémentaires migrés avec succès.</p>";
        } else {
            echo "<p>Tous les bordereaux ont déjà été migrés.</p>";
        }
    }
    
    // Vérifier si le contrôleur d'impression a été mis à jour
    $print_controller = file_get_contents('controllers/retours/print.php');
    
    if (strpos($print_controller, 'bordereaux_retour') === false) {
        echo "<h2>Le contrôleur d'impression doit être mis à jour.</h2>";
        echo "<p>Veuillez suivre les instructions pour mettre à jour le contrôleur d'impression.</p>";
    } else {
        echo "<h2>Le contrôleur d'impression est à jour.</h2>";
    }
    
    echo "<h2>Problème résolu !</h2>";
    echo "<p>Le bouton d'impression devrait maintenant fonctionner correctement.</p>";
    echo "<p><a href='index.php?page=retours' class='btn btn-primary'>Retour à la liste des bordereaux</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Erreur:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
