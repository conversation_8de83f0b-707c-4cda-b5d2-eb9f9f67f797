<?php
// Contrôleur pour la modification d'un dossier

// Vérifier si l'ID du dossier est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=lots', 'ID du dossier non spécifié.', 'danger');
}

$dossier_id = intval($_GET['id']);
$lot_id = isset($_GET['lot_id']) ? intval($_GET['lot_id']) : 0;

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du dossier
$sql = "SELECT d.*, l.numero_lot, l.cdm_id, c.nom as cdm_nom
        FROM dossiers d
        JOIN lots l ON d.lot_id = l.id
        JOIN cdm c ON l.cdm_id = c.id
        WHERE d.id = ?";
$dossier = $db->single($sql, [$dossier_id]);

// Vérifier si le dossier existe
if (!$dossier) {
    redirect('index.php?page=lots', 'Dossier non trouvé.', 'danger');
}

// Si le lot_id n'est pas fourni, utiliser celui du dossier
if ($lot_id === 0) {
    $lot_id = $dossier['lot_id'];
}

// Titre de la page
$page_title = 'Modifier le dossier #' . $dossier['numero_dossier'];
$page = 'dossiers/edit';

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=dossiers/edit&id=' . $dossier_id . '&lot_id=' . $lot_id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupérer et nettoyer les données du formulaire
    $numero_dossier = isset($_POST['numero_dossier']) ? clean($_POST['numero_dossier']) : '';
    $numero_adherent = isset($_POST['numero_adherent']) ? clean($_POST['numero_adherent']) : '';
    $nom = isset($_POST['nom']) ? clean($_POST['nom']) : '';
    $beneficiaire = isset($_POST['beneficiaire']) ? clean($_POST['beneficiaire']) : '';
    $acte_id = isset($_POST['acte_id']) ? intval($_POST['acte_id']) : 0;
    $montant = isset($_POST['montant']) ? floatval(str_replace(',', '.', $_POST['montant'])) : 0;
    $numero_bon = isset($_POST['numero_bon']) ? clean($_POST['numero_bon']) : '';
    $recu = isset($_POST['recu']) ? 1 : 0;
    $notes = isset($_POST['notes']) ? clean($_POST['notes']) : '';

    // Valider les données
    $errors = [];

    if (empty($numero_dossier)) {
        $errors[] = 'Le numéro de dossier est obligatoire.';
    }

    // Vérifier si le numéro de dossier est unique (sauf pour ce dossier)
    $sql = "SELECT COUNT(*) as count FROM dossiers WHERE numero_dossier = ? AND id != ?";
    $result = $db->single($sql, [$numero_dossier, $dossier_id]);
    if ($result['count'] > 0) {
        $errors[] = 'Ce numéro de dossier existe déjà. Veuillez en choisir un autre.';
    }

    // Le numéro d'adhérent n'est plus obligatoire
    // if (empty($numero_adherent)) {
    //     $errors[] = 'Le numéro d\'adhérent est obligatoire.';
    // }

    if (empty($nom)) {
        $errors[] = 'Le nom est obligatoire.';
    }

    // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = $_POST;
        redirect('index.php?page=dossiers/edit&id=' . $dossier_id . '&lot_id=' . $lot_id, 'Veuillez corriger les erreurs.', 'danger');
    }

    try {
        // Mettre à jour le dossier dans la base de données
        $sql = "UPDATE dossiers
                SET numero_dossier = ?, numero_adherent = ?, nom = ?, beneficiaire = ?,
                    acte_id = ?, montant = ?, numero_bon = ?, recu = ?, notes = ?,
                    updated_at = NOW()
                WHERE id = ?";
        $params = [
            $numero_dossier, $numero_adherent, $nom, $beneficiaire,
            $acte_id, $montant, $numero_bon, $recu, $notes,
            $dossier_id
        ];
        $db->query($sql, $params);

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Modification de dossier', "Dossier #$numero_dossier modifié");

        // Mettre à jour le statut du lot si nécessaire
        if ($dossier['recu'] != $recu) {
            // Vérifier si tous les dossiers sont reçus
            $sql = "SELECT COUNT(*) as total, SUM(recu) as recus FROM dossiers WHERE lot_id = ?";
            $result = $db->single($sql, [$lot_id]);

            if ($result['total'] > 0 && $result['total'] == $result['recus']) {
                // Tous les dossiers sont reçus, mettre à jour le statut du lot
                $sql = "UPDATE lots SET statut = 'recu', updated_at = NOW() WHERE id = ? AND statut = 'en_attente'";
                $db->query($sql, [$lot_id]);

                logActivity($_SESSION['user_id'], 'Mise à jour de lot', "Statut du lot mis à jour: en_attente -> recu");
            }
        }

        // Rediriger vers la page de détails du lot
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Le dossier a été modifié avec succès.', 'success');

    } catch (Exception $e) {
        redirect('index.php?page=dossiers/edit&id=' . $dossier_id . '&lot_id=' . $lot_id, 'Erreur lors de la modification du dossier: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'numero_dossier' => $dossier['numero_dossier'],
    'numero_adherent' => $dossier['numero_adherent'],
    'nom' => $dossier['nom'],
    'beneficiaire' => $dossier['beneficiaire'],
    'acte_id' => $dossier['acte_id'],
    'montant' => $dossier['montant'],
    'numero_bon' => $dossier['numero_bon'],
    'recu' => $dossier['recu'],
    'notes' => $dossier['notes']
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Obtenir la liste des actes
$actes_list = getActesList();

// Charger la vue
require_once VIEWS_PATH . 'dossiers/edit.php';
?>
