<?php
// Vue pour afficher les détails d'un lot
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-layer-group"></i> Lot #<?php echo $lot['numero_lot']; ?></h1>
        <div>
            <a href="index.php?page=lots/edit&id=<?php echo $lot_id; ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Modifier
            </a>
            <a href="index.php?page=lots/print&id=<?php echo $lot_id; ?>" class="btn btn-info">
                <i class="fas fa-print"></i> Imprimer
            </a>
            <div class="btn-group">
                <a href="index.php?page=lots/export_single&id=<?php echo $lot_id; ?>&format=csv" class="btn btn-success">
                    <i class="fas fa-file-excel"></i> Exporter Excel
                </a>
                <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="visually-hidden">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="index.php?page=lots/export_single&id=<?php echo $lot_id; ?>&format=csv">Format CSV</a></li>
                    <li><a class="dropdown-item" href="index.php?page=lots/export_single&id=<?php echo $lot_id; ?>&format=xlsx">Format XLSX</a></li>
                </ul>
            </div>
            <a href="index.php?page=lots" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>

    <?php if (!empty($form_errors)): ?>
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle"></i> Erreurs:</h5>
            <ul class="mb-0">
                <?php foreach ($form_errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="row mb-4">
        <!-- Informations du lot -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Informations du lot</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <th style="width: 40%">Numéro de lot</th>
                                <td><?php echo $lot['numero_lot']; ?></td>
                            </tr>
                            <tr>
                                <th>CDM</th>
                                <td><?php echo $lot['cdm_nom']; ?></td>
                            </tr>
                            <tr>
                                <th>Date de demande</th>
                                <td><?php echo formatDate($lot['date_demande']); ?></td>
                            </tr>
                            <tr>
                                <th>Date de réception</th>
                                <td><?php echo formatDate($lot['date_reception']); ?></td>
                            </tr>
                            <tr>
                                <th>Statut</th>
                                <td>
                                    <span class="badge bg-<?php
                                        echo $lot['statut'] === 'en_attente' ? 'warning' :
                                            ($lot['statut'] === 'recu' ? 'info' :
                                            ($lot['statut'] === 'traite' ? 'success' : 'secondary'));
                                    ?>">
                                        <?php
                                            echo $lot['statut'] === 'en_attente' ? 'En attente' :
                                                ($lot['statut'] === 'recu' ? 'Reçu' :
                                                ($lot['statut'] === 'traite' ? 'Traité' : 'Archivé'));
                                        ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>Notes</th>
                                <td><?php echo nl2br($lot['notes']); ?></td>
                            </tr>
                            <tr>
                                <th>Créé par</th>
                                <td><?php echo $lot['created_by_username']; ?></td>
                            </tr>
                            <tr>
                                <th>Date de création</th>
                                <td><?php echo formatDate($lot['created_at'], DATETIME_FORMAT); ?></td>
                            </tr>
                            <tr>
                                <th>Dernière mise à jour</th>
                                <td><?php echo formatDate($lot['updated_at'], DATETIME_FORMAT); ?></td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Formulaire de mise à jour du statut -->
                    <form method="post" action="index.php?page=lots/view&id=<?php echo $lot_id; ?>" class="mt-3">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="update_status">

                        <div class="input-group">
                            <select class="form-select" name="statut">
                                <option value="en_attente" <?php echo $lot['statut'] === 'en_attente' ? 'selected' : ''; ?>>En attente</option>
                                <option value="recu" <?php echo $lot['statut'] === 'recu' ? 'selected' : ''; ?>>Reçu</option>
                                <option value="traite" <?php echo $lot['statut'] === 'traite' ? 'selected' : ''; ?>>Traité</option>
                                <option value="archive" <?php echo $lot['statut'] === 'archive' ? 'selected' : ''; ?>>Archivé</option>
                            </select>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Mettre à jour le statut
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Statistiques du lot -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Statistiques du lot</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total dossiers</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo $total_dossiers; ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Reçus</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo $dossiers_recus; ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-warning">
                                <div class="card-body text-center">
                                    <h6 class="card-title">En attente</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo $dossiers_en_attente; ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if ($total_dossiers > 0): ?>
                        <div class="progress mt-3" style="height: 25px;">
                            <?php
                                $recu_percent = ($dossiers_recus / $total_dossiers) * 100;
                                $attente_percent = 100 - $recu_percent;
                            ?>
                            <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $recu_percent; ?>%;" aria-valuenow="<?php echo $recu_percent; ?>" aria-valuemin="0" aria-valuemax="100">
                                <?php echo round($recu_percent); ?>% Reçus
                            </div>
                            <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo $attente_percent; ?>%;" aria-valuenow="<?php echo $attente_percent; ?>" aria-valuemin="0" aria-valuemax="100">
                                <?php echo round($attente_percent); ?>% En attente
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-info-circle"></i> Informations</h6>
                        <ul class="mb-0">
                            <li>Lot créé le <?php echo formatDate($lot['created_at'], DATETIME_FORMAT); ?></li>
                            <?php if (!empty($lot['date_reception'])): ?>
                                <li>Reçu le <?php echo formatDate($lot['date_reception']); ?></li>
                            <?php endif; ?>
                            <li>
                                <?php if ($total_dossiers === 0): ?>
                                    Aucun dossier enregistré
                                <?php else: ?>
                                    <?php echo $total_dossiers; ?> dossier(s) au total
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire d'ajout de dossier -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-plus-circle"></i> Ajouter un dossier</h5>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=lots/view&id=<?php echo $lot_id; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="add_dossier">

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="numero_dossier" class="form-label required">Numéro de dossier</label>
                        <input type="text" class="form-control" id="numero_dossier" name="numero_dossier" value="<?php echo $form_data['numero_dossier']; ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="numero_adherent" class="form-label">Numéro d'adhérent</label>
                        <input type="text" class="form-control" id="numero_adherent" name="numero_adherent" value="<?php echo $form_data['numero_adherent']; ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="numero_bon" class="form-label">Numéro de bon</label>
                        <input type="text" class="form-control" id="numero_bon" name="numero_bon" value="<?php echo $form_data['numero_bon']; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="nom" class="form-label required">Nom</label>
                        <input type="text" class="form-control" id="nom" name="nom" value="<?php echo $form_data['nom']; ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="beneficiaire" class="form-label">Bénéficiaire</label>
                        <input type="text" class="form-control" id="beneficiaire" name="beneficiaire" value="<?php echo $form_data['beneficiaire']; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="actes" class="form-label">Actes</label>
                        <select class="form-select" id="actes" multiple size="5">
                            <?php if (!empty($actes_list)): ?>
                                <?php foreach ($actes_list as $acte):
                                    $prix = isset($acte['prix']) ? $acte['prix'] : 0;
                                ?>
                                    <option value="<?php echo $acte['id']; ?>" data-prix="<?php echo $prix; ?>">
                                        <?php echo $acte['nom']; ?> (<?php echo number_format($prix, 2, ',', ' '); ?> DH)
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-primary" id="add-acte">Ajouter</button>
                            <button type="button" class="btn btn-sm btn-danger" id="remove-acte">Supprimer</button>
                        </div>
                        <div class="mt-2">
                            <div id="selected-actes" class="border p-2 rounded" style="min-height: 60px;">
                                <!-- Les actes sélectionnés seront affichés ici -->
                            </div>
                            <input type="hidden" name="acte_ids" id="acte_ids" value="">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="montant" class="form-label">Montant</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="montant" name="montant" value="<?php echo $form_data['montant']; ?>" readonly>
                            <span class="input-group-text">DH</span>
                        </div>
                        <small class="text-muted">Calculé automatiquement à partir des actes sélectionnés</small>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Statut de réception</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="recu" id="recu_non" value="0" <?php echo $form_data['recu'] === '0' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="recu_non">
                                <span class="badge bg-warning">Non reçu</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="recu" id="recu_oui" value="1" <?php echo $form_data['recu'] === '1' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="recu_oui">
                                <span class="badge bg-success">Reçu</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="2"><?php echo $form_data['notes']; ?></textarea>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus-circle"></i> Ajouter le dossier
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des dossiers -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-folder"></i> Dossiers du lot</h5>
                <span class="badge bg-light text-dark"><?php echo count($dossiers); ?> dossier(s)</span>
            </div>
        </div>
        <div class="card-body">
            <?php if (count($dossiers) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>N° Dossier</th>
                                <th>N° Adhérent</th>
                                <th>Nom</th>
                                <th>Bénéficiaire</th>
                                <th>Acte</th>
                                <th>Montant</th>
                                <th>N° Bon</th>
                                <th>Statut</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tfoot>
                            <tr class="table-primary">
                                <th colspan="5" class="text-end">Total:</th>
                                <th>
                                    <?php
                                        $total = 0;
                                        foreach ($dossiers as $dossier) {
                                            $total += $dossier['montant'];
                                        }
                                        echo number_format($total, 2, ',', ' ') . ' DH';
                                    ?>
                                </th>
                                <th colspan="3"></th>
                            </tr>
                        </tfoot>
                        <tbody>
                            <?php foreach ($dossiers as $dossier): ?>
                                <tr>
                                    <td><?php echo $dossier['numero_dossier']; ?></td>
                                    <td><?php echo $dossier['numero_adherent']; ?></td>
                                    <td><?php echo $dossier['nom']; ?></td>
                                    <td><?php echo $dossier['beneficiaire']; ?></td>
                                    <td><?php echo $dossier['acte_nom']; ?></td>
                                    <td><?php echo !empty($dossier['montant']) ? number_format($dossier['montant'], 2, ',', ' ') . ' DH' : ''; ?></td>
                                    <td><?php echo $dossier['numero_bon']; ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $dossier['recu'] ? 'success' : 'warning'; ?>">
                                            <?php echo $dossier['recu'] ? 'Reçu' : 'Non reçu'; ?>
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        <a href="index.php?page=dossiers/edit&id=<?php echo $dossier['id']; ?>" class="btn btn-sm btn-warning btn-action" data-bs-toggle="tooltip" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="index.php?page=dossiers/delete&id=<?php echo $dossier['id']; ?>&lot_id=<?php echo $lot_id; ?>" class="btn btn-sm btn-danger btn-action confirm-delete" data-bs-toggle="tooltip" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun dossier n'a été ajouté à ce lot.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
