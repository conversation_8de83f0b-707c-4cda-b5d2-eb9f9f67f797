<?php
// Contrôleur pour la suppression d'un utilisateur

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Vérifier si l'ID est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=admin/users', 'ID de l\'utilisateur non spécifié.', 'danger');
}

$id = intval($_GET['id']);

// Titre de la page
$page_title = 'Supprimer un utilisateur';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations de l'utilisateur
$sql = "SELECT * FROM users WHERE id = ?";
$user = $db->single($sql, [$id]);

if (!$user) {
    redirect('index.php?page=admin/users', 'Utilisateur non trouvé.', 'danger');
}

// Empêcher la suppression de son propre compte
if ($id === $_SESSION['user_id']) {
    redirect('index.php?page=admin/users', 'Vous ne pouvez pas supprimer votre propre compte.', 'danger');
}

// Vérifier si l'utilisateur est le dernier administrateur
if ($user['role'] === 'admin') {
    $sql = "SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND id != ?";
    $result = $db->single($sql, [$id]);
    
    if ($result['count'] === 0) {
        redirect('index.php?page=admin/users', 'Vous ne pouvez pas supprimer le dernier administrateur.', 'danger');
    }
}

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=admin/users/delete&id=' . $id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }
    
    try {
        // Supprimer l'utilisateur
        $sql = "DELETE FROM users WHERE id = ?";
        $db->query($sql, [$id]);
        
        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Suppression d\'un utilisateur', 'ID: ' . $id . ', Utilisateur: ' . $user['username']);
        
        redirect('index.php?page=admin/users', 'L\'utilisateur a été supprimé avec succès.', 'success');
    } catch (Exception $e) {
        redirect('index.php?page=admin/users/delete&id=' . $id, 'Erreur lors de la suppression: ' . $e->getMessage(), 'danger');
    }
}

// Charger la vue
require_once VIEWS_PATH . 'admin/users/delete.php';
?>
