<?php
// Contrôleur pour la modification d'un lot

// Vérifier si l'ID du lot est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=lots', 'ID du lot non spécifié.', 'danger');
}

$lot_id = intval($_GET['id']);

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du lot
$sql = "SELECT l.*, c.nom as cdm_nom
        FROM lots l
        JOIN cdm c ON l.cdm_id = c.id
        WHERE l.id = ?";
$lot = $db->single($sql, [$lot_id]);

// Vérifier si le lot existe
if (!$lot) {
    redirect('index.php?page=lots', 'Lot non trouvé.', 'danger');
}

// Titre de la page
$page_title = 'Modifier le lot #' . $lot['numero_lot'];
$page = 'lots/edit';

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=lots/edit&id=' . $lot_id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupérer et nettoyer les données du formulaire
    $numero_lot = isset($_POST['numero_lot']) ? clean($_POST['numero_lot']) : '';
    $cdm_id = isset($_POST['cdm_id']) ? intval($_POST['cdm_id']) : 0;
    $date_demande = isset($_POST['date_demande']) ? clean($_POST['date_demande']) : '';
    $date_reception = isset($_POST['date_reception']) ? clean($_POST['date_reception']) : '';
    $statut = isset($_POST['statut']) ? clean($_POST['statut']) : '';
    $notes = isset($_POST['notes']) ? clean($_POST['notes']) : '';

    // Valider les données
    $errors = [];

    if (empty($numero_lot)) {
        $errors[] = 'Le numéro de lot est obligatoire.';
    } else {
        // Vérifier si le numéro de lot existe déjà (sauf pour ce lot)
        $sql = "SELECT COUNT(*) as count FROM lots WHERE numero_lot = ? AND id != ?";
        $result = $db->single($sql, [$numero_lot, $lot_id]);

        if ($result['count'] > 0) {
            $errors[] = 'Ce numéro de lot existe déjà. Veuillez en choisir un autre.';
        }
    }

    if ($cdm_id <= 0) {
        $errors[] = 'Veuillez sélectionner un CDM.';
    }

    if (empty($date_demande)) {
        $errors[] = 'La date de demande est requise.';
    } else {
        // Convertir la date au format MySQL (YYYY-MM-DD)
        $date_demande = date('Y-m-d', strtotime(str_replace('/', '-', $date_demande)));
    }

    // Convertir la date de réception si elle est fournie
    if (!empty($date_reception)) {
        $date_reception = date('Y-m-d', strtotime(str_replace('/', '-', $date_reception)));
    } else {
        $date_reception = null;
    }

    // Valider le statut
    if (empty($statut) || !in_array($statut, ['en_attente', 'recu', 'traite', 'archive'])) {
        $errors[] = 'Le statut sélectionné n\'est pas valide.';
    }

    // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = $_POST;
        redirect('index.php?page=lots/edit&id=' . $lot_id, 'Veuillez corriger les erreurs.', 'danger');
    }

    try {
        // Mettre à jour le lot dans la base de données
        $sql = "UPDATE lots 
                SET numero_lot = ?, cdm_id = ?, date_demande = ?, date_reception = ?, 
                    statut = ?, notes = ?, updated_at = NOW()
                WHERE id = ?";
        $params = [$numero_lot, $cdm_id, $date_demande, $date_reception, $statut, $notes, $lot_id];
        $db->query($sql, $params);

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Modification de lot', "Lot #$numero_lot modifié");

        // Rediriger vers la page de détails du lot
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Le lot a été modifié avec succès.', 'success');

    } catch (Exception $e) {
        redirect('index.php?page=lots/edit&id=' . $lot_id, 'Erreur lors de la modification du lot: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'numero_lot' => $lot['numero_lot'],
    'cdm_id' => $lot['cdm_id'],
    'date_demande' => formatDate($lot['date_demande']),
    'date_reception' => formatDate($lot['date_reception']),
    'statut' => $lot['statut'],
    'notes' => $lot['notes']
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Obtenir la liste des CDM
$cdm_list = getCDMList();

// Charger la vue
require_once VIEWS_PATH . 'lots/edit.php';
?>
