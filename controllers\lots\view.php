<?php
// Contrôleur pour afficher les détails d'un lot

// Vérifier si l'ID du lot est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=lots', 'ID du lot non spécifié.', 'danger');
}

$lot_id = intval($_GET['id']);

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du lot
$sql = "SELECT l.*, c.nom as cdm_nom, u.username as created_by_username
        FROM lots l
        JOIN cdm c ON l.cdm_id = c.id
        JOIN users u ON l.created_by = u.id
        WHERE l.id = ?";
$lot = $db->single($sql, [$lot_id]);

// Vérifier si le lot existe
if (!$lot) {
    redirect('index.php?page=lots', 'Lot non trouvé.', 'danger');
}

// Titre de la page
$page_title = 'Lot #' . $lot['numero_lot'];
$page = 'lots/view';

// Récupérer les dossiers associés à ce lot
$sql = "SELECT d.*, a.nom as acte_nom
        FROM dossiers d
        LEFT JOIN actes a ON d.acte_id = a.id
        WHERE d.lot_id = ?
        ORDER BY d.created_at DESC";
$dossiers = $db->all($sql, [$lot_id]);

// Statistiques du lot
$total_dossiers = count($dossiers);
$dossiers_recus = 0;
$dossiers_en_attente = 0;

foreach ($dossiers as $dossier) {
    if ($dossier['recu']) {
        $dossiers_recus++;
    } else {
        $dossiers_en_attente++;
    }
}

// Obtenir la liste des actes pour le formulaire d'ajout de dossier
// Vérifier si la colonne 'active' existe
$db = Database::getInstance();
try {
    $check_column = "SHOW COLUMNS FROM actes LIKE 'active'";
    $column_exists = $db->query($check_column)->rowCount() > 0;

    if ($column_exists) {
        $actes_list = $db->all("SELECT id, nom, prix FROM actes WHERE active = 1 ORDER BY nom");
    } else {
        $actes_list = $db->all("SELECT id, nom, prix FROM actes ORDER BY nom");
    }
} catch (Exception $e) {
    // En cas d'erreur, récupérer tous les actes sans condition
    $actes_list = $db->all("SELECT id, nom, prix FROM actes ORDER BY nom");
}

// Traitement du formulaire d'ajout de dossier
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_dossier') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupérer et nettoyer les données du formulaire
    $numero_dossier = clean($_POST['numero_dossier'] ?? '');
    $numero_adherent = clean($_POST['numero_adherent'] ?? '');
    $nom = clean($_POST['nom'] ?? '');
    $beneficiaire = clean($_POST['beneficiaire'] ?? '');
    $acte_ids = isset($_POST['acte_ids']) ? $_POST['acte_ids'] : '';
    $acte_id = 0; // On utilisera le premier acte comme acte principal

    // Si des actes sont sélectionnés, prendre le premier comme acte principal
    if (!empty($acte_ids)) {
        $acte_ids_array = explode(',', $acte_ids);
        if (!empty($acte_ids_array)) {
            $acte_id = intval($acte_ids_array[0]);
        }
    }

    $montant = isset($_POST['montant']) ? floatval(str_replace(',', '.', $_POST['montant'])) : 0;
    $numero_bon = clean($_POST['numero_bon'] ?? '');
    $recu = isset($_POST['recu']) && $_POST['recu'] === '1' ? 1 : 0;
    $notes = clean($_POST['notes'] ?? '');

    // Valider les données
    $errors = [];

    if (empty($numero_dossier)) {
        $errors[] = 'Le numéro de dossier est requis.';
    }

    // Vérifier si le numéro de dossier est unique
    $sql = "SELECT COUNT(*) as count FROM dossiers WHERE numero_dossier = ?";
    $result = $db->single($sql, [$numero_dossier]);
    if ($result['count'] > 0) {
        $errors[] = 'Ce numéro de dossier existe déjà. Veuillez en choisir un autre.';
    }

    // Le numéro d'adhérent n'est plus obligatoire
    // if (empty($numero_adherent)) {
    //     $errors[] = 'Le numéro d\'adhérent est requis.';
    // }

    if (empty($nom)) {
        $errors[] = 'Le nom est requis.';
    }

    // S'il y a des erreurs, rediriger vers la page avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = $_POST;
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Veuillez corriger les erreurs.', 'danger');
    }

    // Stocker les actes sélectionnés dans les notes si plusieurs actes
    if (!empty($acte_ids) && strpos($acte_ids, ',') !== false) {
        $actes_info = [];
        $acte_ids_array = explode(',', $acte_ids);

        foreach ($acte_ids_array as $aid) {
            foreach ($actes_list as $acte) {
                if ($acte['id'] == $aid) {
                    // Récupérer le prix à jour directement depuis la base de données
                    $acte_query = "SELECT prix FROM actes WHERE id = ?";
                    $acte_result = $db->single($acte_query, [$aid]);
                    $prix_value = isset($acte_result['prix']) ? $acte_result['prix'] : (isset($acte['prix']) ? $acte['prix'] : 0);
                    $prix = number_format($prix_value, 2, ',', ' ');
                    $actes_info[] = $acte['nom'] . ' (' . $prix . ' DH)';
                    break;
                }
            }
        }

        if (!empty($actes_info)) {
            $notes .= "\n\nActes sélectionnés: " . implode(', ', $actes_info);
        }
    }

    try {
        // Insérer le dossier dans la base de données
        $sql = "INSERT INTO dossiers (lot_id, numero_dossier, numero_adherent, nom, beneficiaire,
                                     acte_id, montant, numero_bon, recu, notes, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        $params = [$lot_id, $numero_dossier, $numero_adherent, $nom, $beneficiaire,
                  $acte_id ?: null, $montant, $numero_bon, $recu, $notes];
        $db->query($sql, $params);

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Ajout de dossier', "Dossier #$numero_dossier ajouté au lot #{$lot['numero_lot']}");

        // Mettre à jour le statut du lot si nécessaire
        if ($lot['statut'] === 'en_attente' && $recu) {
            $sql = "UPDATE lots SET statut = 'recu', date_reception = CURDATE(), updated_at = NOW() WHERE id = ?";
            $db->query($sql, [$lot_id]);

            logActivity($_SESSION['user_id'], 'Mise à jour de lot', "Statut du lot #{$lot['numero_lot']} mis à jour: en_attente -> recu");
        }

        // Rediriger vers la page de détails du lot
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Le dossier a été ajouté avec succès.', 'success');

    } catch (Exception $e) {
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Erreur lors de l\'ajout du dossier: ' . $e->getMessage(), 'danger');
    }
}

// Traitement du formulaire de mise à jour du statut du lot
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_status') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupérer le nouveau statut
    $new_status = clean($_POST['statut'] ?? '');

    // Valider le statut
    if (!in_array($new_status, ['en_attente', 'recu', 'traite', 'archive'])) {
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Statut invalide.', 'danger');
    }

    try {
        // Mettre à jour le statut du lot
        $sql = "UPDATE lots SET statut = ?, updated_at = NOW()";
        $params = [$new_status];

        // Si le statut passe à "reçu", mettre à jour la date de réception
        if ($new_status === 'recu' && $lot['statut'] !== 'recu') {
            $sql .= ", date_reception = CURDATE()";
        }

        $sql .= " WHERE id = ?";
        $params[] = $lot_id;

        $db->query($sql, $params);

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Mise à jour de lot', "Statut du lot #{$lot['numero_lot']} mis à jour: {$lot['statut']} -> $new_status");

        // Rediriger vers la page de détails du lot
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Le statut du lot a été mis à jour avec succès.', 'success');

    } catch (Exception $e) {
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Erreur lors de la mise à jour du statut: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'numero_dossier' => '',
    'numero_adherent' => '',
    'nom' => '',
    'beneficiaire' => '',
    'acte_id' => '',
    'montant' => '',
    'numero_bon' => '',
    'recu' => '0',
    'notes' => ''
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Charger la vue
require_once VIEWS_PATH . 'lots/view.php';
?>
