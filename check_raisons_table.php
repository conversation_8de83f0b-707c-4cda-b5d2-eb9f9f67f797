<?php
// Script pour vérifier la structure de la table raisons_retour
require_once 'config/config.php';
require_once 'config/database.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

try {
    // Vérifier la structure de la table raisons_retour
    $sql = "DESCRIBE raisons_retour";
    $result = $db->all($sql);
    
    echo "<h2>Structure de la table raisons_retour</h2>";
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    // Vérifier si la colonne 'active' existe
    $sql = "SHOW COLUMNS FROM raisons_retour LIKE 'active'";
    $result = $db->query($sql);
    $column_exists = $result->rowCount() > 0;
    
    if ($column_exists) {
        echo "<p>La colonne 'active' existe dans la table raisons_retour.</p>";
    } else {
        echo "<p>La colonne 'active' n'existe pas dans la table raisons_retour.</p>";
        
        // Ajouter la colonne 'active'
        $sql = "ALTER TABLE raisons_retour ADD COLUMN active TINYINT(1) NOT NULL DEFAULT 1";
        $db->query($sql);
        echo "<p>La colonne 'active' a été ajoutée à la table raisons_retour.</p>";
    }
    
    // Vérifier les données existantes
    $sql = "SELECT * FROM raisons_retour LIMIT 5";
    $data = $db->all($sql);
    
    echo "<h2>Exemples de données dans raisons_retour</h2>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage();
}
?>
