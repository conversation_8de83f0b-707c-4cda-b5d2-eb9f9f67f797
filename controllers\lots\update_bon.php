<?php
// Contrôleur pour la mise à jour du numéro de bon d'un dossier

// Vérifier si la méthode est POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    redirect('index.php?page=lots/search', 'Méthode non autorisée.', 'danger');
}

// Vérifier si les paramètres nécessaires sont fournis
if (!isset($_POST['dossier_id']) || empty($_POST['dossier_id'])) {
    redirect('index.php?page=lots/search', 'ID du dossier non spécifié.', 'danger');
}

$dossier_id = intval($_POST['dossier_id']);
$numero_bon = isset($_POST['numero_bon']) ? clean($_POST['numero_bon']) : '';
$return_url = isset($_POST['return_url']) ? $_POST['return_url'] : 'index.php?page=lots/search';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Vérifier si le dossier existe
$sql = "SELECT d.*, l.numero_lot, c.nom as cdm_nom
        FROM dossiers d
        JOIN lots l ON d.lot_id = l.id
        JOIN cdm c ON l.cdm_id = c.id
        WHERE d.id = ?";
$dossier = $db->single($sql, [$dossier_id]);

if (!$dossier) {
    redirect($return_url, 'Dossier non trouvé.', 'danger');
}

try {
    // Mettre à jour le numéro de bon du dossier
    $sql = "UPDATE dossiers SET numero_bon = ?, updated_at = NOW() WHERE id = ?";
    $db->query($sql, [$numero_bon, $dossier_id]);

    // Journaliser l'action
    logActivity($_SESSION['user_id'], 'Mise à jour du numéro de bon', "Dossier #{$dossier['numero_dossier']}, N° bon: $numero_bon");

    // Rediriger vers la page précédente avec l'ID du dossier mis à jour
    $return_url .= (strpos($return_url, '?') !== false ? '&' : '?') . 'updated_id=' . $dossier_id;
    redirect($return_url, 'Numéro de bon mis à jour avec succès.', 'success');
} catch (Exception $e) {
    redirect($return_url, 'Erreur lors de la mise à jour du numéro de bon: ' . $e->getMessage(), 'danger');
}
?>
