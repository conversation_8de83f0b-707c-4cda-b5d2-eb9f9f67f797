<?php
// Contrôleur pour la suppression d'un CDM

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Vérifier si l'ID est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=admin/cdm', 'ID du CDM non spécifié.', 'danger');
}

$id = intval($_GET['id']);

// Titre de la page
$page_title = 'Supprimer un CDM';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du CDM
$sql = "SELECT * FROM cdm WHERE id = ?";
$cdm = $db->single($sql, [$id]);

if (!$cdm) {
    redirect('index.php?page=admin/cdm', 'CDM non trouvé.', 'danger');
}

// Vérifier si le CDM est utilisé dans des lots
$sql = "SELECT COUNT(*) as total FROM lots WHERE cdm_id = ?";
$result = $db->single($sql, [$id]);

$is_used = ($result['total'] > 0);

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        redirect('index.php?page=admin/cdm', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }
    
    // Si le CDM est utilisé, on le désactive au lieu de le supprimer
    if ($is_used) {
        $sql = "UPDATE cdm SET active = 0, updated_at = NOW() WHERE id = ?";
        $params = [$id];
        
        if ($db->query($sql, $params)) {
            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Désactivation d\'un CDM', 'ID: ' . $id . ', Nom: ' . $cdm['nom']);
            
            redirect('index.php?page=admin/cdm', 'CDM désactivé avec succès.', 'success');
        } else {
            redirect('index.php?page=admin/cdm', 'Erreur lors de la désactivation du CDM.', 'danger');
        }
    } else {
        // Si le CDM n'est pas utilisé, on peut le supprimer
        $sql = "DELETE FROM cdm WHERE id = ?";
        $params = [$id];
        
        if ($db->query($sql, $params)) {
            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Suppression d\'un CDM', 'ID: ' . $id . ', Nom: ' . $cdm['nom']);
            
            redirect('index.php?page=admin/cdm', 'CDM supprimé avec succès.', 'success');
        } else {
            redirect('index.php?page=admin/cdm', 'Erreur lors de la suppression du CDM.', 'danger');
        }
    }
}

// Charger la vue
require_once VIEWS_PATH . 'admin/cdm/delete.php';
?>
