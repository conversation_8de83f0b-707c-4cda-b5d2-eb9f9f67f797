<?php
// Vue pour afficher les détails d'un bordereau de dossiers retournés
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-undo-alt"></i> Bordereau de retour #<?php echo $numero_bordereau; ?></h1>
        <div>
            <a href="index.php?page=retours/edit&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Modifier
            </a>
            <a href="index.php?page=retours/print&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>" class="btn btn-info">
                <i class="fas fa-print"></i> Imprimer
            </a>
            <div class="btn-group">
                <a href="index.php?page=retours/export&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>&format=csv" class="btn btn-success">
                    <i class="fas fa-file-excel"></i> Exporter Excel
                </a>
                <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="visually-hidden">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="index.php?page=retours/export&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>&format=csv">Format CSV</a></li>
                    <li><a class="dropdown-item" href="index.php?page=retours/export&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>&format=xlsx">Format XLSX</a></li>
                </ul>
            </div>
            <a href="index.php?page=retours" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Informations du bordereau -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Informations du bordereau</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <th style="width: 40%">Numéro de bordereau</th>
                                <td><?php echo $numero_bordereau; ?></td>
                            </tr>
                            <tr>
                                <th>CDM</th>
                                <td><?php echo $cdm['nom']; ?></td>
                            </tr>
                            <tr>
                                <th>Date du bordereau</th>
                                <td><?php echo formatDate($bordereau_info['date_bordereau']); ?></td>
                            </tr>
                            <tr>
                                <th>Adresse CDM</th>
                                <td><?php echo $cdm['adresse']; ?></td>
                            </tr>
                            <tr>
                                <th>Contact CDM</th>
                                <td><?php echo $cdm['contact_nom']; ?></td>
                            </tr>
                            <tr>
                                <th>Téléphone CDM</th>
                                <td><?php echo $cdm['telephone']; ?></td>
                            </tr>
                            <tr>
                                <th>Email CDM</th>
                                <td><?php echo $cdm['email']; ?></td>
                            </tr>
                            <tr>
                                <th>Créé par</th>
                                <td><?php echo $bordereau_info['created_by_username']; ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Statistiques du bordereau -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Statistiques du bordereau</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total dossiers</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo $total_dossiers; ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Corrigés</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo $dossiers_corriges; ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Non corrigés</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo $dossiers_non_corriges; ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if ($total_dossiers > 0): ?>
                        <div class="progress mt-3" style="height: 25px;">
                            <?php
                                $corrige_percent = ($dossiers_corriges / $total_dossiers) * 100;
                                $non_corrige_percent = 100 - $corrige_percent;
                            ?>
                            <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $corrige_percent; ?>%;" aria-valuenow="<?php echo $corrige_percent; ?>" aria-valuemin="0" aria-valuemax="100">
                                <?php echo round($corrige_percent); ?>% Corrigés
                            </div>
                            <div class="progress-bar bg-danger" role="progressbar" style="width: <?php echo $non_corrige_percent; ?>%;" aria-valuenow="<?php echo $non_corrige_percent; ?>" aria-valuemin="0" aria-valuemax="100">
                                <?php echo round($non_corrige_percent); ?>% Non corrigés
                            </div>
                        </div>

                        <!-- Principaux motifs de retour -->
                        <?php if (!empty($raisons_count)): ?>
                            <div class="mt-4">
                                <h6>Principaux motifs de retour:</h6>
                                <ul class="list-group">
                                    <?php
                                        $count = 0;
                                        foreach ($raisons_count as $raison => $nombre) {
                                            if ($count >= 3) break; // Limiter à 3 motifs
                                            $percent = ($nombre / $total_dossiers) * 100;
                                            echo '<li class="list-group-item d-flex justify-content-between align-items-center">';
                                            echo $raison;
                                            echo '<span class="badge bg-primary rounded-pill">' . $nombre . ' (' . round($percent) . '%)</span>';
                                            echo '</li>';
                                            $count++;
                                        }
                                    ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-info-circle"></i> Informations</h6>
                        <ul class="mb-0">
                            <li>Bordereau créé le <?php echo formatDate($bordereau_info['date_bordereau']); ?></li>
                            <li>Montant total: <?php echo number_format($montant_total, 2, ',', ' '); ?> DH</li>
                            <?php if ($total_dossiers > 0): ?>
                                <li>Taux de correction: <?php echo round($corrige_percent); ?>%</li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire d'ajout de dossier retourné -->
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-plus-circle"></i> Ajouter un dossier retourné</h5>
        </div>
        <div class="card-body">
            <?php if (isset($_SESSION['form_errors']) && !empty($_SESSION['form_errors'])): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle"></i> Erreurs:</h5>
                    <ul class="mb-0">
                        <?php foreach ($_SESSION['form_errors'] as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php unset($_SESSION['form_errors']); ?>
            <?php endif; ?>

            <form method="post" action="index.php?page=retours/add_dossier">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                <input type="hidden" name="numero_bordereau" value="<?php echo $numero_bordereau; ?>">
                <input type="hidden" name="cdm_id" value="<?php echo $cdm_id; ?>">
                <input type="hidden" name="date_bordereau" value="<?php echo $bordereau_info['date_bordereau']; ?>">

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="numero_dossier" class="form-label required">Numéro de dossier</label>
                        <input type="text" class="form-control" id="numero_dossier" name="numero_dossier" value="<?php echo $form_data['numero_dossier']; ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="numero_adherent" class="form-label required">Numéro d'adhérent</label>
                        <input type="text" class="form-control" id="numero_adherent" name="numero_adherent" value="<?php echo $form_data['numero_adherent']; ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="raison_retour_id" class="form-label required">Motif de retour</label>
                        <select class="form-select" id="raison_retour_id" name="raison_retour_id" required>
                            <option value="">Sélectionnez une raison</option>
                            <?php foreach (getRaisonsRetourList() as $raison): ?>
                                <option value="<?php echo $raison['id']; ?>" <?php echo $form_data['raison_retour_id'] == $raison['id'] ? 'selected' : ''; ?>><?php echo $raison['raison']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="nom" class="form-label required">Nom</label>
                        <input type="text" class="form-control" id="nom" name="nom" value="<?php echo $form_data['nom']; ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="beneficiaire" class="form-label">Bénéficiaire</label>
                        <input type="text" class="form-control" id="beneficiaire" name="beneficiaire" value="<?php echo $form_data['beneficiaire']; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="acte_id" class="form-label">Acte</label>
                        <select class="form-select" id="acte_id" name="acte_id">
                            <option value="">Sélectionnez un acte</option>
                            <?php foreach (getActesList() as $acte): ?>
                                <option value="<?php echo $acte['id']; ?>" <?php echo $form_data['acte_id'] == $acte['id'] ? 'selected' : ''; ?>><?php echo $acte['nom']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="montant" class="form-label">Montant (DH)</label>
                        <input type="number" class="form-control" id="montant" name="montant" step="0.01" min="0" value="<?php echo $form_data['montant']; ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="date_retour" class="form-label required">Date de retour</label>
                        <input type="text" class="form-control datepicker" id="date_retour" name="date_retour" required value="<?php echo $form_data['date_retour']; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="date_correction" class="form-label">Date de correction</label>
                        <input type="text" class="form-control datepicker" id="date_correction" name="date_correction" value="<?php echo $form_data['date_correction']; ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Statut de correction</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="corrige" id="non_corrige" value="0" <?php echo $form_data['corrige'] == '0' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="non_corrige">
                                <span class="badge bg-danger">Non corrigé</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="corrige" id="corrige" value="1" <?php echo $form_data['corrige'] == '1' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="corrige">
                                <span class="badge bg-success">Corrigé</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"><?php echo $form_data['notes']; ?></textarea>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-plus-circle"></i> Ajouter le dossier
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des dossiers retournés -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-folder"></i> Dossiers du bordereau</h5>
                <span class="badge bg-light text-dark"><?php echo count($dossiers); ?> dossier(s)</span>
            </div>
        </div>
        <div class="card-body">
            <?php if (count($dossiers) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>N° Dossier</th>
                                <th>N° Adhérent</th>
                                <th>Nom</th>
                                <th>Bénéficiaire</th>
                                <th>Acte</th>
                                <th>Montant</th>
                                <th>Motif de retour</th>
                                <th>Date retour</th>
                                <th>Date correction</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dossiers as $dossier): ?>
                                <tr>
                                    <td><?php echo $dossier['numero_dossier']; ?></td>
                                    <td><?php echo $dossier['numero_adherent']; ?></td>
                                    <td><?php echo $dossier['nom']; ?></td>
                                    <td><?php echo $dossier['beneficiaire']; ?></td>
                                    <td><?php echo $dossier['acte_nom']; ?></td>
                                    <td><?php echo !empty($dossier['montant']) ? number_format($dossier['montant'], 2, ',', ' ') . ' DH' : ''; ?></td>
                                    <td><?php echo $dossier['raison_retour_nom']; ?></td>
                                    <td><?php echo formatDate($dossier['date_retour']); ?></td>
                                    <td><?php echo formatDate($dossier['date_correction']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $dossier['corrige'] ? 'success' : 'danger'; ?>">
                                            <?php echo $dossier['corrige'] ? 'Corrigé' : 'Non corrigé'; ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="5" class="text-end">Total:</th>
                                <th><?php echo number_format($montant_total, 2, ',', ' '); ?> DH</th>
                                <th colspan="4"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun dossier n'a été ajouté à ce bordereau.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
