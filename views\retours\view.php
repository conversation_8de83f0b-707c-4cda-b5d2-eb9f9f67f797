<?php
// Vue pour afficher les détails d'un bordereau de dossiers retournés
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container-fluid">
    <!-- En-tête avec gradient et design moderne -->
    <div class="card mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 15px;">
        <div class="card-body text-white">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2" style="font-weight: 600;">
                        <i class="fas fa-file-alt me-2"></i>Bordereau de Retour
                        <span class="badge bg-light text-dark ms-2" style="font-size: 0.7em;">#<?php echo $numero_bordereau; ?></span>
                    </h1>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-building me-1"></i><?php echo $cdm['nom']; ?> •
                        <i class="fas fa-calendar me-1"></i><?php echo formatDate($bordereau_info['date_bordereau']); ?>
                    </p>
                </div>
                <div class="text-end">
                    <div class="btn-group mb-2" role="group">
                        <a href="index.php?page=retours/edit&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>"
                           class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Modifier
                        </a>
                        <a href="index.php?page=retours/print&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>"
                           class="btn btn-info btn-sm">
                            <i class="fas fa-print"></i> Imprimer
                        </a>
                    </div>
                    <div class="btn-group mb-2" role="group">
                        <button type="button" class="btn btn-success btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i> Exporter
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="index.php?page=retours/export&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>&format=csv">
                                <i class="fas fa-file-csv me-2"></i>Format CSV
                            </a></li>
                            <li><a class="dropdown-item" href="index.php?page=retours/export&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>&format=xlsx">
                                <i class="fas fa-file-excel me-2"></i>Format Excel
                            </a></li>
                        </ul>
                    </div>
                    <a href="index.php?page=retours" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Informations du bordereau -->
        <div class="col-lg-4">
            <div class="card h-100 shadow-sm" style="border-radius: 12px;">
                <div class="card-header" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 12px 12px 0 0;">
                    <h5 class="mb-0 text-white"><i class="fas fa-info-circle me-2"></i>Informations du Bordereau</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="d-flex align-items-center p-2 bg-light rounded">
                                <i class="fas fa-hashtag text-primary me-2"></i>
                                <div>
                                    <small class="text-muted">Numéro</small>
                                    <div class="fw-bold"><?php echo $numero_bordereau; ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex align-items-center p-2 bg-light rounded">
                                <i class="fas fa-building text-success me-2"></i>
                                <div>
                                    <small class="text-muted">CDM</small>
                                    <div class="fw-bold"><?php echo $cdm['nom']; ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex align-items-center p-2 bg-light rounded">
                                <i class="fas fa-calendar text-warning me-2"></i>
                                <div>
                                    <small class="text-muted">Date</small>
                                    <div class="fw-bold"><?php echo formatDate($bordereau_info['date_bordereau']); ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex align-items-center p-2 bg-light rounded">
                                <i class="fas fa-user text-info me-2"></i>
                                <div>
                                    <small class="text-muted">Créé par</small>
                                    <div class="fw-bold"><?php echo $bordereau_info['created_by_username']; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations CDM détaillées -->
                    <div class="mt-3">
                        <h6 class="text-muted mb-2"><i class="fas fa-address-card me-1"></i>Détails CDM</h6>
                        <div class="small">
                            <?php if (!empty($cdm['adresse'])): ?>
                                <div class="mb-1"><i class="fas fa-map-marker-alt text-danger me-1"></i><?php echo $cdm['adresse']; ?></div>
                            <?php endif; ?>
                            <?php if (!empty($cdm['telephone'])): ?>
                                <div class="mb-1"><i class="fas fa-phone text-success me-1"></i><?php echo $cdm['telephone']; ?></div>
                            <?php endif; ?>
                            <?php if (!empty($cdm['email'])): ?>
                                <div class="mb-1"><i class="fas fa-envelope text-primary me-1"></i><?php echo $cdm['email']; ?></div>
                            <?php endif; ?>
                            <?php if (!empty($cdm['contact_nom'])): ?>
                                <div class="mb-1"><i class="fas fa-user-tie text-info me-1"></i><?php echo $cdm['contact_nom']; ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistiques du bordereau -->
        <div class="col-lg-8">
            <div class="card h-100 shadow-sm" style="border-radius: 12px;">
                <div class="card-header" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); border-radius: 12px 12px 0 0;">
                    <h5 class="mb-0 text-white"><i class="fas fa-chart-pie me-2"></i>Tableau de Bord & Statistiques</h5>
                </div>
                <div class="card-body">
                    <!-- Cartes de statistiques -->
                    <div class="row g-3 mb-4">
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px;">
                                <div class="card-body text-center text-white">
                                    <i class="fas fa-folder-open fa-2x mb-2 opacity-75"></i>
                                    <h6 class="card-title mb-1">Total Dossiers</h6>
                                    <h2 class="mb-0 fw-bold"><?php echo $total_dossiers; ?></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); border-radius: 10px;">
                                <div class="card-body text-center text-white">
                                    <i class="fas fa-check-circle fa-2x mb-2 opacity-75"></i>
                                    <h6 class="card-title mb-1">Corrigés</h6>
                                    <h2 class="mb-0 fw-bold"><?php echo $dossiers_corriges; ?></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #fc4a1a 0%, #f7b733 100%); border-radius: 10px;">
                                <div class="card-body text-center text-white">
                                    <i class="fas fa-exclamation-circle fa-2x mb-2 opacity-75"></i>
                                    <h6 class="card-title mb-1">En Attente</h6>
                                    <h2 class="mb-0 fw-bold"><?php echo $dossiers_non_corriges; ?></h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if ($total_dossiers > 0): ?>
                        <!-- Barre de progression moderne -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">Progression des Corrections</h6>
                                <small class="text-muted"><?php echo $dossiers_corriges; ?>/<?php echo $total_dossiers; ?> dossiers</small>
                            </div>
                            <?php
                                $corrige_percent = ($dossiers_corriges / $total_dossiers) * 100;
                                $non_corrige_percent = 100 - $corrige_percent;
                            ?>
                            <div class="progress" style="height: 20px; border-radius: 10px;">
                                <div class="progress-bar" role="progressbar"
                                     style="width: <?php echo $corrige_percent; ?>%; background: linear-gradient(90deg, #11998e 0%, #38ef7d 100%);"
                                     aria-valuenow="<?php echo $corrige_percent; ?>" aria-valuemin="0" aria-valuemax="100">
                                    <?php if ($corrige_percent > 15): ?>
                                        <?php echo round($corrige_percent); ?>% Corrigés
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between mt-1">
                                <small class="text-success">✓ <?php echo round($corrige_percent); ?>% Corrigés</small>
                                <small class="text-warning">⏳ <?php echo round($non_corrige_percent); ?>% En attente</small>
                            </div>
                        </div>

                        <!-- Principaux motifs de retour -->
                        <?php if (!empty($raisons_count)): ?>
                            <div class="mt-4">
                                <h6 class="mb-3"><i class="fas fa-chart-bar me-2"></i>Principaux Motifs de Retour</h6>
                                <div class="row g-2">
                                    <?php
                                        $count = 0;
                                        $colors = ['primary', 'success', 'warning', 'info', 'secondary'];
                                        foreach ($raisons_count as $raison => $nombre) {
                                            if ($count >= 5) break; // Limiter à 5 motifs
                                            $percent = ($nombre / $total_dossiers) * 100;
                                            $color = $colors[$count % count($colors)];
                                            echo '<div class="col-12">';
                                            echo '<div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">';
                                            echo '<div class="d-flex align-items-center">';
                                            echo '<span class="badge bg-' . $color . ' me-2">' . ($count + 1) . '</span>';
                                            echo '<span class="small">' . htmlspecialchars($raison) . '</span>';
                                            echo '</div>';
                                            echo '<div class="text-end">';
                                            echo '<span class="fw-bold text-' . $color . '">' . $nombre . '</span>';
                                            echo '<small class="text-muted ms-1">(' . round($percent) . '%)</small>';
                                            echo '</div>';
                                            echo '</div>';
                                            echo '</div>';
                                            $count++;
                                        }
                                    ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Résumé financier -->
                    <div class="mt-4 p-3 rounded" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="mb-1"><i class="fas fa-coins me-2"></i>Résumé Financier</h6>
                                <div class="small opacity-75">
                                    Bordereau créé le <?php echo formatDate($bordereau_info['date_bordereau']); ?>
                                    <?php if ($total_dossiers > 0): ?>
                                        • Taux de correction: <?php echo round($corrige_percent); ?>%
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="h4 mb-0 fw-bold"><?php echo number_format($montant_total, 2, ',', ' '); ?> DH</div>
                                <small class="opacity-75">Montant total</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire d'ajout de dossier retourné -->
    <div class="card mb-4 shadow-sm" style="border-radius: 12px; border: none;">
        <div class="card-header text-white" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); border-radius: 12px 12px 0 0;">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Ajouter un Nouveau Dossier</h5>
                <button class="btn btn-outline-light btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#addDossierForm" aria-expanded="true">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
        </div>
        <div class="collapse show" id="addDossierForm">
            <div class="card-body p-4">
            <?php if (isset($_SESSION['form_errors']) && !empty($_SESSION['form_errors'])): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle"></i> Erreurs:</h5>
                    <ul class="mb-0">
                        <?php foreach ($_SESSION['form_errors'] as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php unset($_SESSION['form_errors']); ?>
            <?php endif; ?>

            <form method="post" action="index.php?page=retours/add_dossier">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                <input type="hidden" name="numero_bordereau" value="<?php echo $numero_bordereau; ?>">
                <input type="hidden" name="cdm_id" value="<?php echo $cdm_id; ?>">
                <input type="hidden" name="date_bordereau" value="<?php echo $bordereau_info['date_bordereau']; ?>">

                <div class="row g-3 mb-4">
                    <div class="col-md-4">
                        <label for="numero_dossier" class="form-label fw-bold">
                            <i class="fas fa-folder text-primary me-1"></i>Numéro de dossier <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                            <input type="text" class="form-control" id="numero_dossier" name="numero_dossier"
                                   value="<?php echo $form_data['numero_dossier']; ?>" required
                                   placeholder="Ex: DOS-2024-001">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="numero_adherent" class="form-label fw-bold">
                            <i class="fas fa-user text-success me-1"></i>Numéro d'adhérent <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                            <input type="text" class="form-control" id="numero_adherent" name="numero_adherent"
                                   value="<?php echo $form_data['numero_adherent']; ?>" required
                                   placeholder="Ex: ADH-123456">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="raison_retour_id" class="form-label fw-bold">
                            <i class="fas fa-exclamation-triangle text-warning me-1"></i>Motif de retour <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="raison_retour_id" name="raison_retour_id" required>
                            <option value="">🔍 Sélectionnez un motif...</option>
                            <?php foreach (getRaisonsRetourList() as $raison): ?>
                                <option value="<?php echo $raison['id']; ?>" <?php echo $form_data['raison_retour_id'] == $raison['id'] ? 'selected' : ''; ?>>
                                    <?php echo $raison['raison']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="row g-3 mb-4">
                    <div class="col-md-6">
                        <label for="nom" class="form-label fw-bold">
                            <i class="fas fa-user-circle text-info me-1"></i>Nom complet <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-signature"></i></span>
                            <input type="text" class="form-control" id="nom" name="nom"
                                   value="<?php echo $form_data['nom']; ?>" required
                                   placeholder="Nom et prénom du titulaire">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="beneficiaire" class="form-label fw-bold">
                            <i class="fas fa-users text-secondary me-1"></i>Bénéficiaire
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user-friends"></i></span>
                            <input type="text" class="form-control" id="beneficiaire" name="beneficiaire"
                                   value="<?php echo $form_data['beneficiaire']; ?>"
                                   placeholder="Nom du bénéficiaire (optionnel)">
                        </div>
                    </div>
                </div>

                <div class="row g-3 mb-4">
                    <div class="col-md-4">
                        <label for="acte_id" class="form-label fw-bold">
                            <i class="fas fa-file-medical text-primary me-1"></i>Type d'acte
                        </label>
                        <select class="form-select" id="acte_id" name="acte_id">
                            <option value="">🏥 Sélectionnez un acte...</option>
                            <?php foreach (getActesList() as $acte): ?>
                                <option value="<?php echo $acte['id']; ?>" <?php echo $form_data['acte_id'] == $acte['id'] ? 'selected' : ''; ?>>
                                    <?php echo $acte['nom']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="montant" class="form-label fw-bold">
                            <i class="fas fa-coins text-warning me-1"></i>Montant (DH)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">💰</span>
                            <input type="number" class="form-control" id="montant" name="montant"
                                   step="0.01" min="0" value="<?php echo $form_data['montant']; ?>"
                                   placeholder="0.00">
                            <span class="input-group-text">DH</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="date_retour" class="form-label fw-bold">
                            <i class="fas fa-calendar-alt text-danger me-1"></i>Date de retour <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            <input type="text" class="form-control datepicker" id="date_retour" name="date_retour"
                                   required value="<?php echo $form_data['date_retour']; ?>"
                                   placeholder="jj/mm/aaaa">
                        </div>
                    </div>
                </div>

                <div class="row g-3 mb-4">
                    <div class="col-md-4">
                        <label for="date_correction" class="form-label fw-bold">
                            <i class="fas fa-calendar-check text-success me-1"></i>Date de correction
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            <input type="text" class="form-control datepicker" id="date_correction" name="date_correction"
                                   value="<?php echo $form_data['date_correction']; ?>"
                                   placeholder="jj/mm/aaaa (optionnel)">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-tasks text-info me-1"></i>Statut de correction
                        </label>
                        <div class="mt-2">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="corrige" id="non_corrige" value="0"
                                       <?php echo $form_data['corrige'] == '0' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="non_corrige">
                                    <span class="badge bg-warning text-dark">⏳ En attente</span>
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="corrige" id="corrige" value="1"
                                       <?php echo $form_data['corrige'] == '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="corrige">
                                    <span class="badge bg-success">✓ Corrigé</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="notes" class="form-label fw-bold">
                            <i class="fas fa-sticky-note text-secondary me-1"></i>Notes & Commentaires
                        </label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"
                                  placeholder="Ajoutez des notes ou commentaires..."><?php echo $form_data['notes']; ?></textarea>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                    <div class="text-muted small">
                        <i class="fas fa-info-circle me-1"></i>
                        Les champs marqués d'un <span class="text-danger">*</span> sont obligatoires
                    </div>
                    <div class="btn-group">
                        <button type="reset" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-1"></i>Réinitialiser
                        </button>
                        <button type="submit" class="btn btn-primary" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;">
                            <i class="fas fa-plus-circle me-1"></i>Ajouter le Dossier
                        </button>
                    </div>
                </div>
            </form>
            </div>
        </div>
    </div>

    <!-- Liste des dossiers retournés -->
    <div class="card shadow-sm" style="border-radius: 12px; border: none;">
        <div class="card-header text-white" style="background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%); border-radius: 12px 12px 0 0;">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Dossiers du Bordereau</h5>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-light text-dark px-3 py-2" style="border-radius: 20px;">
                        <i class="fas fa-folder me-1"></i><?php echo count($dossiers); ?> dossier(s)
                    </span>
                    <?php if (count($dossiers) > 0): ?>
                        <span class="badge bg-success px-3 py-2" style="border-radius: 20px;">
                            <i class="fas fa-coins me-1"></i><?php echo number_format($montant_total, 2, ',', ' '); ?> DH
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (count($dossiers) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0" style="border-radius: 0 0 12px 12px; overflow: hidden;">
                        <thead style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                            <tr>
                                <th class="border-0 fw-bold text-center">#</th>
                                <th class="border-0 fw-bold"><i class="fas fa-hashtag me-1 text-primary"></i>N° Dossier</th>
                                <th class="border-0 fw-bold"><i class="fas fa-id-card me-1 text-success"></i>N° Adhérent</th>
                                <th class="border-0 fw-bold"><i class="fas fa-user me-1 text-info"></i>Nom</th>
                                <th class="border-0 fw-bold"><i class="fas fa-users me-1 text-secondary"></i>Bénéficiaire</th>
                                <th class="border-0 fw-bold"><i class="fas fa-file-medical me-1 text-primary"></i>Acte</th>
                                <th class="border-0 fw-bold text-end"><i class="fas fa-coins me-1 text-warning"></i>Montant</th>
                                <th class="border-0 fw-bold"><i class="fas fa-exclamation-triangle me-1 text-warning"></i>Motif de retour</th>
                                <th class="border-0 fw-bold text-center"><i class="fas fa-calendar me-1 text-danger"></i>Date retour</th>
                                <th class="border-0 fw-bold text-center"><i class="fas fa-calendar-check me-1 text-success"></i>Date correction</th>
                                <th class="border-0 fw-bold text-center"><i class="fas fa-tasks me-1 text-info"></i>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dossiers as $index => $dossier): ?>
                                <tr class="border-bottom">
                                    <td class="text-center fw-bold text-muted"><?php echo $index + 1; ?></td>
                                    <td class="fw-bold text-primary"><?php echo htmlspecialchars($dossier['numero_dossier']); ?></td>
                                    <td><?php echo htmlspecialchars($dossier['numero_adherent']); ?></td>
                                    <td><?php echo htmlspecialchars($dossier['nom']); ?></td>
                                    <td class="text-muted"><?php echo htmlspecialchars($dossier['beneficiaire']); ?></td>
                                    <td>
                                        <?php if (!empty($dossier['acte_nom'])): ?>
                                            <span class="badge bg-light text-dark"><?php echo htmlspecialchars($dossier['acte_nom']); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-end fw-bold">
                                        <?php if (!empty($dossier['montant'])): ?>
                                            <span class="text-success"><?php echo number_format($dossier['montant'], 2, ',', ' '); ?> DH</span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark" style="font-size: 0.8em;">
                                            <?php echo htmlspecialchars($dossier['raison_retour_nom']); ?>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <small class="text-muted"><?php echo formatDate($dossier['date_retour']); ?></small>
                                    </td>
                                    <td class="text-center">
                                        <?php if (!empty($dossier['date_correction'])): ?>
                                            <small class="text-success"><?php echo formatDate($dossier['date_correction']); ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($dossier['corrige']): ?>
                                            <span class="badge bg-success">✓ Corrigé</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning text-dark">⏳ En attente</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun dossier trouvé</h5>
                    <p class="text-muted">Ce bordereau ne contient aucun dossier retourné.</p>
                    <small class="text-muted">Utilisez le formulaire ci-dessus pour ajouter des dossiers.</small>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
