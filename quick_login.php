<?php
/**
 * Script de connexion rapide pour les tests
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

// Si déjà connecté, rediriger
if (isset($_SESSION['user_id'])) {
    $redirect = $_GET['redirect'] ?? 'index.php?page=dashboard';
    header("Location: $redirect");
    exit;
}

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    try {
        $db = Database::getInstance();
        
        // Vérifier les identifiants
        $sql = "SELECT id, username, password, role, active FROM users WHERE username = ?";
        $user = $db->single($sql, [$username]);
        
        if ($user && password_verify($password, $user['password']) && $user['active']) {
            // Créer la session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            
            // Mettre à jour la dernière connexion
            $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
            $db->query($sql, [$user['id']]);
            
            // Rediriger
            $redirect = $_GET['redirect'] ?? 'index.php?page=dashboard';
            header("Location: $redirect");
            exit;
        } else {
            $error = "Identifiants incorrects";
        }
    } catch (Exception $e) {
        $error = "Erreur: " . $e->getMessage();
    }
}

// Connexion automatique admin si demandée
if (isset($_GET['auto']) && $_GET['auto'] === 'admin') {
    try {
        $db = Database::getInstance();
        
        // Chercher ou créer l'utilisateur admin
        $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
        $user = $db->single($sql);
        
        if (!$user) {
            // Créer l'utilisateur admin
            $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (username, password, email, nom, prenom, role, active, created_at) VALUES
                    ('admin', ?, '<EMAIL>', 'Administrateur', 'Système', 'admin', 1, NOW())";
            $db->query($sql, [$password_hash]);
            
            // Récupérer l'utilisateur créé
            $user = $db->single("SELECT id, username, password, role, active FROM users WHERE username = 'admin'");
        }
        
        if ($user && $user['active']) {
            // Créer la session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            
            // Mettre à jour la dernière connexion
            $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
            $db->query($sql, [$user['id']]);
            
            // Rediriger
            $redirect = $_GET['redirect'] ?? 'index.php?page=dashboard';

            // Debug
            echo "Connexion réussie pour: " . $user['username'] . "<br>";
            echo "Redirection vers: " . $redirect . "<br>";
            echo "Session user_id: " . $_SESSION['user_id'] . "<br>";
            echo "Session role: " . $_SESSION['role'] . "<br>";
            echo "<a href='$redirect'>Cliquer ici pour continuer</a><br>";

            // Redirection automatique après 3 secondes
            echo "<script>setTimeout(function(){ window.location.href = '$redirect'; }, 3000);</script>";
            exit;
        }
    } catch (Exception $e) {
        $error = "Erreur lors de la connexion automatique: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion Rapide - LOT</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .login-body {
            padding: 30px;
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: bold;
        }
        .btn-auto {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            color: white;
            font-weight: bold;
            margin: 5px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="login-card">
                    <div class="login-header">
                        <h2><i class="fas fa-hospital-alt"></i> LOT System</h2>
                        <p class="mb-0">Connexion Rapide</p>
                    </div>
                    <div class="login-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="post">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user"></i> Nom d'utilisateur
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="admin" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock"></i> Mot de passe
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       value="admin123" required>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-login">
                                    <i class="fas fa-sign-in-alt"></i> Se connecter
                                </button>
                            </div>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <h6>Connexion Automatique:</h6>
                            <a href="?auto=admin" class="btn btn-auto">
                                <i class="fas fa-bolt"></i> Admin Auto
                            </a>
                        </div>
                        
                        <hr>
                        
                        <div class="text-center">
                            <h6>Tests Directs:</h6>
                            <a href="?auto=admin&redirect=index.php?page=retours/edit%26numero=10001%26cdm_id=1" 
                               class="btn btn-auto btn-sm">
                                📝 Page Problématique
                            </a>
                            <a href="?auto=admin&redirect=index.php?page=dashboard" 
                               class="btn btn-auto btn-sm">
                                📊 Dashboard
                            </a>
                        </div>
                        
                        <div class="mt-3 text-center text-muted">
                            <small>
                                <i class="fas fa-info-circle"></i> 
                                Identifiants par défaut: admin / admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
