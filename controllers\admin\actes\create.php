<?php
// Contrôleur pour la création d'un nouvel acte médical

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Titre de la page
$page_title = 'Nouvel acte médical';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=admin/actes/create', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupération des données du formulaire
    $nom = clean($_POST['nom'] ?? '');
    $description = clean($_POST['description'] ?? '');
    $prix = isset($_POST['prix']) ? floatval(str_replace(',', '.', $_POST['prix'])) : 0;
    $active = isset($_POST['active']) ? 1 : 0;

    // Validation des données
    $errors = [];

    if (empty($nom)) {
        $errors[] = 'Le nom de l\'acte est obligatoire.';
    }

    // Vérifier si le nom existe déjà
    $sql = "SELECT COUNT(*) as count FROM actes WHERE nom = ?";
    $result = $db->single($sql, [$nom]);

    if ($result['count'] > 0) {
        $errors[] = 'Un acte avec ce nom existe déjà.';
    }

    // Si aucune erreur, créer l'acte
    if (empty($errors)) {
        // Vérifier si la colonne 'active' existe
        try {
            $check_column = "SHOW COLUMNS FROM actes LIKE 'active'";
            $column_exists = $db->query($check_column)->rowCount() > 0;
        } catch (Exception $e) {
            $column_exists = false;
        }

        if ($column_exists) {
            $sql = "INSERT INTO actes (nom, description, prix, active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, NOW(), NOW())";
            $params = [$nom, $description, $prix, $active];
        } else {
            $sql = "INSERT INTO actes (nom, description, prix, created_at, updated_at)
                    VALUES (?, ?, ?, NOW(), NOW())";
            $params = [$nom, $description, $prix];
        }

        if ($db->query($sql, $params)) {
            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Création d\'un acte', 'Nom: ' . $nom);

            redirect('index.php?page=admin/actes', 'Acte créé avec succès.', 'success');
        } else {
            $errors[] = 'Erreur lors de la création de l\'acte.';
        }
    }
}

// Récupérer les données du formulaire en cas d'erreur
$default_data = [
    'nom' => '',
    'description' => '',
    'prix' => '0.00',
    'active' => 1
];

$form_data = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $form_data = [
        'nom' => $_POST['nom'] ?? '',
        'description' => $_POST['description'] ?? '',
        'prix' => $_POST['prix'] ?? '0.00',
        'active' => isset($_POST['active']) ? 1 : 0
    ];
} else {
    $form_data = $default_data;
}

// Charger la vue
require_once VIEWS_PATH . 'admin/actes/create.php';
?>
