<?php
// Contrôleur pour la recherche de lots et dossiers

// Titre de la page
$page_title = 'Recherche';
$page = 'lots/search';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de recherche
$search_type = isset($_GET['search_type']) ? clean($_GET['search_type']) : 'dossier';
$search_term = isset($_GET['search_term']) ? clean($_GET['search_term']) : '';
$cdm_id = isset($_GET['cdm_id']) ? intval($_GET['cdm_id']) : 0;
$date_debut = isset($_GET['date_debut']) ? clean($_GET['date_debut']) : '';
$date_fin = isset($_GET['date_fin']) ? clean($_GET['date_fin']) : '';
$statut = isset($_GET['statut']) ? clean($_GET['statut']) : '';

// Résultats de recherche
$results = [];
$total_results = 0;

// Effectuer la recherche si un terme est fourni
if (!empty($search_term)) {
    // Recherche de dossiers
    if ($search_type === 'dossier') {
        $sql = "SELECT d.*, l.numero_lot, c.nom as cdm_nom, a.nom as acte_nom
                FROM dossiers d
                JOIN lots l ON d.lot_id = l.id
                JOIN cdm c ON l.cdm_id = c.id
                LEFT JOIN actes a ON d.acte_id = a.id
                WHERE (d.numero_dossier LIKE ? OR d.numero_adherent LIKE ? OR d.nom LIKE ? OR d.beneficiaire LIKE ?)";
        
        $params = ["%$search_term%", "%$search_term%", "%$search_term%", "%$search_term%"];
        
        // Filtres supplémentaires
        if ($cdm_id > 0) {
            $sql .= " AND l.cdm_id = ?";
            $params[] = $cdm_id;
        }
        
        if (!empty($date_debut)) {
            $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
            $sql .= " AND l.date_demande >= ?";
            $params[] = $date_debut_formatted;
        }
        
        if (!empty($date_fin)) {
            $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
            $sql .= " AND l.date_demande <= ?";
            $params[] = $date_fin_formatted;
        }
        
        if (!empty($statut)) {
            if ($statut === 'recu') {
                $sql .= " AND d.recu = 1";
            } elseif ($statut === 'non_recu') {
                $sql .= " AND d.recu = 0";
            }
        }
        
        $sql .= " ORDER BY d.created_at DESC LIMIT 100";
        
        $results = $db->all($sql, $params);
        $total_results = count($results);
    }
    // Recherche de lots
    elseif ($search_type === 'lot') {
        $sql = "SELECT l.*, c.nom as cdm_nom, 
                       (SELECT COUNT(*) FROM dossiers WHERE lot_id = l.id) as nb_dossiers,
                       (SELECT COUNT(*) FROM dossiers WHERE lot_id = l.id AND recu = 1) as nb_recus
                FROM lots l
                JOIN cdm c ON l.cdm_id = c.id
                WHERE l.numero_lot LIKE ?";
        
        $params = ["%$search_term%"];
        
        // Filtres supplémentaires
        if ($cdm_id > 0) {
            $sql .= " AND l.cdm_id = ?";
            $params[] = $cdm_id;
        }
        
        if (!empty($date_debut)) {
            $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
            $sql .= " AND l.date_demande >= ?";
            $params[] = $date_debut_formatted;
        }
        
        if (!empty($date_fin)) {
            $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
            $sql .= " AND l.date_demande <= ?";
            $params[] = $date_fin_formatted;
        }
        
        if (!empty($statut)) {
            $sql .= " AND l.statut = ?";
            $params[] = $statut;
        }
        
        $sql .= " ORDER BY l.created_at DESC LIMIT 100";
        
        $results = $db->all($sql, $params);
        $total_results = count($results);
    }
    // Recherche d'adhérent
    elseif ($search_type === 'adherent') {
        $sql = "SELECT d.*, l.numero_lot, c.nom as cdm_nom, a.nom as acte_nom
                FROM dossiers d
                JOIN lots l ON d.lot_id = l.id
                JOIN cdm c ON l.cdm_id = c.id
                LEFT JOIN actes a ON d.acte_id = a.id
                WHERE d.numero_adherent = ?";
        
        $params = [$search_term];
        
        // Filtres supplémentaires
        if ($cdm_id > 0) {
            $sql .= " AND l.cdm_id = ?";
            $params[] = $cdm_id;
        }
        
        if (!empty($date_debut)) {
            $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
            $sql .= " AND l.date_demande >= ?";
            $params[] = $date_debut_formatted;
        }
        
        if (!empty($date_fin)) {
            $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
            $sql .= " AND l.date_demande <= ?";
            $params[] = $date_fin_formatted;
        }
        
        if (!empty($statut)) {
            if ($statut === 'recu') {
                $sql .= " AND d.recu = 1";
            } elseif ($statut === 'non_recu') {
                $sql .= " AND d.recu = 0";
            }
        }
        
        $sql .= " ORDER BY d.created_at DESC LIMIT 100";
        
        $results = $db->all($sql, $params);
        $total_results = count($results);
    }
}

// Obtenir la liste des CDM pour le filtre
$cdm_list = getCDMList();

// Charger la vue
require_once VIEWS_PATH . 'lots/search.php';
?>
