-- Script de mise à jour de la base de données pour l'application LOT
-- Ce script ajoute toutes les colonnes et tables manquantes

-- 1. Ajout de la colonne 'prix' à la table 'actes' si elle n'existe pas
SET @prix_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'actes'
    AND COLUMN_NAME = 'prix'
);

SET @sql_add_prix = IF(@prix_exists = 0, 
    'ALTER TABLE actes ADD COLUMN prix DECIMAL(10,2) DEFAULT 0.00 AFTER description',
    'SELECT "La colonne prix existe déjà dans la table actes" AS message');

PREPARE stmt FROM @sql_add_prix;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. Ajout de la colonne 'active' à la table 'actes' si elle n'existe pas
SET @active_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'actes'
    AND COLUMN_NAME = 'active'
);

SET @sql_add_active = IF(@active_exists = 0, 
    'ALTER TABLE actes ADD COLUMN active TINYINT(1) NOT NULL DEFAULT 1',
    'SELECT "La colonne active existe déjà dans la table actes" AS message');

PREPARE stmt FROM @sql_add_active;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. Ajout de la colonne 'active' à la table 'raisons_retour' si elle n'existe pas
SET @active_raisons_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'raisons_retour'
    AND COLUMN_NAME = 'active'
);

SET @sql_add_active_raisons = IF(@active_raisons_exists = 0, 
    'ALTER TABLE raisons_retour ADD COLUMN active TINYINT(1) NOT NULL DEFAULT 1',
    'SELECT "La colonne active existe déjà dans la table raisons_retour" AS message');

PREPARE stmt FROM @sql_add_active_raisons;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. Création de la table 'bordereaux_retour' si elle n'existe pas
SET @bordereaux_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'bordereaux_retour'
);

SET @sql_create_bordereaux = IF(@bordereaux_exists = 0, 
    'CREATE TABLE bordereaux_retour (
        id INT AUTO_INCREMENT PRIMARY KEY,
        numero INT NOT NULL,
        cdm_id INT NOT NULL,
        date_bordereau DATE NOT NULL,
        notes TEXT NULL,
        created_by INT NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NULL,
        UNIQUE KEY unique_bordereau (numero, cdm_id),
        FOREIGN KEY (cdm_id) REFERENCES cdm(id),
        FOREIGN KEY (created_by) REFERENCES users(id)
    )',
    'SELECT "La table bordereaux_retour existe déjà" AS message');

PREPARE stmt FROM @sql_create_bordereaux;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. Migration des données vers bordereaux_retour si la table vient d'être créée
SET @sql_migrate_bordereaux = IF(@bordereaux_exists = 0, 
    'INSERT IGNORE INTO bordereaux_retour (numero, cdm_id, date_bordereau, created_by, created_at)
     SELECT DISTINCT numero_bordereau, cdm_id, date_bordereau, created_by, created_at
     FROM dossiers_retournes',
    'SELECT "Pas besoin de migrer les données vers bordereaux_retour" AS message');

PREPARE stmt FROM @sql_migrate_bordereaux;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. Ajout de la colonne 'notes' à la table 'bordereaux_retour' si elle n'existe pas
SET @notes_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'bordereaux_retour'
    AND COLUMN_NAME = 'notes'
);

SET @sql_add_notes = IF(@notes_exists = 0, 
    'ALTER TABLE bordereaux_retour ADD COLUMN notes TEXT NULL AFTER date_bordereau',
    'SELECT "La colonne notes existe déjà dans la table bordereaux_retour" AS message');

PREPARE stmt FROM @sql_add_notes;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. Création de la table 'dossier_raisons' si elle n'existe pas
SET @dossier_raisons_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'dossier_raisons'
);

SET @sql_create_dossier_raisons = IF(@dossier_raisons_exists = 0, 
    'CREATE TABLE dossier_raisons (
        id INT AUTO_INCREMENT PRIMARY KEY,
        dossier_id INT NOT NULL,
        raison_id INT NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_dossier_raison (dossier_id, raison_id),
        FOREIGN KEY (dossier_id) REFERENCES dossiers_retournes(id) ON DELETE CASCADE,
        FOREIGN KEY (raison_id) REFERENCES raisons_retour(id) ON DELETE CASCADE
    )',
    'SELECT "La table dossier_raisons existe déjà" AS message');

PREPARE stmt FROM @sql_create_dossier_raisons;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 8. Migration des données vers dossier_raisons si la table vient d'être créée
SET @sql_migrate_raisons = IF(@dossier_raisons_exists = 0, 
    'INSERT IGNORE INTO dossier_raisons (dossier_id, raison_id, created_at)
     SELECT id, raison_retour_id, created_at FROM dossiers_retournes 
     WHERE raison_retour_id IS NOT NULL AND raison_retour_id > 0',
    'SELECT "Pas besoin de migrer les données vers dossier_raisons" AS message');

PREPARE stmt FROM @sql_migrate_raisons;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 9. Ajout de la colonne 'action_type' à la table 'logs' si elle n'existe pas
SET @action_type_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'logs'
    AND COLUMN_NAME = 'action_type'
);

SET @sql_add_action_type = IF(@action_type_exists = 0, 
    'ALTER TABLE logs ADD COLUMN action_type VARCHAR(50) DEFAULT "info" AFTER action',
    'SELECT "La colonne action_type existe déjà dans la table logs" AS message');

PREPARE stmt FROM @sql_add_action_type;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 10. Ajout de la colonne 'beneficiaire' à la table 'dossiers_retournes' si elle n'existe pas
SET @beneficiaire_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'dossiers_retournes'
    AND COLUMN_NAME = 'beneficiaire'
);

SET @sql_add_beneficiaire = IF(@beneficiaire_exists = 0, 
    'ALTER TABLE dossiers_retournes ADD COLUMN beneficiaire VARCHAR(100) AFTER nom',
    'SELECT "La colonne beneficiaire existe déjà dans la table dossiers_retournes" AS message');

PREPARE stmt FROM @sql_add_beneficiaire;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 11. Ajout de la colonne 'notes' à la table 'dossiers_retournes' si elle n'existe pas
SET @notes_dossiers_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'dossiers_retournes'
    AND COLUMN_NAME = 'notes'
);

SET @sql_add_notes_dossiers = IF(@notes_dossiers_exists = 0, 
    'ALTER TABLE dossiers_retournes ADD COLUMN notes TEXT AFTER corrige',
    'SELECT "La colonne notes existe déjà dans la table dossiers_retournes" AS message');

PREPARE stmt FROM @sql_add_notes_dossiers;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Mise à jour des données existantes pour la colonne action_type dans logs
UPDATE logs SET action_type = 'info' WHERE action_type IS NULL;

-- Message de fin
SELECT 'Mise à jour de la base de données terminée avec succès' AS message;
