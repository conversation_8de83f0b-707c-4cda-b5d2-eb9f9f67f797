<?php
/**
 * Classe utilitaire pour l'exportation de données vers Excel
 */
class ExcelExporter {
    /**
     * Exporte les données vers un fichier Excel CSV
     * 
     * @param array $data Les données à exporter (tableau de tableaux)
     * @param array $headers Les en-têtes des colonnes
     * @param string $filename Le nom du fichier à générer
     * @return void
     */
    public static function exportToCSV($data, $headers, $filename) {
        // Définir les en-têtes HTTP pour le téléchargement
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        
        // Créer un gestionnaire de fichier pour la sortie
        $output = fopen('php://output', 'w');
        
        // Ajouter le BOM UTF-8 pour une meilleure compatibilité avec Excel
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Écrire les en-têtes
        fputcsv($output, $headers, ';');
        
        // Écrire les données
        foreach ($data as $row) {
            fputcsv($output, $row, ';');
        }
        
        // Fermer le gestionnaire de fichier
        fclose($output);
        exit;
    }
    
    /**
     * Exporte les données vers un fichier Excel XLSX (nécessite la bibliothèque PhpSpreadsheet)
     * 
     * @param array $data Les données à exporter (tableau de tableaux)
     * @param array $headers Les en-têtes des colonnes
     * @param string $filename Le nom du fichier à générer
     * @return void
     */
    public static function exportToXLSX($data, $headers, $filename) {
        // Vérifier si la bibliothèque PhpSpreadsheet est disponible
        if (!class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            // Si non disponible, utiliser l'exportation CSV à la place
            self::exportToCSV($data, $headers, $filename);
            return;
        }
        
        // Créer un nouveau document Excel
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Ajouter les en-têtes
        $col = 1;
        foreach ($headers as $header) {
            $sheet->setCellValueByColumnAndRow($col++, 1, $header);
        }
        
        // Ajouter les données
        $row = 2;
        foreach ($data as $dataRow) {
            $col = 1;
            foreach ($dataRow as $value) {
                $sheet->setCellValueByColumnAndRow($col++, $row, $value);
            }
            $row++;
        }
        
        // Créer un writer pour XLSX
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        
        // Définir les en-têtes HTTP pour le téléchargement
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');
        
        // Envoyer le fichier au navigateur
        $writer->save('php://output');
        exit;
    }
    
    /**
     * Exporte les données vers un fichier Excel (utilise CSV par défaut)
     * 
     * @param array $data Les données à exporter (tableau de tableaux)
     * @param array $headers Les en-têtes des colonnes
     * @param string $filename Le nom du fichier à générer
     * @param string $format Le format d'exportation ('csv' ou 'xlsx')
     * @return void
     */
    public static function export($data, $headers, $filename, $format = 'csv') {
        if ($format === 'xlsx') {
            self::exportToXLSX($data, $headers, $filename);
        } else {
            self::exportToCSV($data, $headers, $filename);
        }
    }
}
?>
