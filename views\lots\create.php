<?php
// Vue pour la création d'un nouveau lot
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-plus-circle"></i> Nouveau lot</h1>
        <div>
            <a href="index.php?page=lots" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>

    <?php if (!empty($form_errors)): ?>
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle"></i> Erreurs:</h5>
            <ul class="mb-0">
                <?php foreach ($form_errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-layer-group"></i> Informations du lot</h5>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=lots/create">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="numero_lot" class="form-label required">Numéro de lot</label>
                        <input type="text" class="form-control" id="numero_lot" name="numero_lot" value="<?php echo $form_data['numero_lot'] ?? ''; ?>" required>
                        <div class="form-text">Numéro unique du lot (obligatoire et doit être unique)</div>
                    </div>
                    <div class="col-md-4">
                        <label for="cdm_id" class="form-label required">CDM</label>
                        <select class="form-select" id="cdm_id" name="cdm_id" required>
                            <option value="">Sélectionnez un CDM</option>
                            <?php foreach ($cdm_list as $cdm): ?>
                                <option value="<?php echo $cdm['id']; ?>" <?php echo $form_data['cdm_id'] == $cdm['id'] ? 'selected' : ''; ?>>
                                    <?php echo $cdm['nom']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Centre médical concerné par ce lot</div>
                    </div>
                    <div class="col-md-4">
                        <label for="date_demande" class="form-label required">Date de demande</label>
                        <input type="text" class="form-control datepicker" id="date_demande" name="date_demande" value="<?php echo $form_data['date_demande']; ?>" required placeholder="jj/mm/aaaa">
                        <div class="form-text">Date à laquelle le lot a été demandé</div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $form_data['notes']; ?></textarea>
                    <div class="form-text">Informations complémentaires sur ce lot</div>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> Le numéro de lot doit être unique et ne peut pas être modifié après la création.
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Créer le lot
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
