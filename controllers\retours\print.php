<?php
// Contrôleur pour l'impression d'un bordereau de dossiers retournés

// Vérifier si les paramètres du bordereau sont fournis
if (!isset($_GET['numero']) || empty($_GET['numero']) || !isset($_GET['cdm_id']) || empty($_GET['cdm_id'])) {
    redirect('index.php?page=retours', 'Paramètres du bordereau non spécifiés.', 'danger');
}

$numero_bordereau = intval($_GET['numero']);
$cdm_id = intval($_GET['cdm_id']);

// Titre de la page
$page_title = 'Impression - Bordereau #' . $numero_bordereau;
$page = 'retours/print';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Vérifier si le bordereau existe
// Vérifier d'abord dans la table bordereaux_retour
$sql = "SELECT COUNT(*) as count FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
$result = $db->single($sql, [$numero_bordereau, $cdm_id]);

if ($result['count'] === 0) {
    // Si le bordereau n'existe pas dans la table bordereaux_retour, vérifier dans dossiers_retournes
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $result = $db->single($sql, [$numero_bordereau, $cdm_id]);

    if ($result['count'] === 0) {
        redirect('index.php?page=retours', 'Bordereau non trouvé.', 'danger');
    }
}

// Récupérer la date du bordereau, les notes et les informations de l'utilisateur qui l'a créé
// Essayer d'abord dans la table bordereaux_retour
$sql = "SELECT br.date_bordereau, br.notes, u.username as created_by_username
        FROM bordereaux_retour br
        JOIN users u ON br.created_by = u.id
        WHERE br.numero = ? AND br.cdm_id = ?
        LIMIT 1";
$bordereau_info = $db->single($sql, [$numero_bordereau, $cdm_id]);

// Si aucune information n'est trouvée, essayer dans la table dossiers_retournes
if (!$bordereau_info) {
    $sql = "SELECT dr.date_bordereau, '' as notes, u.username as created_by_username
            FROM dossiers_retournes dr
            JOIN users u ON dr.created_by = u.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            LIMIT 1";
    $bordereau_info = $db->single($sql, [$numero_bordereau, $cdm_id]);
}

// Récupérer les informations du CDM
$sql = "SELECT * FROM cdm WHERE id = ?";
$cdm = $db->single($sql, [$cdm_id]);

if (!$cdm) {
    redirect('index.php?page=retours', 'CDM non trouvé.', 'danger');
}

// Récupérer les dossiers associés à ce bordereau
$sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
        FROM dossiers_retournes dr
        LEFT JOIN actes a ON dr.acte_id = a.id
        LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
        WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
        ORDER BY dr.created_at ASC";
$dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);

// Récupérer toutes les raisons de retour pour chaque dossier
for ($i = 0; $i < count($dossiers); $i++) {
    $sql = "SELECT dr.*, r.raison as raison
            FROM dossier_raisons dr
            JOIN raisons_retour r ON dr.raison_id = r.id
            WHERE dr.dossier_id = ?
            ORDER BY dr.created_at ASC";
    $raisons = $db->all($sql, [$dossiers[$i]['id']]);

    // Si aucune raison n'est trouvée dans la nouvelle table, utiliser la raison principale
    if (empty($raisons) && !empty($dossiers[$i]['raison_retour_nom'])) {
        $dossiers[$i]['raisons'] = [['raison' => $dossiers[$i]['raison_retour_nom']]];
    } else {
        $dossiers[$i]['raisons'] = $raisons;
    }

    // Créer une chaîne de toutes les raisons pour l'affichage
    $raisons_str = [];
    foreach ($dossiers[$i]['raisons'] as $raison) {
        $raisons_str[] = $raison['raison'];
    }
    $dossiers[$i]['raisons_str'] = implode(', ', $raisons_str);
}

// Statistiques du bordereau
$total_dossiers = count($dossiers);
$dossiers_corriges = 0;
$dossiers_non_corriges = 0;
$montant_total = 0;

foreach ($dossiers as $dossier) {
    if ($dossier['corrige']) {
        $dossiers_corriges++;
    } else {
        $dossiers_non_corriges++;
    }

    $montant_total += $dossier['montant'];
}

// Charger la vue
require_once VIEWS_PATH . 'retours/print.php';
?>
