/**
 * Script principal pour l'application de gestion des lots
 */

// Attendre que le DOM soit chargé
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialiser les popovers Bootstrap
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Fermer automatiquement les alertes après 5 secondes
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // Confirmation de suppression
    document.querySelectorAll('.confirm-delete').forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cet élément ? Cette action est irréversible.')) {
                e.preventDefault();
            }
        });
    });
    
    // Gestion des formulaires dynamiques (ajout/suppression de champs)
    setupDynamicForms();
    
    // Initialiser les sélecteurs de date
    initDatepickers();
    
    // Initialiser les graphiques si la page en contient
    initCharts();
    
    // Gestion des filtres de recherche avancée
    setupAdvancedSearch();
    
    // Gestion de l'impression
    setupPrinting();
});

/**
 * Initialise les formulaires dynamiques
 */
function setupDynamicForms() {
    // Ajouter un nouveau champ
    document.querySelectorAll('.add-field').forEach(function(button) {
        button.addEventListener('click', function() {
            var container = document.querySelector(this.dataset.container);
            var template = document.querySelector(this.dataset.template).innerHTML;
            var index = container.querySelectorAll('.dynamic-field').length;
            
            // Remplacer l'index dans le template
            template = template.replace(/\{index\}/g, index);
            
            // Créer un élément temporaire pour contenir le HTML
            var temp = document.createElement('div');
            temp.innerHTML = template;
            
            // Ajouter le nouveau champ au conteneur
            container.appendChild(temp.firstElementChild);
            
            // Réinitialiser les événements pour les nouveaux champs
            initDatepickers();
        });
    });
    
    // Supprimer un champ
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('remove-field')) {
            var field = e.target.closest('.dynamic-field');
            field.remove();
        }
    });
}

/**
 * Initialise les sélecteurs de date
 */
function initDatepickers() {
    // Si flatpickr est disponible
    if (typeof flatpickr !== 'undefined') {
        flatpickr('.datepicker', {
            dateFormat: 'd/m/Y',
            locale: 'fr',
            allowInput: true
        });
    }
}

/**
 * Initialise les graphiques Chart.js modernes
 * Note: Les graphiques sont maintenant gérés par charts-modern.js
 */
function initCharts() {
    // Les graphiques sont automatiquement initialisés par charts-modern.js
    // Cette fonction est conservée pour la compatibilité
    console.log('Charts initialized by charts-modern.js');
}

/**
 * Configure la recherche avancée
 */
function setupAdvancedSearch() {
    var toggleBtn = document.getElementById('toggleAdvancedSearch');
    var advancedFilters = document.getElementById('advancedFilters');
    
    if (toggleBtn && advancedFilters) {
        toggleBtn.addEventListener('click', function() {
            advancedFilters.classList.toggle('d-none');
            
            if (advancedFilters.classList.contains('d-none')) {
                toggleBtn.innerHTML = '<i class="fas fa-plus"></i> Afficher les filtres avancés';
            } else {
                toggleBtn.innerHTML = '<i class="fas fa-minus"></i> Masquer les filtres avancés';
            }
        });
    }
    
    // Réinitialiser les filtres
    var resetBtn = document.getElementById('resetFilters');
    if (resetBtn) {
        resetBtn.addEventListener('click', function() {
            var form = this.closest('form');
            form.reset();
            
            // Réinitialiser les sélecteurs personnalisés
            form.querySelectorAll('select').forEach(function(select) {
                if (select.classList.contains('select2-hidden-accessible')) {
                    $(select).val(null).trigger('change');
                }
            });
        });
    }
}

/**
 * Configure l'impression
 */
function setupPrinting() {
    var printBtn = document.getElementById('printPage');
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            window.print();
        });
    }
}

/**
 * Exporte un tableau en Excel
 * @param {string} tableId - ID du tableau à exporter
 * @param {string} filename - Nom du fichier Excel
 */
function exportTableToExcel(tableId, filename = 'export') {
    var table = document.getElementById(tableId);
    if (!table) return;
    
    var wb = XLSX.utils.table_to_book(table, {sheet: "Feuille1"});
    XLSX.writeFile(wb, filename + '.xlsx');
}

/**
 * Exporte un tableau en PDF
 * @param {string} tableId - ID du tableau à exporter
 * @param {string} filename - Nom du fichier PDF
 */
function exportTableToPDF(tableId, filename = 'export') {
    var table = document.getElementById(tableId);
    if (!table) return;
    
    html2canvas(table).then(function(canvas) {
        var imgData = canvas.toDataURL('image/png');
        var pdf = new jsPDF('l', 'mm', 'a4');
        var pageWidth = pdf.internal.pageSize.getWidth();
        var pageHeight = pdf.internal.pageSize.getHeight();
        var imageWidth = canvas.width;
        var imageHeight = canvas.height;
        var ratio = imageWidth / imageHeight;
        var width = pageWidth - 20;
        var height = width / ratio;
        
        if (height > pageHeight - 20) {
            height = pageHeight - 20;
            width = height * ratio;
        }
        
        pdf.addImage(imgData, 'PNG', 10, 10, width, height);
        pdf.save(filename + '.pdf');
    });
}
