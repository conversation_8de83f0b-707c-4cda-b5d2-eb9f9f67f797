<?php
// Contrôleur pour la gestion des actes médicaux

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Titre de la page
$page_title = 'Gestion des actes médicaux';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de pagination
$page_num = isset($_GET['page_num']) ? intval($_GET['page_num']) : 1;
$items_per_page = ITEMS_PER_PAGE;
$offset = ($page_num - 1) * $items_per_page;

// Recherche
$search = isset($_GET['search']) ? clean($_GET['search']) : '';

// Construction de la requête SQL
$sql_count = "SELECT COUNT(*) as total FROM actes";

// Vérifier si la colonne 'active' existe
try {
    $check_column = "SHOW COLUMNS FROM actes LIKE 'active'";
    $column_exists = $db->query($check_column)->rowCount() > 0;

    if ($column_exists) {
        $sql = "SELECT id, nom, description, prix, active, created_at, updated_at FROM actes";
    } else {
        $sql = "SELECT id, nom, description, prix, created_at, updated_at FROM actes";
        $column_exists = false;
    }
} catch (Exception $e) {
    // En cas d'erreur, utiliser une requête sans la colonne 'active'
    $sql = "SELECT id, nom, description, prix, created_at, updated_at FROM actes";
    $column_exists = false;
}

$params = [];

// Appliquer le filtre de recherche
if (!empty($search)) {
    $sql_count .= " WHERE nom LIKE ? OR description LIKE ?";
    $sql .= " WHERE nom LIKE ? OR description LIKE ?";
    $search_param = "%$search%";
    $params = [$search_param, $search_param];
}

// Obtenir le nombre total d'actes
$result = $db->single($sql_count, $params);
$total_items = $result['total'];
$total_pages = ceil($total_items / $items_per_page);

// Ajouter l'ordre et la limite à la requête principale
$sql .= " ORDER BY nom ASC LIMIT ?, ?";
$params[] = $offset;
$params[] = $items_per_page;

// Exécuter la requête
$actes_list = $db->all($sql, $params);

// Ajouter une propriété active par défaut si la colonne n'existe pas
if (!$column_exists) {
    foreach ($actes_list as &$acte) {
        $acte['active'] = 1; // Par défaut, tous les actes sont considérés comme actifs
    }
}

// Charger la vue
require_once VIEWS_PATH . 'admin/actes/index.php';
?>
