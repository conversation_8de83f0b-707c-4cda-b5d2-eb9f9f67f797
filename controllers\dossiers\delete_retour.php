<?php
// Contrôleur pour la suppression d'un dossier retourné

// Vérifier si l'ID du dossier est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=retours', 'ID du dossier non spécifié.', 'danger');
}

$dossier_id = intval($_GET['id']);

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du dossier
$sql = "SELECT dr.*, l.numero as numero_bordereau, l.cdm_id, c.nom as cdm_nom
        FROM dossiers_retournes dr
        LEFT JOIN bordereaux_retour l ON dr.numero_bordereau = l.numero AND dr.cdm_id = l.cdm_id
        JOIN cdm c ON dr.cdm_id = c.id
        WHERE dr.id = ?";
$dossier = $db->single($sql, [$dossier_id]);

// Vérifier si le dossier existe
if (!$dossier) {
    redirect('index.php?page=retours', 'Dossier non trouvé.', 'danger');
}

$numero_bordereau = $dossier['numero_bordereau'];
$cdm_id = $dossier['cdm_id'];

// Titre de la page
$page_title = 'Supprimer le dossier retourné #' . $dossier['numero_dossier'];
$page = 'dossiers/delete_retour';

// Traitement de la confirmation de suppression
if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
    try {
        // Supprimer les raisons associées au dossier
        $sql = "DELETE FROM dossier_raisons WHERE dossier_id = ?";
        $db->query($sql, [$dossier_id]);
        
        // Supprimer le dossier de la base de données
        $sql = "DELETE FROM dossiers_retournes WHERE id = ?";
        $db->query($sql, [$dossier_id]);

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Suppression d\'un dossier retourné', "Dossier #{$dossier['numero_dossier']} supprimé");

        // Rediriger vers la page d'édition du bordereau
        redirect('index.php?page=retours/edit&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Le dossier a été supprimé avec succès.', 'success');
    } catch (Exception $e) {
        redirect('index.php?page=retours/edit&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Erreur lors de la suppression du dossier: ' . $e->getMessage(), 'danger');
    }
} else {
    // Charger la vue
    require_once VIEWS_PATH . 'dossiers/delete_retour.php';
}
?>
