<?php
// Script pour ajouter la colonne 'prix' à la table 'actes'
require_once 'config/config.php';
require_once 'config/database.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

try {
    // Vérifier si la colonne existe déjà
    $sql = "SHOW COLUMNS FROM actes LIKE 'prix'";
    $result = $db->query($sql);
    $column_exists = $result->rowCount() > 0;
    
    if (!$column_exists) {
        // Ajouter la colonne 'prix' à la table 'actes'
        $sql = "ALTER TABLE actes ADD COLUMN prix DECIMAL(10,2) DEFAULT 0.00 AFTER description";
        $db->query($sql);
        echo "La colonne 'prix' a été ajoutée avec succès à la table 'actes'.";
    } else {
        echo "La colonne 'prix' existe déjà dans la table 'actes'.";
    }
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage();
}
?>
