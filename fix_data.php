<?php
/**
 * Script pour corriger les données manquantes
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // Créer la session admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🔧 Correction des Données</h2>";
    echo "<p>✅ Session admin créée</p>";
    
    // 1. Vérifier et corriger les actes
    echo "<h3>🩺 Correction des actes médicaux</h3>";
    
    $sql = "SELECT COUNT(*) as count FROM actes";
    $result = $db->single($sql);
    echo "• Actes existants: " . $result['count'] . "<br>";
    
    if ($result['count'] < 10) {
        echo "📦 Ajout d'actes médicaux...<br>";
        
        $actes = [
            ['CONS-GEN', 'Consultation générale', 'Consultation médicale générale chez un médecin généraliste'],
            ['CONS-SPEC', 'Consultation spécialiste', 'Consultation avec un médecin spécialiste'],
            ['RADIO-STD', 'Radiographie standard', 'Examen radiographique standard (thorax, membres, etc.)'],
            ['RADIO-CONT', 'Radiographie avec contraste', 'Examen radiographique avec produit de contraste'],
            ['ECHO-ABD', 'Échographie abdominale', 'Examen échographique de l\'abdomen'],
            ['ECHO-CARD', 'Échocardiographie', 'Examen échographique du cœur'],
            ['LABO-STD', 'Analyses standard', 'Analyses biologiques de routine (NFS, glycémie, etc.)'],
            ['LABO-SPEC', 'Analyses spécialisées', 'Analyses biologiques spécialisées (hormones, marqueurs, etc.)'],
            ['DENT-CONS', 'Consultation dentaire', 'Consultation chez un chirurgien-dentiste'],
            ['DENT-SOIN', 'Soins dentaires', 'Soins dentaires conservateurs (plombages, détartrage, etc.)'],
            ['KINE-SEANCE', 'Séance de kinésithérapie', 'Séance individuelle de rééducation fonctionnelle'],
            ['OPHTA-CONS', 'Consultation ophtalmologique', 'Consultation chez un ophtalmologue'],
            ['HOSP-JOUR', 'Hospitalisation de jour', 'Hospitalisation de moins de 24 heures'],
            ['CHIR-AMB', 'Chirurgie ambulatoire', 'Intervention chirurgicale en ambulatoire'],
            ['URGENCE', 'Soins d\'urgence', 'Soins médicaux d\'urgence'],
            ['TRANSPORT', 'Transport sanitaire', 'Transport en ambulance ou véhicule sanitaire'],
            ['PHARMA', 'Médicaments', 'Remboursement de médicaments prescrits'],
            ['OPTIQUE', 'Optique médicale', 'Lunettes et lentilles de contact sur prescription'],
            ['PROTHESE', 'Prothèses et orthèses', 'Appareillage médical et prothèses'],
            ['PSYCHO', 'Consultation psychologique', 'Consultation chez un psychologue ou psychiatre']
        ];
        
        foreach ($actes as $acte) {
            $sql = "INSERT IGNORE INTO actes (code, nom, description, created_at) VALUES (?, ?, ?, NOW())";
            $db->query($sql, $acte);
            echo "  ✅ " . $acte[1] . "<br>";
        }
    }
    
    // 2. Vérifier et corriger les CDM
    echo "<h3>🏥 Correction des CDM</h3>";
    
    $sql = "SELECT COUNT(*) as count FROM cdm";
    $result = $db->single($sql);
    echo "• CDM existants: " . $result['count'] . "<br>";
    
    if ($result['count'] < 3) {
        echo "📦 Ajout de CDM...<br>";
        
        $cdms = [
            ['CDM Centre-Ville', '123 Avenue Mohammed V, Casablanca', '0522-123456', '<EMAIL>', 'Dr. Ahmed Benali'],
            ['CDM Maarif', '456 Boulevard Zerktouni, Casablanca', '0522-234567', '<EMAIL>', 'Dr. Fatima Alami'],
            ['CDM Ain Sebaa', '789 Rue des Palmiers, Ain Sebaa', '0522-345678', '<EMAIL>', 'Dr. Mohamed Tazi'],
            ['CDM Hay Mohammadi', '321 Avenue Hassan II, Hay Mohammadi', '0522-456789', '<EMAIL>', 'Dr. Aicha Bennani'],
            ['CDM Sidi Bernoussi', '654 Rue de la Liberté, Sidi Bernoussi', '0522-567890', '<EMAIL>', 'Dr. Youssef Idrissi']
        ];
        
        foreach ($cdms as $cdm) {
            $sql = "INSERT IGNORE INTO cdm (nom, adresse, telephone, email, contact_nom, active, created_at) VALUES (?, ?, ?, ?, ?, 1, NOW())";
            $db->query($sql, $cdm);
            echo "  ✅ " . $cdm[0] . "<br>";
        }
    }
    
    // 3. Corriger les dossiers retournés sans acte
    echo "<h3>📋 Correction des dossiers sans acte</h3>";
    
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE acte_id IS NULL";
    $result = $db->single($sql);
    echo "• Dossiers sans acte: " . $result['count'] . "<br>";
    
    if ($result['count'] > 0) {
        echo "🔧 Attribution d'actes par défaut...<br>";
        
        // Récupérer le premier acte disponible
        $sql = "SELECT id FROM actes ORDER BY id LIMIT 1";
        $first_acte = $db->single($sql);
        
        if ($first_acte) {
            $sql = "UPDATE dossiers_retournes SET acte_id = ? WHERE acte_id IS NULL";
            $db->query($sql, [$first_acte['id']]);
            echo "  ✅ Actes attribués<br>";
        }
    }
    
    // 4. Vérifier les raisons de retour
    echo "<h3>❓ Correction des raisons de retour</h3>";
    
    $sql = "SELECT COUNT(*) as count FROM raisons_retour";
    $result = $db->single($sql);
    echo "• Raisons existantes: " . $result['count'] . "<br>";
    
    if ($result['count'] < 10) {
        echo "📦 Ajout de raisons de retour...<br>";
        
        $raisons = [
            ['Document manquant', 'Un ou plusieurs documents requis sont manquants dans le dossier'],
            ['Signature manquante', 'La signature du patient ou du médecin est manquante'],
            ['Informations incomplètes', 'Les informations fournies sont incomplètes ou erronées'],
            ['Erreur d\'identification', 'Erreur dans le numéro d\'adhérent ou les informations d\'identification'],
            ['Document illisible', 'Un ou plusieurs documents sont illisibles ou de mauvaise qualité'],
            ['Acte non couvert', 'L\'acte médical n\'est pas couvert par l\'assurance ou nécessite une autorisation'],
            ['Date expirée', 'La date de validité du document est expirée ou antérieure à la période de couverture'],
            ['Doublon', 'Le dossier est un doublon d\'un dossier déjà traité ou en cours de traitement'],
            ['Montant incorrect', 'Le montant facturé ne correspond pas au tarif conventionné'],
            ['Prescription manquante', 'L\'ordonnance ou la prescription médicale est manquante'],
            ['Cachet médical absent', 'Le cachet du médecin traitant est absent ou illisible'],
            ['Formulaire incomplet', 'Le formulaire de remboursement est incomplet ou mal rempli']
        ];
        
        foreach ($raisons as $raison) {
            $sql = "INSERT IGNORE INTO raisons_retour (raison, description, created_at) VALUES (?, ?, NOW())";
            $db->query($sql, $raison);
            echo "  ✅ " . $raison[0] . "<br>";
        }
    }
    
    // 5. Créer des données de test complètes
    echo "<h3>🧪 Création de données de test</h3>";
    
    // Vérifier si le bordereau 10001 existe
    $sql = "SELECT COUNT(*) as count FROM bordereaux_retour WHERE numero = 10001";
    $result = $db->single($sql);
    
    if ($result['count'] == 0) {
        echo "📦 Création du bordereau 10001...<br>";
        
        $sql = "INSERT INTO bordereaux_retour (numero, cdm_id, date_bordereau, notes, created_by, created_at) VALUES (10001, 1, '2024-01-15', 'Bordereau de test', 1, NOW())";
        $db->query($sql);
        echo "  ✅ Bordereau créé<br>";
    }
    
    // Nettoyer les anciens dossiers de test
    $sql = "DELETE FROM dossiers_retournes WHERE numero_bordereau = 10001 AND cdm_id = 1";
    $db->query($sql);
    echo "🧹 Anciens dossiers de test supprimés<br>";
    
    // Créer de nouveaux dossiers de test avec toutes les données
    echo "📦 Création de nouveaux dossiers de test...<br>";
    
    $test_dossiers = [
        [
            'numero_dossier' => 'DOS-TEST-001',
            'numero_adherent' => 'ADH-123456',
            'nom' => 'ALAMI Mohammed',
            'beneficiaire' => 'ALAMI Mohammed',
            'acte_id' => 1,
            'montant' => 150.00,
            'raison_retour_id' => 1,
            'notes' => 'Dossier de test 1 - Consultation générale'
        ],
        [
            'numero_dossier' => 'DOS-TEST-002',
            'numero_adherent' => 'ADH-234567',
            'nom' => 'BENNANI Fatima',
            'beneficiaire' => 'BENNANI Fatima',
            'acte_id' => 3,
            'montant' => 200.00,
            'raison_retour_id' => 2,
            'notes' => 'Dossier de test 2 - Radiographie'
        ],
        [
            'numero_dossier' => 'DOS-TEST-003',
            'numero_adherent' => 'ADH-345678',
            'nom' => 'TAZI Ahmed',
            'beneficiaire' => 'TAZI Aicha',
            'acte_id' => 5,
            'montant' => 300.00,
            'raison_retour_id' => 3,
            'notes' => 'Dossier de test 3 - Échographie'
        ]
    ];
    
    foreach ($test_dossiers as $dossier) {
        // Vérifier que l'acte existe
        $sql = "SELECT id FROM actes WHERE id = ?";
        $acte_exists = $db->single($sql, [$dossier['acte_id']]);

        if (!$acte_exists) {
            // Prendre le premier acte disponible
            $sql = "SELECT id FROM actes ORDER BY id LIMIT 1";
            $first_acte = $db->single($sql);
            $dossier['acte_id'] = $first_acte['id'];
        }

        // Vérifier que la raison existe
        $sql = "SELECT id FROM raisons_retour WHERE id = ?";
        $raison_exists = $db->single($sql, [$dossier['raison_retour_id']]);

        if (!$raison_exists) {
            // Prendre la première raison disponible
            $sql = "SELECT id FROM raisons_retour ORDER BY id LIMIT 1";
            $first_raison = $db->single($sql);
            $dossier['raison_retour_id'] = $first_raison['id'];
        }

        $sql = "INSERT INTO dossiers_retournes (numero_bordereau, cdm_id, date_bordereau, numero_dossier,
                                              numero_adherent, nom, beneficiaire, acte_id, montant,
                                              raison_retour_id, date_retour, corrige, notes, created_by, created_at)
                VALUES (10001, 1, '2024-01-15', ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 1, NOW())";

        $params = [
            $dossier['numero_dossier'],
            $dossier['numero_adherent'],
            $dossier['nom'],
            $dossier['beneficiaire'],
            $dossier['acte_id'],
            $dossier['montant'],
            $dossier['raison_retour_id'],
            date('Y-m-d'),
            $dossier['notes']
        ];

        $db->query($sql, $params);
        echo "  ✅ " . $dossier['numero_dossier'] . " - " . $dossier['nom'] . " (Acte ID: " . $dossier['acte_id'] . ")<br>";
    }
    
    echo "<h3>🎉 Résumé des corrections:</h3>";
    echo "<ul>";
    echo "<li>✅ Actes médicaux: mis à jour</li>";
    echo "<li>✅ CDM: mis à jour</li>";
    echo "<li>✅ Raisons de retour: mis à jour</li>";
    echo "<li>✅ Dossiers de test: créés avec toutes les données</li>";
    echo "<li>✅ Bordereau 10001: prêt pour les tests</li>";
    echo "</ul>";
    
    echo "<h3>🔗 Tests disponibles:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank'>📝 Voir le bordereau corrigé</a></li>";
    echo "<li><a href='test_export.php' target='_blank'>📊 Test d'exportation</a></li>";
    echo "<li><a href='test_add_dossier.php' target='_blank'>➕ Test ajout dossier</a></li>";
    echo "<li><a href='direct_test.php' target='_blank'>🧪 Tests directs</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Erreur</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
    echo "<p>Trace: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correction des Données - LOT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h2, h3 {
            color: #333;
            border-bottom: 2px solid #ffc107;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
