<?php
/**
 * اختبار مباشر لصفحة الطباعة مع تسجيل دخول تلقائي
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // تسجيل دخول تلقائي
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🔍 اختبار مباشر لصفحة الطباعة</h2>";
    echo "<p>✅ تم تسجيل الدخول كـ: " . $_SESSION['username'] . "</p>";
    
    // نفس الكود الموجود في controllers/retours/print.php
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>📊 1. فحص وجود البوردرو</h3>";
    
    // التحقق من وجود البوردرو
    $sql = "SELECT COUNT(*) as count FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
    $result = $db->single($sql, [$numero_bordereau, $cdm_id]);
    
    if ($result['count'] === 0) {
        $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
        $result = $db->single($sql, [$numero_bordereau, $cdm_id]);
        echo "• البوردرو موجود في dossiers_retournes: " . $result['count'] . " دوسيه<br>";
    } else {
        echo "• البوردرو موجود في bordereaux_retour<br>";
    }
    
    echo "<h3>📊 2. جلب معلومات البوردرو</h3>";
    
    // جلب معلومات البوردرو
    $sql = "SELECT br.date_bordereau, br.notes, u.username as created_by_username
            FROM bordereaux_retour br
            JOIN users u ON br.created_by = u.id
            WHERE br.numero = ? AND br.cdm_id = ?
            LIMIT 1";
    $bordereau_info = $db->single($sql, [$numero_bordereau, $cdm_id]);
    
    if (!$bordereau_info) {
        $sql = "SELECT dr.date_bordereau, '' as notes, u.username as created_by_username
                FROM dossiers_retournes dr
                JOIN users u ON dr.created_by = u.id
                WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
                LIMIT 1";
        $bordereau_info = $db->single($sql, [$numero_bordereau, $cdm_id]);
    }
    
    if ($bordereau_info) {
        echo "• تاريخ البوردرو: " . $bordereau_info['date_bordereau'] . "<br>";
        echo "• منشئ البوردرو: " . $bordereau_info['created_by_username'] . "<br>";
    }
    
    echo "<h3>📊 3. جلب معلومات CDM</h3>";
    
    $sql = "SELECT * FROM cdm WHERE id = ?";
    $cdm = $db->single($sql, [$cdm_id]);
    
    if ($cdm) {
        echo "• اسم CDM: " . $cdm['nom'] . "<br>";
        echo "• عنوان CDM: " . $cdm['adresse'] . "<br>";
    }
    
    echo "<h3>📊 4. جلب الدوسيهات (نفس الكود في print.php)</h3>";
    
    // نفس الاستعلام الموجود في print.php
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• عدد الدوسيهات المُسترجعة: " . count($dossiers) . "<br>";
    
    echo "<h4>📋 الدوسيهات الأولية:</h4>";
    foreach ($dossiers as $index => $dossier) {
        echo ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - " . $dossier['nom'] . "<br>";
    }
    
    echo "<h3>📊 5. إضافة الأسباب المتعددة (نفس الكود في print.php)</h3>";
    
    // نفس الكود الموجود في print.php لإضافة الأسباب
    foreach ($dossiers as &$dossier) {
        $sql = "SELECT dr.*, r.raison as raison
                FROM dossier_raisons dr
                JOIN raisons_retour r ON dr.raison_id = r.id
                WHERE dr.dossier_id = ?
                ORDER BY dr.created_at ASC";
        $raisons = $db->all($sql, [$dossier['id']]);
        
        // Si aucune raison n'est trouvée dans la nouvelle table, utiliser la raison principale
        if (empty($raisons) && !empty($dossier['raison_retour_nom'])) {
            $dossier['raisons'] = [['raison' => $dossier['raison_retour_nom']]];
        } else {
            $dossier['raisons'] = $raisons;
        }
        
        // Créer une chaîne de toutes les raisons pour l'affichage
        $raisons_str = [];
        foreach ($dossier['raisons'] as $raison) {
            $raisons_str[] = $raison['raison'];
        }
        $dossier['raisons_str'] = implode(', ', $raisons_str);
    }
    
    echo "<h4>📋 الدوسيهات بعد إضافة الأسباب:</h4>";
    foreach ($dossiers as $index => $dossier) {
        echo ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - " . $dossier['nom'] . " - أسباب: " . $dossier['raisons_str'] . "<br>";
    }
    
    echo "<h3>📊 6. فحص التكرارات النهائي</h3>";
    
    $unique_dossiers = [];
    $duplicates_found = [];
    
    foreach ($dossiers as $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($unique_dossiers[$key])) {
            $duplicates_found[] = $key;
            echo "⚠️ <strong>تكرار مكتشف:</strong> " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . "<br>";
        } else {
            $unique_dossiers[$key] = true;
        }
    }
    
    if (empty($duplicates_found)) {
        echo "✅ <strong>لا توجد تكرارات في النتيجة النهائية</strong><br>";
    } else {
        echo "❌ <strong>توجد " . count($duplicates_found) . " تكرارات</strong><br>";
        
        echo "<h4>🔧 تحليل التكرارات:</h4>";
        foreach ($duplicates_found as $duplicate_key) {
            list($numero_dossier, $numero_adherent) = explode('_', $duplicate_key);
            
            // البحث عن جميع الدوسيهات المكررة
            $sql = "SELECT id, created_at FROM dossiers_retournes 
                    WHERE numero_bordereau = ? AND cdm_id = ? 
                    AND numero_dossier = ? AND numero_adherent = ?
                    ORDER BY id ASC";
            $duplicate_records = $db->all($sql, [$numero_bordereau, $cdm_id, $numero_dossier, $numero_adherent]);
            
            echo "• $numero_dossier - $numero_adherent موجود " . count($duplicate_records) . " مرات:<br>";
            foreach ($duplicate_records as $record) {
                echo "  - ID: " . $record['id'] . " - تاريخ الإنشاء: " . $record['created_at'] . "<br>";
            }
        }
    }
    
    echo "<h3>📊 7. الإحصائيات النهائية</h3>";
    
    $total_dossiers = count($dossiers);
    $dossiers_corriges = 0;
    $dossiers_non_corriges = 0;
    $montant_total = 0;
    
    foreach ($dossiers as $dossier) {
        if ($dossier['corrige']) {
            $dossiers_corriges++;
        } else {
            $dossiers_non_corriges++;
        }
        $montant_total += $dossier['montant'];
    }
    
    echo "• <strong>إجمالي الدوسيهات:</strong> $total_dossiers<br>";
    echo "• <strong>الدوسيهات المُصححة:</strong> $dossiers_corriges<br>";
    echo "• <strong>الدوسيهات في الانتظار:</strong> $dossiers_non_corriges<br>";
    echo "• <strong>المبلغ الإجمالي:</strong> " . number_format($montant_total, 2, ',', ' ') . " DH<br>";
    
    if (empty($duplicates_found)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ النتيجة: البيانات صحيحة!</h4>";
        echo "<p style='color: #155724;'>صفحة الطباعة ستعمل بشكل صحيح.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24; margin-top: 0;'>❌ مشكلة: توجد تكرارات!</h4>";
        echo "<p style='color: #721c24;'>يجب حل مشكلة التكرارات أولاً.</p>";
        echo "</div>";
        
        echo "<h4>🔧 حل المشكلة:</h4>";
        echo "<ul>";
        echo "<li><a href='fix_duplicates_final.php' target='_blank'>🔧 إصلاح التكرارات نهائياً</a></li>";
        echo "<li><a href='reset_test_data.php' target='_blank'>🔄 إعادة تعيين بيانات الاختبار</a></li>";
        echo "</ul>";
    }
    
    echo "<h3>🔗 الروابط:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank'>🖨️ صفحة الطباعة الفعلية</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank'>📝 صفحة التحرير</a></li>";
    echo "<li><a href='index.php?page=retours/export&numero_bordereau=10001&cdm_id=1' target='_blank'>📊 تصدير Excel</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>التتبع: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مباشر للطباعة - LOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
