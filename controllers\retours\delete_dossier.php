<?php
// Contrôleur pour la suppression d'un dossier retourné

// Vérifier si les paramètres nécessaires sont fournis
if (!isset($_GET['id']) || empty($_GET['id']) || !isset($_GET['numero']) || empty($_GET['numero']) || !isset($_GET['cdm_id']) || empty($_GET['cdm_id'])) {
    redirect('index.php?page=retours', 'Paramètres manquants.', 'danger');
}

$dossier_id = intval($_GET['id']);
$numero_bordereau = intval($_GET['numero']);
$cdm_id = intval($_GET['cdm_id']);

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du dossier
$sql = "SELECT dr.*, c.nom as cdm_nom
        FROM dossiers_retournes dr
        JOIN cdm c ON dr.cdm_id = c.id
        WHERE dr.id = ? AND dr.numero_bordereau = ? AND dr.cdm_id = ?";
$dossier = $db->single($sql, [$dossier_id, $numero_bordereau, $cdm_id]);

// Vérifier si le dossier existe
if (!$dossier) {
    redirect('index.php?page=retours/view&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Dossier non trouvé.', 'danger');
}

// Traitement de la confirmation de suppression
if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
    try {
        // Supprimer le dossier de la base de données
        $sql = "DELETE FROM dossiers_retournes WHERE id = ?";
        $db->query($sql, [$dossier_id]);

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Suppression d\'un dossier retourné', "Dossier #{$dossier['numero_dossier']} supprimé");

        // Rediriger vers la page de détails du bordereau
        redirect('index.php?page=retours/view&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Le dossier a été supprimé avec succès.', 'success');
    } catch (Exception $e) {
        redirect('index.php?page=retours/view&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Erreur lors de la suppression du dossier: ' . $e->getMessage(), 'danger');
    }
} else {
    // Afficher la page de confirmation
    $page_title = 'Supprimer un dossier retourné';
    $page = 'retours/delete_dossier';
    
    // Charger la vue
    require_once VIEWS_PATH . 'retours/delete_dossier.php';
}
?>
