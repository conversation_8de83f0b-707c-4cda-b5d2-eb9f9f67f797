<?php
// Vue pour la recherche de lots et dossiers avec séparation entre recherche simple et avancée
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-search"></i> Recherche</h1>
        <?php if ($total_results > 0): ?>
        <div>
            <a href="index.php?page=lots/search_print&search_type=<?php echo $search_type; ?>&search_term=<?php echo urlencode($search_term); ?>&cdm_id=<?php echo $cdm_id; ?>&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>&statut=<?php echo $statut; ?>" class="btn btn-outline-secondary" id="printResults">
                <i class="fas fa-print"></i> Imprimer les résultats
            </a>
            <div class="btn-group">
                <a href="index.php?page=lots/search_export&search_type=<?php echo $search_type; ?>&search_term=<?php echo urlencode($search_term); ?>&cdm_id=<?php echo $cdm_id; ?>&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>&statut=<?php echo $statut; ?>&format=csv" class="btn btn-success">
                    <i class="fas fa-file-excel"></i> Exporter Excel
                </a>
                <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="visually-hidden">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="index.php?page=lots/search_export&search_type=<?php echo $search_type; ?>&search_term=<?php echo urlencode($search_term); ?>&cdm_id=<?php echo $cdm_id; ?>&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>&statut=<?php echo $statut; ?>&format=csv">Format CSV</a></li>
                    <li><a class="dropdown-item" href="index.php?page=lots/search_export&search_type=<?php echo $search_type; ?>&search_term=<?php echo urlencode($search_term); ?>&cdm_id=<?php echo $cdm_id; ?>&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>&statut=<?php echo $statut; ?>&format=xlsx">Format XLSX</a></li>
                </ul>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Onglets de recherche -->
    <ul class="nav nav-tabs mb-4" id="searchTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link <?php echo empty($_GET['advanced']) && empty($_GET['bon_search']) ? 'active' : ''; ?>" id="simple-tab" data-bs-toggle="tab" data-bs-target="#simple-search" type="button" role="tab" aria-controls="simple-search" aria-selected="<?php echo empty($_GET['advanced']) && empty($_GET['bon_search']) ? 'true' : 'false'; ?>">
                <i class="fas fa-search"></i> Recherche simple
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link <?php echo !empty($_GET['advanced']) ? 'active' : ''; ?>" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced-search" type="button" role="tab" aria-controls="advanced-search" aria-selected="<?php echo !empty($_GET['advanced']) ? 'true' : 'false'; ?>">
                <i class="fas fa-filter"></i> Recherche avancée
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link <?php echo !empty($_GET['bon_search']) ? 'active' : ''; ?>" id="bon-tab" data-bs-toggle="tab" data-bs-target="#bon-search" type="button" role="tab" aria-controls="bon-search" aria-selected="<?php echo !empty($_GET['bon_search']) ? 'true' : 'false'; ?>">
                <i class="fas fa-receipt"></i> Recherche et ajout du numéro de bon
            </button>
        </li>
    </ul>

    <div class="tab-content" id="searchTabsContent">
        <!-- Recherche simple -->
        <div class="tab-pane fade <?php echo empty($_GET['advanced']) && empty($_GET['bon_search']) ? 'show active' : ''; ?>" id="simple-search" role="tabpanel" aria-labelledby="simple-tab">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-search"></i> Recherche simple</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="index.php" class="row g-3">
                        <input type="hidden" name="page" value="lots/search">

                        <div class="col-md-4">
                            <label for="search_type" class="form-label">Type de recherche</label>
                            <select class="form-select" id="search_type" name="search_type">
                                <option value="dossier" <?php echo $search_type === 'dossier' ? 'selected' : ''; ?>>Dossier (N° dossier, nom, bénéficiaire)</option>
                                <option value="lot" <?php echo $search_type === 'lot' ? 'selected' : ''; ?>>Lot (N° lot)</option>
                                <option value="adherent" <?php echo $search_type === 'adherent' ? 'selected' : ''; ?>>Adhérent (N° adhérent exact)</option>
                            </select>
                        </div>

                        <div class="col-md-8">
                            <label for="search_term" class="form-label">Terme de recherche</label>
                            <input type="text" class="form-control" id="search_term" name="search_term" value="<?php echo $search_term; ?>" placeholder="Entrez votre terme de recherche...">
                        </div>

                        <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Rechercher
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Recherche avancée -->
        <div class="tab-pane fade <?php echo !empty($_GET['advanced']) ? 'show active' : ''; ?>" id="advanced-search" role="tabpanel" aria-labelledby="advanced-tab">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-filter"></i> Recherche avancée</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="index.php" class="row g-3">
                        <input type="hidden" name="page" value="lots/search">
                        <input type="hidden" name="advanced" value="1">

                        <div class="col-md-4">
                            <label for="search_type_adv" class="form-label">Type de recherche</label>
                            <select class="form-select" id="search_type_adv" name="search_type">
                                <option value="dossier" <?php echo $search_type === 'dossier' ? 'selected' : ''; ?>>Dossier (N° dossier, nom, bénéficiaire)</option>
                                <option value="lot" <?php echo $search_type === 'lot' ? 'selected' : ''; ?>>Lot (N° lot)</option>
                                <option value="adherent" <?php echo $search_type === 'adherent' ? 'selected' : ''; ?>>Adhérent (N° adhérent exact)</option>
                            </select>
                        </div>

                        <div class="col-md-8">
                            <label for="search_term_adv" class="form-label">Terme de recherche</label>
                            <input type="text" class="form-control" id="search_term_adv" name="search_term" value="<?php echo $search_term; ?>" placeholder="Entrez votre terme de recherche...">
                        </div>

                        <div class="col-md-4">
                            <label for="cdm_id" class="form-label">CDM</label>
                            <select class="form-select" id="cdm_id" name="cdm_id">
                                <option value="">Tous les CDM</option>
                                <?php foreach ($cdm_list as $cdm): ?>
                                    <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm_id == $cdm['id'] ? 'selected' : ''; ?>>
                                        <?php echo $cdm['nom']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label for="statut" class="form-label">Statut</label>
                            <select class="form-select" id="statut" name="statut">
                                <option value="">Tous les statuts</option>
                                <option value="recu" <?php echo $statut === 'recu' ? 'selected' : ''; ?>>Reçu</option>
                                <option value="non_recu" <?php echo $statut === 'non_recu' ? 'selected' : ''; ?>>Non reçu</option>
                            </select>
                        </div>

                        <div class="col-md-2">
                            <label for="date_debut" class="form-label">Date début</label>
                            <input type="text" class="form-control datepicker" id="date_debut" name="date_debut" value="<?php echo $date_debut; ?>" placeholder="jj/mm/aaaa">
                        </div>

                        <div class="col-md-2">
                            <label for="date_fin" class="form-label">Date fin</label>
                            <input type="text" class="form-control datepicker" id="date_fin" name="date_fin" value="<?php echo $date_fin; ?>" placeholder="jj/mm/aaaa">
                        </div>

                        <div class="col-12 text-end">
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> Réinitialiser
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Rechercher
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Recherche et ajout du numéro de bon -->
        <div class="tab-pane fade <?php echo !empty($_GET['bon_search']) ? 'show active' : ''; ?>" id="bon-search" role="tabpanel" aria-labelledby="bon-tab">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-receipt"></i> Recherche et ajout du numéro de bon</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="index.php" class="row g-3">
                        <input type="hidden" name="page" value="lots/search">
                        <input type="hidden" name="bon_search" value="1">

                        <div class="col-md-6">
                            <label for="search_term_bon" class="form-label">Terme de recherche</label>
                            <input type="text" class="form-control" id="search_term_bon" name="search_term" value="<?php echo $search_term; ?>" placeholder="N° dossier, N° adhérent, N° lot ou nom..." required>
                            <div class="form-text">Recherchez par numéro de dossier, numéro d'adhérent, numéro de lot ou nom</div>
                        </div>

                        <div class="col-md-6">
                            <label for="cdm_id_bon" class="form-label">CDM (optionnel)</label>
                            <select class="form-select" id="cdm_id_bon" name="cdm_id">
                                <option value="">Tous les CDM</option>
                                <?php foreach ($cdm_list as $cdm): ?>
                                    <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm_id == $cdm['id'] ? 'selected' : ''; ?>>
                                        <?php echo $cdm['nom']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Rechercher
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Résultats de recherche -->
    <?php if (!empty($search_term) || !empty($_GET['advanced']) && ($cdm_id > 0 || !empty($date_debut) || !empty($date_fin) || !empty($statut))): ?>
        <div class="card" id="search-results">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Résultats de recherche</h5>
                    <span class="badge bg-primary"><?php echo $total_results; ?> résultat(s) trouvé(s)</span>
                </div>
            </div>
            <div class="card-body">
                <?php if ($total_results > 0): ?>
                    <?php if (!empty($_GET['bon_search'])): ?>
                        <!-- Résultats de recherche pour l'ajout de numéro de bon -->
                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="results-table">
                                <thead>
                                    <tr>
                                        <th>N° Lot</th>
                                        <th>CDM</th>
                                        <th>N° Dossier</th>
                                        <th>N° Adhérent</th>
                                        <th>Nom</th>
                                        <th>Date réception</th>
                                        <th>Statut</th>
                                        <th>Acte</th>
                                        <th>N° Bon</th>
                                        <th class="text-end no-print">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results as $dossier): ?>
                                        <tr data-id="<?php echo $dossier['id']; ?>">
                                            <td>
                                                <a href="index.php?page=lots/view&id=<?php echo $dossier['lot_id']; ?>" class="text-decoration-none">
                                                    <?php echo $dossier['numero_lot']; ?>
                                                </a>
                                            </td>
                                            <td><?php echo $dossier['cdm_nom']; ?></td>
                                            <td><?php echo $dossier['numero_dossier']; ?></td>
                                            <td><?php echo $dossier['numero_adherent']; ?></td>
                                            <td><?php echo $dossier['nom']; ?></td>
                                            <td><?php echo formatDate($dossier['date_reception']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $dossier['recu'] ? 'success' : 'warning'; ?>">
                                                    <?php echo $dossier['recu'] ? 'Reçu' : 'Non reçu'; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $dossier['acte_nom']; ?></td>
                                            <td>
                                                <form method="post" action="index.php?page=lots/update_bon" class="d-flex">
                                                    <input type="hidden" name="dossier_id" value="<?php echo $dossier['id']; ?>">
                                                    <input type="hidden" name="return_url" value="<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?>">
                                                    <div class="input-group input-group-sm">
                                                        <input type="text" class="form-control form-control-sm" name="numero_bon" value="<?php echo $dossier['numero_bon']; ?>" placeholder="N° bon">
                                                        <button type="submit" class="btn btn-sm btn-success">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                    </div>
                                                </form>
                                            </td>
                                            <td class="text-end no-print">
                                                <a href="index.php?page=dossiers/edit&id=<?php echo $dossier['id']; ?>" class="btn btn-sm btn-warning btn-action" data-bs-toggle="tooltip" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="index.php?page=lots/view&id=<?php echo $dossier['lot_id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir le lot">
                                                    <i class="fas fa-folder-open"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php elseif ($search_type === 'dossier' || $search_type === 'adherent'): ?>
                        <!-- Résultats de recherche pour les dossiers -->
                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="results-table">
                                <thead>
                                    <tr>
                                        <th>N° Lot</th>
                                        <th>CDM</th>
                                        <th>N° Dossier</th>
                                        <th>N° Adhérent</th>
                                        <th>Nom</th>
                                        <th>Bénéficiaire</th>
                                        <th>Acte</th>
                                        <th>Statut</th>
                                        <th class="text-end no-print">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results as $dossier): ?>
                                        <tr data-id="<?php echo $dossier['id']; ?>">
                                            <td>
                                                <a href="index.php?page=lots/view&id=<?php echo $dossier['lot_id']; ?>" class="text-decoration-none">
                                                    <?php echo $dossier['numero_lot']; ?>
                                                </a>
                                            </td>
                                            <td><?php echo $dossier['cdm_nom']; ?></td>
                                            <td><?php echo $dossier['numero_dossier']; ?></td>
                                            <td><?php echo $dossier['numero_adherent']; ?></td>
                                            <td><?php echo $dossier['nom']; ?></td>
                                            <td><?php echo $dossier['beneficiaire']; ?></td>
                                            <td><?php echo $dossier['acte_nom']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $dossier['recu'] ? 'success' : 'warning'; ?>">
                                                    <?php echo $dossier['recu'] ? 'Reçu' : 'Non reçu'; ?>
                                                </span>
                                            </td>
                                            <td class="text-end no-print">
                                                <a href="index.php?page=dossiers/edit&id=<?php echo $dossier['id']; ?>" class="btn btn-sm btn-warning btn-action" data-bs-toggle="tooltip" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="index.php?page=lots/view&id=<?php echo $dossier['lot_id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir le lot">
                                                    <i class="fas fa-folder-open"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php elseif ($search_type === 'lot'): ?>
                        <!-- Résultats de recherche pour les lots -->
                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="results-table">
                                <thead>
                                    <tr>
                                        <th>N° Lot</th>
                                        <th>CDM</th>
                                        <th>Date demande</th>
                                        <th>Date réception</th>
                                        <th>Dossiers</th>
                                        <th>Reçus</th>
                                        <th>Statut</th>
                                        <th class="text-end no-print">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results as $lot): ?>
                                        <tr data-id="<?php echo $lot['id']; ?>">
                                            <td><?php echo $lot['numero_lot']; ?></td>
                                            <td><?php echo $lot['cdm_nom']; ?></td>
                                            <td><?php echo formatDate($lot['date_demande']); ?></td>
                                            <td><?php echo formatDate($lot['date_reception']); ?></td>
                                            <td><?php echo $lot['nb_dossiers']; ?></td>
                                            <td><?php echo $lot['nb_recus']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php
                                                    echo $lot['statut'] === 'en_attente' ? 'warning' :
                                                        ($lot['statut'] === 'recu' ? 'info' :
                                                        ($lot['statut'] === 'traite' ? 'success' : 'secondary'));
                                                ?>">
                                                    <?php
                                                        echo $lot['statut'] === 'en_attente' ? 'En attente' :
                                                            ($lot['statut'] === 'recu' ? 'Reçu' :
                                                            ($lot['statut'] === 'traite' ? 'Traité' : 'Archivé'));
                                                    ?>
                                                </span>
                                            </td>
                                            <td class="text-end no-print">
                                                <a href="index.php?page=lots/view&id=<?php echo $lot['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir le lot">
                                                    <i class="fas fa-folder-open"></i>
                                                </a>
                                                <a href="index.php?page=lots/edit&id=<?php echo $lot['id']; ?>" class="btn btn-sm btn-warning btn-action" data-bs-toggle="tooltip" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>

                    <?php if ($total_results >= 100): ?>
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle"></i> Les résultats sont limités aux 100 premiers enregistrements. Veuillez affiner votre recherche pour des résultats plus précis.
                        </div>
                    <?php endif; ?>

                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Aucun résultat ne correspond à votre recherche. Veuillez essayer avec d'autres termes ou critères.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Styles pour l'impression -->
<style>
@media print {
    .no-print, .no-print * {
        display: none !important;
    }

    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }

    body {
        font-size: 12pt;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    table, th, td {
        border: 1px solid #ddd;
    }

    th, td {
        padding: 8px;
        text-align: left;
    }

    th {
        background-color: #f2f2f2;
    }

    .badge {
        padding: 5px;
        border-radius: 4px;
    }

    .bg-success {
        background-color: #28a745 !important;
        color: white !important;
    }

    .bg-warning {
        background-color: #ffc107 !important;
        color: black !important;
    }

    .bg-info {
        background-color: #17a2b8 !important;
        color: white !important;
    }

    .bg-secondary {
        background-color: #6c757d !important;
        color: white !important;
    }

    .card {
        border: none;
    }

    .card-header {
        display: none;
    }

    .card-body {
        padding: 0;
    }

    h1 {
        text-align: center;
        margin-bottom: 20px;
    }

    @page {
        size: landscape;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des onglets
    const urlParams = new URLSearchParams(window.location.search);
    const advancedParam = urlParams.get('advanced');
    const bonSearchParam = urlParams.get('bon_search');

    if (advancedParam === '1') {
        document.getElementById('advanced-tab').click();
    } else if (bonSearchParam === '1') {
        document.getElementById('bon-tab').click();
    } else {
        document.getElementById('simple-tab').click();
    }

    // Mettre en évidence la ligne qui vient d'être mise à jour
    const updatedId = urlParams.get('updated_id');
    if (updatedId) {
        const row = document.querySelector(`tr[data-id="${updatedId}"]`);
        if (row) {
            row.classList.add('table-success');
            setTimeout(() => {
                row.classList.remove('table-success');
            }, 3000);
        }
    }
});
</script>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
