<?php
// Contrôleur pour la gestion des raisons de retour

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Titre de la page
$page_title = 'Gestion des raisons de retour';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de pagination
$page_num = isset($_GET['page_num']) ? intval($_GET['page_num']) : 1;
$items_per_page = ITEMS_PER_PAGE;
$offset = ($page_num - 1) * $items_per_page;

// Recherche
$search = isset($_GET['search']) ? clean($_GET['search']) : '';

// Vérifier si la colonne 'active' existe
try {
    $check_column = "SHOW COLUMNS FROM raisons_retour LIKE 'active'";
    $column_exists = $db->query($check_column)->rowCount() > 0;

    if (!$column_exists) {
        // Ajouter la colonne 'active' à la table raisons_retour
        $add_column = "ALTER TABLE raisons_retour ADD COLUMN active TINYINT(1) NOT NULL DEFAULT 1";
        $db->query($add_column);
    }
} catch (Exception $e) {
    // En cas d'erreur, continuer sans la colonne 'active'
}

// Construction de la requête SQL
$sql_count = "SELECT COUNT(*) as total FROM raisons_retour";
$sql = "SELECT * FROM raisons_retour";

$params = [];

// Appliquer le filtre de recherche
if (!empty($search)) {
    $sql_count .= " WHERE raison LIKE ? OR description LIKE ?";
    $sql .= " WHERE raison LIKE ? OR description LIKE ?";
    $search_param = "%$search%";
    $params = [$search_param, $search_param];
}

// Obtenir le nombre total de raisons
$result = $db->single($sql_count, $params);
$total_items = $result['total'];
$total_pages = ceil($total_items / $items_per_page);

// Ajouter l'ordre et la limite à la requête principale
$sql .= " ORDER BY raison ASC LIMIT ?, ?";
$params[] = $offset;
$params[] = $items_per_page;

// Exécuter la requête
$raisons_list = $db->all($sql, $params);

// Charger la vue
require_once VIEWS_PATH . 'admin/raisons/index.php';
?>
