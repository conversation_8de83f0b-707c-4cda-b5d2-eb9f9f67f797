/* Styles personnalisés pour l'application de gestion des lots */

/* Variables */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Général */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

main {
    flex: 1;
    position: relative;
    z-index: 1;
}

/* Assurer que le contenu principal ne chevauche pas avec la navbar */
.container, .container-fluid {
    position: relative;
    z-index: 1;
}

/* Corrections pour les éléments qui pourraient interférer */
.card, .table-responsive, .modal {
    position: relative;
    z-index: auto;
}

/* Force l'affichage correct des dropdowns Bootstrap */
.navbar .dropdown-menu {
    z-index: 1060 !important;
    position: absolute !important;
    will-change: transform;
    top: 100% !important;
    transform: translate3d(0px, 0px, 0px) !important;
}

/* Assurer que les dropdowns ne sont pas coupées */
.navbar-nav .dropdown {
    position: static !important;
}

@media (min-width: 992px) {
    .navbar-nav .dropdown {
        position: relative !important;
    }
}

.footer {
    margin-top: auto;
}

/* Tableaux */
.table-responsive {
    overflow-x: auto;
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Cartes */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    font-weight: 600;
}

/* Formulaires */
.form-label {
    font-weight: 500;
}

.required:after {
    content: ' *';
    color: var(--danger-color);
}

/* Badges */
.badge-status {
    font-size: 0.85rem;
    padding: 0.35em 0.65em;
}

.badge-en-attente {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.badge-recu {
    background-color: var(--info-color);
    color: var(--dark-color);
}

.badge-traite {
    background-color: var(--success-color);
    color: white;
}

.badge-archive {
    background-color: var(--secondary-color);
    color: white;
}

/* Boutons d'action */
.btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin-right: 0.25rem;
}

/* Statistiques */
.stat-card {
    border-left: 4px solid var(--primary-color);
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .card-body {
    padding: 1.25rem;
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    opacity: 0.3;
    position: absolute;
    right: 1rem;
    top: 1rem;
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 700;
}

.stat-card .stat-label {
    font-size: 0.875rem;
    color: var(--secondary-color);
    text-transform: uppercase;
}

.stat-card.primary {
    border-left-color: var(--primary-color);
}

.stat-card.success {
    border-left-color: var(--success-color);
}

.stat-card.warning {
    border-left-color: var(--warning-color);
}

.stat-card.danger {
    border-left-color: var(--danger-color);
}

/* Impression */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        font-size: 12pt;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
    
    .table {
        border-collapse: collapse !important;
    }
    
    .table td, .table th {
        background-color: #fff !important;
        border: 1px solid #ddd !important;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .stat-card .stat-value {
        font-size: 1.5rem;
    }
    
    .stat-card .stat-icon {
        font-size: 2rem;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Tableau de bord */
.dashboard-chart {
    height: 300px;
    margin-bottom: 2rem;
}

/* Recherche avancée */
.search-filters {
    background-color: var(--light-color);
    padding: 1.5rem;
    border-radius: 0.25rem;
    margin-bottom: 1.5rem;
}

/* Pagination personnalisée */
.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-link {
    color: var(--primary-color);
}

/* Tooltips */
.custom-tooltip {
    position: relative;
    display: inline-block;
}

.custom-tooltip .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: var(--dark-color);
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.custom-tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Loader */
.loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Améliorations modernes */
.card-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.gradient-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px 15px 0 0;
}

.gradient-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.gradient-warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.gradient-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

/* Boutons modernes */
.btn-modern {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Formulaires modernes */
.form-control-modern {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control-modern:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.input-group-modern .input-group-text {
    border: 2px solid #e9ecef;
    border-right: none;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px 0 0 10px;
}

.input-group-modern .form-control {
    border: 2px solid #e9ecef;
    border-left: none;
    border-radius: 0 10px 10px 0;
}

/* Tableaux modernes */
.table-modern {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.table-modern thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 15px;
}

.table-modern tbody tr {
    transition: all 0.3s ease;
}

.table-modern tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table-modern tbody td {
    padding: 12px 15px;
    border-color: #f1f3f4;
    vertical-align: middle;
}

/* Badges modernes */
.badge-modern {
    border-radius: 20px;
    padding: 8px 15px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75em;
}

/* Animations d'entrée */
.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-30px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(30px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Effets de survol */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Indicateurs de progression modernes */
.progress-modern {
    height: 20px;
    border-radius: 10px;
    background-color: #f1f3f4;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-modern .progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Responsive amélioré */
@media (max-width: 768px) {
    .card-modern {
        border-radius: 10px;
        margin-bottom: 15px;
    }

    .btn-modern {
        padding: 8px 20px;
        font-size: 0.9em;
    }

    .table-modern {
        font-size: 0.9em;
    }

    .table-modern thead th,
    .table-modern tbody td {
        padding: 8px 10px;
    }
}

/* Styles pour la navbar moderne */
.navbar-modern {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1050; /* Assurer que la navbar est au-dessus de tout */
}

.navbar-brand:hover {
    transform: scale(1.05);
    transition: all 0.3s ease;
}

.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(10px);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.dropdown-menu {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideDown 0.3s ease-out;
    z-index: 1060 !important; /* Priorité maximale pour les dropdowns */
    position: absolute !important;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    transition: all 0.3s ease;
    border-radius: 25px;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.dropdown-header {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.75rem;
}

/* Effets de survol pour les icônes */
.nav-link i {
    transition: all 0.3s ease;
}

.nav-link:hover i {
    transform: scale(1.1);
}

/* Animation pour le toggle mobile */
.navbar-toggler {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.navbar-toggler:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.2) !important;
}

.navbar-toggler:active {
    transform: scale(0.95);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

/* Effet de ripple pour les boutons */
.navbar-toggler::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.navbar-toggler:active::before {
    width: 100%;
    height: 100%;
}

/* Indicateur de statut animé */
.status-indicator {
    animation: pulse-status 2s infinite;
}

@keyframes pulse-status {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Amélioration des badges */
.badge {
    position: relative;
    overflow: hidden;
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.badge:hover::before {
    left: 100%;
}

/* Corrections spécifiques pour les dropdowns */
.navbar .dropdown {
    position: static;
}

.navbar .dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    z-index: 1060 !important;
    display: none;
    min-width: 200px;
    margin: 0;
    font-size: 0.875rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: rgba(255, 255, 255, 0.95);
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 15px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

.navbar .dropdown-menu.show {
    display: block !important;
}

.navbar .dropdown-toggle::after {
    margin-left: 0.5em;
}

/* إصلاح مشكلة Bootstrap dropdown z-index - الحل النهائي */
.dropdown-menu {
    z-index: 1060 !important;
}

.navbar-nav .dropdown-menu {
    z-index: 1060 !important;
    position: absolute !important;
}

/* الحل الحاسم لمشكلة z-index */
.navbar-nav .nav-item.dropdown .dropdown-menu {
    z-index: 1060 !important;
    position: absolute !important;
    top: calc(100% + 10px) !important;
    left: 0 !important;
    right: auto !important;
    transform: none !important;
    will-change: auto !important;
}

.navbar-nav .nav-item.dropdown .dropdown-menu.dropdown-menu-end {
    left: auto !important;
    right: 0 !important;
}

/* Force Bootstrap à respecter notre z-index */
.navbar .dropdown-menu.show {
    z-index: 1060 !important;
    position: absolute !important;
}

/* إصلاح للقوائم المنسدلة في الجانب الأيمن */
.navbar-nav .dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

/* تأكيد أن القوائم تظهر فوق المحتوى */
.navbar .nav-item.dropdown {
    position: relative;
}

.navbar .nav-item.dropdown .dropdown-menu {
    position: absolute !important;
    top: calc(100% + 5px) !important;
    z-index: 1060 !important;
    transform: translateY(0) !important;
}

/* إصلاح خاص للقوائم الفرعية */
.dropdown-submenu {
    position: relative;
}

.dropdown-submenu .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -1px;
}

/* Responsive pour la navbar */
@media (max-width: 991.98px) {
    .navbar-nav {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 15px;
        margin-top: 15px;
        z-index: 1055;
        position: relative;
    }

    .nav-link {
        margin: 5px 0;
        border-radius: 10px;
    }

    .dropdown-menu {
        background: rgba(255, 255, 255, 0.9) !important;
        margin-left: 20px;
        border-radius: 10px;
        position: relative !important;
        z-index: 1061 !important;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    }
}

/* Styles pour les graphiques et rapports */
.chart-container {
    position: relative;
    height: 250px;
    margin: 20px 0;
}

.chart-card {
    border-radius: 15px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.chart-header {
    border-radius: 15px 15px 0 0;
    padding: 15px 20px;
    font-weight: 600;
}

.chart-body {
    padding: 20px;
}

/* Styles pour les statistiques */
.stat-card-modern {
    border-radius: 12px;
    border: none;
    overflow: hidden;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card-modern:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.stat-icon-modern {
    font-size: 2.5rem;
    opacity: 0.8;
    margin-bottom: 10px;
}

.stat-value-modern {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label-modern {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Barres de progression modernes */
.progress-modern {
    height: 8px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.progress-modern .progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Tableaux de rapports */
.report-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.report-table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 15px;
    font-size: 0.85rem;
}

.report-table tbody tr {
    transition: all 0.3s ease;
}

.report-table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.report-table tbody td {
    padding: 12px 15px;
    border-color: #f1f3f4;
    vertical-align: middle;
}

/* Filtres de rapport */
.filter-card {
    border-radius: 15px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.filter-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 15px 15px 0 0;
    padding: 15px 20px;
}

.filter-body {
    padding: 25px;
}

/* Alertes personnalisées */
.alert-modern {
    border-radius: 12px;
    border: none;
    padding: 20px;
    margin: 20px 0;
}

.alert-info-modern {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1565c0;
}

.alert-success-modern {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #2e7d32;
}

.alert-warning-modern {
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
    color: #f57c00;
}

.alert-danger-modern {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    color: #c62828;
}

/* Responsive pour les graphiques */
@media (max-width: 768px) {
    .chart-container {
        height: 200px;
    }

    .stat-card-modern {
        margin-bottom: 15px;
    }

    .stat-value-modern {
        font-size: 1.5rem;
    }

    .chart-card {
        margin-bottom: 20px;
    }
}

/* Thème sombre (optionnel) */
@media (prefers-color-scheme: dark) {
    .card-modern {
        background-color: #2d3748;
        color: #e2e8f0;
    }

    .form-control-modern {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }

    .table-modern tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .navbar-modern {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
    }

    .dropdown-menu {
        background: rgba(45, 55, 72, 0.95) !important;
        color: #e2e8f0;
    }

    .dropdown-item {
        color: #e2e8f0;
    }

    .dropdown-item:hover {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
        color: white;
    }

    .chart-card {
        background-color: #2d3748;
        color: #e2e8f0;
    }

    .report-table {
        background-color: #2d3748;
        color: #e2e8f0;
    }
}
