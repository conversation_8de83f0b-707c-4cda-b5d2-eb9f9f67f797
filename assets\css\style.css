/* Styles personnalisés pour l'application de gestion des lots */

/* Variables */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Général */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

.footer {
    margin-top: auto;
}

/* Tableaux */
.table-responsive {
    overflow-x: auto;
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Cartes */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    font-weight: 600;
}

/* Formulaires */
.form-label {
    font-weight: 500;
}

.required:after {
    content: ' *';
    color: var(--danger-color);
}

/* Badges */
.badge-status {
    font-size: 0.85rem;
    padding: 0.35em 0.65em;
}

.badge-en-attente {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.badge-recu {
    background-color: var(--info-color);
    color: var(--dark-color);
}

.badge-traite {
    background-color: var(--success-color);
    color: white;
}

.badge-archive {
    background-color: var(--secondary-color);
    color: white;
}

/* Boutons d'action */
.btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin-right: 0.25rem;
}

/* Statistiques */
.stat-card {
    border-left: 4px solid var(--primary-color);
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .card-body {
    padding: 1.25rem;
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    opacity: 0.3;
    position: absolute;
    right: 1rem;
    top: 1rem;
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 700;
}

.stat-card .stat-label {
    font-size: 0.875rem;
    color: var(--secondary-color);
    text-transform: uppercase;
}

.stat-card.primary {
    border-left-color: var(--primary-color);
}

.stat-card.success {
    border-left-color: var(--success-color);
}

.stat-card.warning {
    border-left-color: var(--warning-color);
}

.stat-card.danger {
    border-left-color: var(--danger-color);
}

/* Impression */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        font-size: 12pt;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
    
    .table {
        border-collapse: collapse !important;
    }
    
    .table td, .table th {
        background-color: #fff !important;
        border: 1px solid #ddd !important;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .stat-card .stat-value {
        font-size: 1.5rem;
    }
    
    .stat-card .stat-icon {
        font-size: 2rem;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Tableau de bord */
.dashboard-chart {
    height: 300px;
    margin-bottom: 2rem;
}

/* Recherche avancée */
.search-filters {
    background-color: var(--light-color);
    padding: 1.5rem;
    border-radius: 0.25rem;
    margin-bottom: 1.5rem;
}

/* Pagination personnalisée */
.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-link {
    color: var(--primary-color);
}

/* Tooltips */
.custom-tooltip {
    position: relative;
    display: inline-block;
}

.custom-tooltip .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: var(--dark-color);
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.custom-tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Loader */
.loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
