<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Favicon -->
    <link rel="icon" href="assets/img/favicon.ico">

    <!-- Script pour la navbar moderne -->
    <script src="assets/js/navbar-modern.js" defer></script>
</head>
<body>
    <!-- Navbar Moderne -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-modern shadow-lg" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); backdrop-filter: blur(10px);">
        <div class="container-fluid px-4">
            <!-- Logo et Nom -->
            <a class="navbar-brand d-flex align-items-center" href="index.php?page=dashboard"
               style="font-weight: 700; font-size: 1.4rem; transition: all 0.3s ease;">
                <div class="me-3 p-2 rounded-circle position-relative"
                     style="background: rgba(255,255,255,0.2); backdrop-filter: blur(10px); transition: all 0.3s ease;">
                    <i class="fas fa-hospital-alt fa-lg text-white"></i>
                    <!-- Indicateur de statut (optionnel) -->
                    <span class="position-absolute top-0 start-100 translate-middle p-1 bg-success border border-light rounded-circle status-indicator">
                        <span class="visually-hidden">Système en ligne</span>
                    </span>
                </div>
                <div>
                    <div style="font-size: 1.2rem; line-height: 1; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        <?php echo APP_NAME; ?>
                    </div>
                    <small style="font-size: 0.7rem; opacity: 0.8; font-weight: 400;">
                        <i class="fas fa-cogs me-1" style="font-size: 0.6rem;"></i>Système de Gestion
                    </small>
                </div>
            </a>

            <!-- Bouton Toggle Mobile -->
            <button class="navbar-toggler border-0 position-relative" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain"
                    aria-controls="navbarMain" aria-expanded="false" aria-label="Toggle navigation"
                    style="background: rgba(255,255,255,0.1); border-radius: 10px; transition: all 0.3s ease;">
                <span class="navbar-toggler-icon"></span>
                <!-- Animation pour le bouton mobile -->
                <div class="position-absolute top-50 start-50 translate-middle" style="width: 100%; height: 100%; border-radius: 10px; background: rgba(255,255,255,0.1); transform: scale(0); transition: all 0.3s ease;"></div>
            </button>

            <?php if (isset($_SESSION['user_id'])): ?>
            <div class="collapse navbar-collapse" id="navbarMain">
                <!-- Menu Principal -->
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <!-- Tableau de Bord -->
                    <li class="nav-item me-2">
                        <a class="nav-link rounded-pill px-3 py-2 <?php echo $page === 'dashboard' ? 'active' : ''; ?>"
                           href="index.php?page=dashboard"
                           style="transition: all 0.3s ease; <?php echo $page === 'dashboard' ? 'background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);' : ''; ?>">
                            <i class="fas fa-chart-pie me-2"></i>
                            <span class="fw-semibold">Tableau de Bord</span>
                        </a>
                    </li>

                    <?php if (isAdmin()): ?>
                    <!-- Analyse Financière -->
                    <li class="nav-item me-2">
                        <a class="nav-link rounded-pill px-3 py-2 <?php echo $page === 'dashboard/finance' ? 'active' : ''; ?>"
                           href="index.php?page=dashboard/finance"
                           style="transition: all 0.3s ease; <?php echo $page === 'dashboard/finance' ? 'background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);' : ''; ?>">
                            <i class="fas fa-chart-line me-2"></i>
                            <span class="fw-semibold">Analyse Financière</span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- Gestion des Lots -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link dropdown-toggle rounded-pill px-3 py-2 <?php echo in_array($page, ['lots', 'lots/create', 'lots/edit', 'lots/view', 'lots/search']) ? 'active' : ''; ?>"
                           href="#" id="lotsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                           style="transition: all 0.3s ease; <?php echo in_array($page, ['lots', 'lots/create', 'lots/edit', 'lots/view', 'lots/search']) ? 'background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);' : ''; ?>">
                            <i class="fas fa-layer-group me-2"></i>
                            <span class="fw-semibold">Lots</span>
                        </a>
                        <ul class="dropdown-menu shadow-lg border-0" aria-labelledby="lotsDropdown"
                            style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 15px; margin-top: 10px;">
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=lots"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-list me-2 text-primary"></i>
                                    <span class="fw-semibold">Liste des Lots</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=lots/create"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-plus me-2 text-success"></i>
                                    <span class="fw-semibold">Nouveau Lot</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=lots/search"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-search me-2 text-info"></i>
                                    <span class="fw-semibold">Recherche</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Dossiers Retournés -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link dropdown-toggle rounded-pill px-3 py-2 <?php echo in_array($page, ['retours', 'retours/create', 'retours/edit', 'retours/view', 'retours/search']) ? 'active' : ''; ?>"
                           href="#" id="retoursDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                           style="transition: all 0.3s ease; <?php echo in_array($page, ['retours', 'retours/create', 'retours/edit', 'retours/view', 'retours/search']) ? 'background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);' : ''; ?>">
                            <i class="fas fa-undo-alt me-2"></i>
                            <span class="fw-semibold">Retours</span>
                        </a>
                        <ul class="dropdown-menu shadow-lg border-0" aria-labelledby="retoursDropdown"
                            style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 15px; margin-top: 10px;">
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=retours"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-list me-2 text-primary"></i>
                                    <span class="fw-semibold">Liste des Bordereaux</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=retours/create"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-plus me-2 text-success"></i>
                                    <span class="fw-semibold">Nouveau Bordereau</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=retours/search"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-search me-2 text-info"></i>
                                    <span class="fw-semibold">Recherche</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Rapports -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link dropdown-toggle rounded-pill px-3 py-2 <?php echo strpos($page, 'reports/') === 0 ? 'active' : ''; ?>"
                           href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                           style="transition: all 0.3s ease; <?php echo strpos($page, 'reports/') === 0 ? 'background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);' : ''; ?>">
                            <i class="fas fa-chart-bar me-2"></i>
                            <span class="fw-semibold">Rapports</span>
                        </a>
                        <ul class="dropdown-menu shadow-lg border-0" aria-labelledby="reportsDropdown"
                            style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 15px; margin-top: 10px;">
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=reports/cdm"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-hospital me-2 text-primary"></i>
                                    <span class="fw-semibold">Activité par CDM</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=reports/actes"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-procedures me-2 text-success"></i>
                                    <span class="fw-semibold">Analyse des Actes</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=reports/retours"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                                    <span class="fw-semibold">Analyse des Retours</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <?php if (isAdmin()): ?>
                    <!-- Administration -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link dropdown-toggle rounded-pill px-3 py-2 <?php echo strpos($page, 'admin/') === 0 ? 'active' : ''; ?>"
                           href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                           style="transition: all 0.3s ease; <?php echo strpos($page, 'admin/') === 0 ? 'background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);' : ''; ?>">
                            <i class="fas fa-cogs me-2"></i>
                            <span class="fw-semibold">Administration</span>
                        </a>
                        <ul class="dropdown-menu shadow-lg border-0" aria-labelledby="adminDropdown"
                            style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 15px; margin-top: 10px; min-width: 250px;">
                            <li>
                                <h6 class="dropdown-header text-muted fw-bold" style="font-size: 0.8rem; text-transform: uppercase; letter-spacing: 0.5px;">
                                    <i class="fas fa-users me-1"></i>Gestion des Utilisateurs
                                </h6>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=admin/users"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-users me-2 text-primary"></i>
                                    <span class="fw-semibold">Utilisateurs</span>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider mx-2" style="opacity: 0.3;"></li>
                            <li>
                                <h6 class="dropdown-header text-muted fw-bold" style="font-size: 0.8rem; text-transform: uppercase; letter-spacing: 0.5px;">
                                    <i class="fas fa-database me-1"></i>Configuration
                                </h6>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=admin/cdm"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-hospital me-2 text-success"></i>
                                    <span class="fw-semibold">CDM</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=admin/actes"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-procedures me-2 text-info"></i>
                                    <span class="fw-semibold">Actes</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=admin/raisons"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-exclamation-circle me-2 text-warning"></i>
                                    <span class="fw-semibold">Motifs de Retour</span>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider mx-2" style="opacity: 0.3;"></li>
                            <li>
                                <h6 class="dropdown-header text-muted fw-bold" style="font-size: 0.8rem; text-transform: uppercase; letter-spacing: 0.5px;">
                                    <i class="fas fa-tools me-1"></i>Système
                                </h6>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=admin/database"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-database me-2 text-danger"></i>
                                    <span class="fw-semibold">Base de Données</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=admin/logs"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-history me-2 text-secondary"></i>
                                    <span class="fw-semibold">Journaux d'Activité</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <?php endif; ?>
                </ul>

                <!-- Menu Utilisateur -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle rounded-pill px-3 py-2 d-flex align-items-center"
                           href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                           style="transition: all 0.3s ease; background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                            <div class="me-2 p-1 rounded-circle" style="background: rgba(255,255,255,0.2);">
                                <i class="fas fa-user-circle fa-lg"></i>
                            </div>
                            <div class="d-none d-md-block">
                                <div class="fw-semibold" style="font-size: 0.9rem; line-height: 1;"><?php echo isset($_SESSION['username']) ? $_SESSION['username'] : 'Utilisateur'; ?></div>
                                <small style="font-size: 0.7rem; opacity: 0.8;">
                                    <?php echo isAdmin() ? 'Administrateur' : 'Utilisateur'; ?>
                                </small>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow-lg border-0" aria-labelledby="userDropdown"
                            style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 15px; margin-top: 10px; min-width: 200px;">
                            <li>
                                <div class="dropdown-header text-center py-3">
                                    <div class="mb-2">
                                        <i class="fas fa-user-circle fa-3x text-primary"></i>
                                    </div>
                                    <div class="fw-bold"><?php echo isset($_SESSION['username']) ? $_SESSION['username'] : 'Utilisateur'; ?></div>
                                    <small class="text-muted">
                                        <?php echo isAdmin() ? 'Administrateur' : 'Utilisateur'; ?>
                                    </small>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider mx-2" style="opacity: 0.3;"></li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="index.php?page=profile"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-user-edit me-2 text-primary"></i>
                                    <span class="fw-semibold">Mon Profil</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2" href="#"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-cog me-2 text-secondary"></i>
                                    <span class="fw-semibold">Paramètres</span>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider mx-2" style="opacity: 0.3;"></li>
                            <li>
                                <a class="dropdown-item rounded-pill mx-2 my-1 py-2 text-danger" href="index.php?page=logout"
                                   style="transition: all 0.3s ease;">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    <span class="fw-semibold">Déconnexion</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
            <?php endif; ?>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        <?php displayFlashMessage(); ?>
    </div>

    <!-- Main Content -->
    <main class="py-4">
