<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Favicon -->
    <link rel="icon" href="assets/img/favicon.ico">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php?page=dashboard">
                <?php echo APP_NAME; ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain" aria-controls="navbarMain" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <?php if (isset($_SESSION['user_id'])): ?>
            <div class="collapse navbar-collapse" id="navbarMain">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>" href="index.php?page=dashboard">
                            <i class="fas fa-tachometer-alt"></i> Tableau de bord
                        </a>
                    </li>

                    <?php if (isAdmin()): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'dashboard/finance' ? 'active' : ''; ?>" href="index.php?page=dashboard/finance">
                            <i class="fas fa-chart-line"></i> Tableau de bord - Analyse financière
                        </a>
                    </li>
                    <?php endif; ?>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?php echo in_array($page, ['lots', 'lots/create', 'lots/edit', 'lots/view', 'lots/search']) ? 'active' : ''; ?>" href="#" id="lotsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-layer-group"></i> Lots
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="lotsDropdown">
                            <li><a class="dropdown-item" href="index.php?page=lots"><i class="fas fa-list"></i> Liste des lots</a></li>
                            <li><a class="dropdown-item" href="index.php?page=lots/create"><i class="fas fa-plus"></i> Nouveau lot</a></li>
                            <li><a class="dropdown-item" href="index.php?page=lots/search"><i class="fas fa-search"></i> Recherche</a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?php echo in_array($page, ['retours', 'retours/create', 'retours/edit', 'retours/view', 'retours/search']) ? 'active' : ''; ?>" href="#" id="retoursDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-undo-alt"></i> Dossiers retournés
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="retoursDropdown">
                            <li><a class="dropdown-item" href="index.php?page=retours"><i class="fas fa-list"></i> Liste des bordereaux</a></li>
                            <li><a class="dropdown-item" href="index.php?page=retours/create"><i class="fas fa-plus"></i> Nouveau bordereau</a></li>
                            <li><a class="dropdown-item" href="index.php?page=retours/search"><i class="fas fa-search"></i> Recherche</a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?php echo strpos($page, 'reports/') === 0 ? 'active' : ''; ?>" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-chart-bar"></i> Rapports
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                            <li><a class="dropdown-item" href="index.php?page=reports/cdm"><i class="fas fa-hospital"></i> Activité par CDM</a></li>
                            <li><a class="dropdown-item" href="index.php?page=reports/actes"><i class="fas fa-procedures"></i> Analyse des actes</a></li>
                            <li><a class="dropdown-item" href="index.php?page=reports/retours"><i class="fas fa-exclamation-triangle"></i> Analyse des retours</a></li>
                        </ul>
                    </li>

                    <?php if (isAdmin()): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?php echo strpos($page, 'admin/') === 0 ? 'active' : ''; ?>" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-cogs"></i> Administration
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="index.php?page=admin/users"><i class="fas fa-users"></i> Utilisateurs</a></li>
                            <li><a class="dropdown-item" href="index.php?page=admin/cdm"><i class="fas fa-hospital"></i> CDM</a></li>
                            <li><a class="dropdown-item" href="index.php?page=admin/actes"><i class="fas fa-procedures"></i> Actes</a></li>
                            <li><a class="dropdown-item" href="index.php?page=admin/raisons"><i class="fas fa-exclamation-circle"></i> Raisons de retour</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.php?page=admin/database"><i class="fas fa-database"></i> Base de données</a></li>
                            <li><a class="dropdown-item" href="index.php?page=admin/logs"><i class="fas fa-history"></i> Journaux d'activité</a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i> <?php echo isset($_SESSION['username']) ? $_SESSION['username'] : 'Utilisateur'; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="index.php?page=profile"><i class="fas fa-user"></i> Mon profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.php?page=logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
            <?php endif; ?>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        <?php displayFlashMessage(); ?>
    </div>

    <!-- Main Content -->
    <main class="py-4">
