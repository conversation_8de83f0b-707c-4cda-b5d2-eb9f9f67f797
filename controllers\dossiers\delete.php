<?php
// Contrôleur pour la suppression d'un dossier

// Vérifier si l'ID du dossier est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=lots', 'ID du dossier non spécifié.', 'danger');
}

$dossier_id = intval($_GET['id']);
$lot_id = isset($_GET['lot_id']) ? intval($_GET['lot_id']) : 0;

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du dossier
$sql = "SELECT d.*, l.numero_lot, l.cdm_id, c.nom as cdm_nom
        FROM dossiers d
        JOIN lots l ON d.lot_id = l.id
        JOIN cdm c ON l.cdm_id = c.id
        WHERE d.id = ?";
$dossier = $db->single($sql, [$dossier_id]);

// Vérifier si le dossier existe
if (!$dossier) {
    redirect('index.php?page=lots', 'Dossier non trouvé.', 'danger');
}

// Si le lot_id n'est pas fourni, utiliser celui du dossier
if ($lot_id === 0) {
    $lot_id = $dossier['lot_id'];
}

// Traitement de la confirmation de suppression
if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
    try {
        // Supprimer le dossier de la base de données
        $sql = "DELETE FROM dossiers WHERE id = ?";
        $db->query($sql, [$dossier_id]);

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Suppression de dossier', "Dossier #{$dossier['numero_dossier']} supprimé");

        // Rediriger vers la page de détails du lot
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Le dossier a été supprimé avec succès.', 'success');
    } catch (Exception $e) {
        redirect('index.php?page=lots/view&id=' . $lot_id, 'Erreur lors de la suppression du dossier: ' . $e->getMessage(), 'danger');
    }
} else {
    // Afficher la page de confirmation
    $page_title = 'Supprimer un dossier';
    $page = 'dossiers/delete';
    
    // Charger la vue
    require_once VIEWS_PATH . 'dossiers/delete.php';
}
?>
