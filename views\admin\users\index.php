<?php
// Vue pour la gestion des utilisateurs
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-users"></i> Gestion des utilisateurs</h1>
        <div>
            <a href="index.php?page=admin/users/create" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Nouvel utilisateur
            </a>
        </div>
    </div>
    
    <!-- Formulaire de recherche -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="admin/users">
                
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" value="<?php echo $search; ?>" placeholder="Rechercher un utilisateur...">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
                
                <div class="col-md-4 text-end">
                    <?php if (!empty($search)): ?>
                        <a href="index.php?page=admin/users" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Réinitialiser
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Liste des utilisateurs -->
    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list"></i> Liste des utilisateurs</h5>
                <span class="badge bg-primary"><?php echo $total_items; ?> utilisateur(s)</span>
            </div>
        </div>
        <div class="card-body">
            <?php if (count($users_list) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th>Nom d'utilisateur</th>
                                <th>Email</th>
                                <th>Rôle</th>
                                <th>Dernière connexion</th>
                                <th>Statut</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users_list as $user): ?>
                                <tr>
                                    <td><?php echo $user['username']; ?></td>
                                    <td><?php echo $user['email']; ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['role'] === 'admin' ? 'danger' : 'info'; ?>">
                                            <?php echo $user['role'] === 'admin' ? 'Administrateur' : 'Utilisateur'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo !empty($user['last_login']) ? formatDate($user['last_login'], DATETIME_FORMAT) : 'Jamais'; ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['active'] ? 'success' : 'danger'; ?>">
                                            <?php echo $user['active'] ? 'Actif' : 'Inactif'; ?>
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        <a href="index.php?page=admin/users/edit&id=<?php echo $user['id']; ?>" class="btn btn-sm btn-warning btn-action" data-bs-toggle="tooltip" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                            <a href="index.php?page=admin/users/delete&id=<?php echo $user['id']; ?>" class="btn btn-sm btn-danger btn-action confirm-delete" data-bs-toggle="tooltip" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Pagination des utilisateurs">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?php echo $page_num <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="index.php?page=admin/users&page_num=<?php echo $page_num - 1; ?>&search=<?php echo $search; ?>">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>
                            
                            <?php for ($i = max(1, $page_num - 2); $i <= min($total_pages, $page_num + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page_num ? 'active' : ''; ?>">
                                    <a class="page-link" href="index.php?page=admin/users&page_num=<?php echo $i; ?>&search=<?php echo $search; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <li class="page-item <?php echo $page_num >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="index.php?page=admin/users&page_num=<?php echo $page_num + 1; ?>&search=<?php echo $search; ?>">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun utilisateur trouvé.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
