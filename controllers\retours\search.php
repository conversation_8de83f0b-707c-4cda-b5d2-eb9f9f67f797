<?php
// Contrôleur pour la recherche de dossiers retournés

// Titre de la page
$page_title = 'Recherche de dossiers retournés';
$page = 'retours/search';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de recherche
$search_type = isset($_GET['search_type']) ? clean($_GET['search_type']) : 'dossier';
$search_term = isset($_GET['search_term']) ? clean($_GET['search_term']) : '';
$cdm_id = isset($_GET['cdm_id']) ? intval($_GET['cdm_id']) : 0;
$raison_id = isset($_GET['raison_id']) ? intval($_GET['raison_id']) : 0;
$corrige = isset($_GET['corrige']) ? clean($_GET['corrige']) : '';
$date_debut = isset($_GET['date_debut']) ? clean($_GET['date_debut']) : '';
$date_fin = isset($_GET['date_fin']) ? clean($_GET['date_fin']) : '';

// Résultats de recherche
$results = [];
$total_results = 0;

// Effectuer la recherche si un terme est fourni
if (!empty($search_term) || $cdm_id > 0 || $raison_id > 0 || $corrige !== '' || !empty($date_debut) || !empty($date_fin)) {
    // Recherche de dossiers retournés
    if ($search_type === 'dossier') {
        $sql = "SELECT dr.*, c.nom as cdm_nom, a.nom as acte_nom, r.raison as raison_retour_nom
                FROM dossiers_retournes dr
                JOIN cdm c ON dr.cdm_id = c.id
                LEFT JOIN actes a ON dr.acte_id = a.id
                LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
                WHERE 1=1";
        
        $params = [];
        
        // Appliquer les filtres
        if (!empty($search_term)) {
            $sql .= " AND (dr.numero_dossier LIKE ? OR dr.numero_adherent LIKE ? OR dr.nom LIKE ? OR dr.beneficiaire LIKE ?)";
            $search_param = "%$search_term%";
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
        }
        
        if ($cdm_id > 0) {
            $sql .= " AND dr.cdm_id = ?";
            $params[] = $cdm_id;
        }
        
        if ($raison_id > 0) {
            $sql .= " AND dr.raison_retour_id = ?";
            $params[] = $raison_id;
        }
        
        if ($corrige === '1' || $corrige === '0') {
            $sql .= " AND dr.corrige = ?";
            $params[] = $corrige;
        }
        
        if (!empty($date_debut)) {
            $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
            $sql .= " AND dr.date_retour >= ?";
            $params[] = $date_debut_formatted;
        }
        
        if (!empty($date_fin)) {
            $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
            $sql .= " AND dr.date_retour <= ?";
            $params[] = $date_fin_formatted;
        }
        
        $sql .= " ORDER BY dr.created_at DESC LIMIT 100";
        
        $results = $db->all($sql, $params);
        $total_results = count($results);
    }
    // Recherche de bordereaux
    elseif ($search_type === 'bordereau') {
        $sql = "SELECT dr.numero_bordereau, dr.cdm_id, c.nom as cdm_nom, 
                       MIN(dr.date_bordereau) as date_bordereau,
                       COUNT(dr.id) as nb_dossiers,
                       SUM(CASE WHEN dr.corrige = 1 THEN 1 ELSE 0 END) as nb_corriges
                FROM dossiers_retournes dr
                JOIN cdm c ON dr.cdm_id = c.id
                WHERE 1=1";
        
        $params = [];
        
        // Appliquer les filtres
        if (!empty($search_term)) {
            $sql .= " AND (dr.numero_bordereau LIKE ? OR c.nom LIKE ?)";
            $search_param = "%$search_term%";
            $params[] = $search_param;
            $params[] = $search_param;
        }
        
        if ($cdm_id > 0) {
            $sql .= " AND dr.cdm_id = ?";
            $params[] = $cdm_id;
        }
        
        if ($raison_id > 0) {
            $sql .= " AND dr.raison_retour_id = ?";
            $params[] = $raison_id;
        }
        
        if ($corrige === '1' || $corrige === '0') {
            $sql .= " AND dr.corrige = ?";
            $params[] = $corrige;
        }
        
        if (!empty($date_debut)) {
            $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
            $sql .= " AND dr.date_bordereau >= ?";
            $params[] = $date_debut_formatted;
        }
        
        if (!empty($date_fin)) {
            $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
            $sql .= " AND dr.date_bordereau <= ?";
            $params[] = $date_fin_formatted;
        }
        
        $sql .= " GROUP BY dr.numero_bordereau, dr.cdm_id, c.nom
                  ORDER BY MIN(dr.date_bordereau) DESC LIMIT 100";
        
        $results = $db->all($sql, $params);
        $total_results = count($results);
    }
    // Recherche d'adhérent
    elseif ($search_type === 'adherent') {
        $sql = "SELECT dr.*, c.nom as cdm_nom, a.nom as acte_nom, r.raison as raison_retour_nom
                FROM dossiers_retournes dr
                JOIN cdm c ON dr.cdm_id = c.id
                LEFT JOIN actes a ON dr.acte_id = a.id
                LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
                WHERE dr.numero_adherent = ?";
        
        $params = [$search_term];
        
        // Filtres supplémentaires
        if ($cdm_id > 0) {
            $sql .= " AND dr.cdm_id = ?";
            $params[] = $cdm_id;
        }
        
        if ($raison_id > 0) {
            $sql .= " AND dr.raison_retour_id = ?";
            $params[] = $raison_id;
        }
        
        if ($corrige === '1' || $corrige === '0') {
            $sql .= " AND dr.corrige = ?";
            $params[] = $corrige;
        }
        
        if (!empty($date_debut)) {
            $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
            $sql .= " AND dr.date_retour >= ?";
            $params[] = $date_debut_formatted;
        }
        
        if (!empty($date_fin)) {
            $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
            $sql .= " AND dr.date_retour <= ?";
            $params[] = $date_fin_formatted;
        }
        
        $sql .= " ORDER BY dr.created_at DESC LIMIT 100";
        
        $results = $db->all($sql, $params);
        $total_results = count($results);
    }
}

// Obtenir la liste des CDM pour le filtre
$cdm_list = getCDMList();

// Obtenir la liste des raisons de retour pour le filtre
$raisons_list = getRaisonsRetourList();

// Charger la vue
require_once VIEWS_PATH . 'retours/search.php';
?>
