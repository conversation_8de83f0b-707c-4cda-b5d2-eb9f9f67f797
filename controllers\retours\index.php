<?php
// Contrôleur pour la liste des bordereaux de dossiers retournés

// Titre de la page
$page_title = 'Liste des bordereaux de retour';
$page = 'retours';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de pagination
$page_num = isset($_GET['page_num']) ? intval($_GET['page_num']) : 1;
$items_per_page = ITEMS_PER_PAGE;
$offset = ($page_num - 1) * $items_per_page;

// Filtres
$cdm_id = isset($_GET['cdm_id']) ? intval($_GET['cdm_id']) : 0;
$corrige = isset($_GET['corrige']) ? clean($_GET['corrige']) : '';
$date_debut = isset($_GET['date_debut']) ? clean($_GET['date_debut']) : '';
$date_fin = isset($_GET['date_fin']) ? clean($_GET['date_fin']) : '';
$search = isset($_GET['search']) ? clean($_GET['search']) : '';

// Construction de la requête SQL
$sql_count = "SELECT COUNT(DISTINCT dr.numero_bordereau) as total 
              FROM dossiers_retournes dr 
              JOIN cdm c ON dr.cdm_id = c.id 
              WHERE 1=1";

$sql = "SELECT dr.numero_bordereau, dr.cdm_id, c.nom as cdm_nom, 
               MIN(dr.date_bordereau) as date_bordereau,
               COUNT(dr.id) as nb_dossiers,
               SUM(CASE WHEN dr.corrige = 1 THEN 1 ELSE 0 END) as nb_corriges
        FROM dossiers_retournes dr
        JOIN cdm c ON dr.cdm_id = c.id
        WHERE 1=1";

$params = [];

// Appliquer les filtres
if ($cdm_id > 0) {
    $sql .= " AND dr.cdm_id = ?";
    $sql_count .= " AND dr.cdm_id = ?";
    $params[] = $cdm_id;
}

if ($corrige === '1' || $corrige === '0') {
    $sql .= " AND dr.corrige = ?";
    $sql_count .= " AND dr.corrige = ?";
    $params[] = $corrige;
}

if (!empty($date_debut)) {
    $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
    $sql .= " AND dr.date_bordereau >= ?";
    $sql_count .= " AND dr.date_bordereau >= ?";
    $params[] = $date_debut_formatted;
}

if (!empty($date_fin)) {
    $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
    $sql .= " AND dr.date_bordereau <= ?";
    $sql_count .= " AND dr.date_bordereau <= ?";
    $params[] = $date_fin_formatted;
}

if (!empty($search)) {
    $sql .= " AND (dr.numero_bordereau LIKE ? OR c.nom LIKE ? OR dr.numero_dossier LIKE ? OR dr.numero_adherent LIKE ? OR dr.nom LIKE ?)";
    $sql_count .= " AND (dr.numero_bordereau LIKE ? OR c.nom LIKE ? OR dr.numero_dossier LIKE ? OR dr.numero_adherent LIKE ? OR dr.nom LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

// Grouper par bordereau
$sql .= " GROUP BY dr.numero_bordereau, dr.cdm_id, c.nom";

// Obtenir le nombre total de bordereaux
$result = $db->single($sql_count, $params);
$total_items = $result['total'];
$total_pages = ceil($total_items / $items_per_page);

// Ajouter l'ordre et la limite à la requête principale
$sql .= " ORDER BY MIN(dr.date_bordereau) DESC LIMIT ?, ?";
$params[] = $offset;
$params[] = $items_per_page;

// Exécuter la requête
$bordereaux = $db->all($sql, $params);

// Obtenir la liste des CDM pour le filtre
$cdm_list = getCDMList();

// Charger la vue
require_once VIEWS_PATH . 'retours/index.php';
?>
