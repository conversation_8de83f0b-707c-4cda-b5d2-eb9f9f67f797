<?php
/**
 * Script pour tester le système automatiquement
 * Connecte automatiquement l'utilisateur admin
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    // Obtenir la connexion à la base de données
    $db = Database::getInstance();
    
    // Chercher l'utilisateur admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if (!$user) {
        // Créer l'utilisateur admin s'il n'existe pas
        $sql = "INSERT INTO users (username, password, email, nom, prenom, role, active, created_at) VALUES
                ('admin', ?, '<EMAIL>', 'Administrateur', 'Système', 'admin', 1, NOW())";
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $db->query($sql, [$password_hash]);
        
        // Récupérer l'utilisateur créé
        $user = $db->single("SELECT id, username, password, role, active FROM users WHERE username = 'admin'");
        echo "✅ Utilisateur admin créé avec succès<br>";
    }
    
    if (!$user['active']) {
        echo "❌ Le compte admin est désactivé<br>";
        exit;
    }
    
    // Mettre à jour la date de dernière connexion
    $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
    $db->query($sql, [$user['id']]);
    
    // Créer la session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['role'] = $user['role'];
    
    echo "✅ Connexion automatique réussie pour: " . $user['username'] . "<br>";
    echo "🔑 Rôle: " . $user['role'] . "<br>";
    echo "📅 Session créée<br><br>";
    
    // Rediriger vers la page de test
    $test_url = isset($_GET['test']) ? $_GET['test'] : 'dashboard';
    
    echo "<h3>🧪 Tests Disponibles:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php?page=dashboard'>📊 Tableau de bord</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1'>📝 Test de la page problématique</a></li>";
    echo "<li><a href='index.php?page=retours/list'>📋 Liste des retours</a></li>";
    echo "<li><a href='index.php?page=lots/list'>📦 Liste des lots</a></li>";
    echo "<li><a href='index.php?page=dashboard/finance'>💰 Analyse financière</a></li>";
    echo "<li><a href='index.php?page=reports/cdm'>📈 Rapports CDM</a></li>";
    echo "</ul>";
    
    echo "<h3>🔧 Outils de Maintenance:</h3>";
    echo "<ul>";
    echo "<li><a href='maintenance.php?key=LOT2024MAINTENANCE'>🛠️ Outils de maintenance</a></li>";
    echo "<li><a href='fix_database.php'>🔧 Corriger la base (nécessite admin)</a></li>";
    echo "<li><a href='reset_database.php'>🔄 Réinitialiser la base (nécessite admin)</a></li>";
    echo "</ul>";
    
    echo "<h3>📊 Informations de Session:</h3>";
    echo "<pre>";
    echo "User ID: " . $_SESSION['user_id'] . "\n";
    echo "Username: " . $_SESSION['username'] . "\n";
    echo "Role: " . $_SESSION['role'] . "\n";
    echo "Session ID: " . session_id() . "\n";
    echo "</pre>";
    
    // Auto-redirection si demandée
    if (isset($_GET['redirect'])) {
        $redirect_url = $_GET['redirect'];
        echo "<script>setTimeout(function(){ window.location.href = '$redirect_url'; }, 2000);</script>";
        echo "<p>🔄 Redirection automatique vers: <strong>$redirect_url</strong> dans 2 secondes...</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Erreur</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
    echo "<p>Trace: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Automatique - LOT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h2, h3 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        pre {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h2>🧪 Test Automatique du Système LOT</h2>
</body>
</html>
