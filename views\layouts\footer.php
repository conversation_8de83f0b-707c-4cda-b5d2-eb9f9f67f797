    </main>

    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <span class="text-muted">&copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?> v<?php echo APP_VERSION; ?></span>
                </div>
                <div class="col-md-6 text-end">
                    <span class="text-muted">Développé avec HafidMGEN</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/script.js"></script>

    <!-- Initialize DataTables -->
    <script>
        $(document).ready(function() {
            $('.datatable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
                },
                responsive: true
            });
        });
    </script>

    <!-- Script pour la gestion des actes multiples -->
    <?php if (isset($page) && $page === 'lots/view'): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const actesSelect = document.getElementById('actes');
            const addActeBtn = document.getElementById('add-acte');
            const removeActeBtn = document.getElementById('remove-acte');
            const selectedActesDiv = document.getElementById('selected-actes');
            const acteIdsInput = document.getElementById('acte_ids');
            const montantInput = document.getElementById('montant');

            // Tableau pour stocker les actes sélectionnés
            let selectedActes = [];

            // Fonction pour mettre à jour l'affichage des actes sélectionnés
            function updateSelectedActes() {
                // Vider la div
                selectedActesDiv.innerHTML = '';

                // Mettre à jour le champ caché avec les IDs
                acteIdsInput.value = selectedActes.map(acte => acte.id).join(',');

                // Calculer le montant total
                let totalMontant = 0;

                // Ajouter chaque acte à la div
                selectedActes.forEach(acte => {
                    const acteElement = document.createElement('div');
                    acteElement.className = 'badge bg-primary me-1 mb-1';
                    const prix = parseFloat(acte.prix) || 0;
                    acteElement.textContent = acte.nom + ' (' + prix.toFixed(2).replace('.', ',') + ' DH)';
                    selectedActesDiv.appendChild(acteElement);

                    // Ajouter au montant total
                    totalMontant += prix;
                });

                // Mettre à jour le champ montant
                montantInput.value = totalMontant.toFixed(2).replace('.', ',');
            }

            // Ajouter un acte à la sélection
            addActeBtn.addEventListener('click', function() {
                const selectedOptions = actesSelect.selectedOptions;

                for (let i = 0; i < selectedOptions.length; i++) {
                    const option = selectedOptions[i];
                    const acteId = option.value;
                    const acteName = option.textContent.split(' (')[0];
                    // Récupérer le prix directement depuis l'attribut data-prix pour éviter les problèmes de mise en cache
                    const actePrix = parseFloat(option.getAttribute('data-prix')) || 0;

                    // Vérifier si l'acte n'est pas déjà sélectionné
                    if (!selectedActes.some(acte => acte.id === acteId)) {
                        selectedActes.push({
                            id: acteId,
                            nom: acteName,
                            prix: actePrix
                        });
                    }
                }

                updateSelectedActes();
            });

            // Supprimer tous les actes sélectionnés
            removeActeBtn.addEventListener('click', function() {
                selectedActes = [];
                updateSelectedActes();
            });

            // Double-clic sur un acte pour l'ajouter directement
            actesSelect.addEventListener('dblclick', function() {
                addActeBtn.click();
            });
        });
    </script>
    <?php endif; ?>
</body>
</html>
