<?php
/**
 * اختبار مبسط للتأكد من البيانات
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// D<PERSON>marrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // Créer la session admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🧪 اختبار مبسط للبيانات</h2>";
    echo "<p>✅ Session admin créée</p>";
    
    // Paramètres du test
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>📊 1. الاستعلام الأساسي</h3>";
    
    $sql = "SELECT id, numero_dossier, numero_adherent, nom, beneficiaire, montant
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            ORDER BY id ASC";
    $basic_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• عدد الدوسيهات: " . count($basic_dossiers) . "<br>";
    
    echo "<h4>📋 البيانات الأساسية:</h4>";
    foreach ($basic_dossiers as $index => $dossier) {
        echo ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - " . $dossier['nom'] . "<br>";
    }
    
    echo "<h3>📊 2. الاستعلام مع الجداول المرتبطة (نفس استعلام print.php)</h3>";
    
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $joined_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• عدد الدوسيهات مع الجداول المرتبطة: " . count($joined_dossiers) . "<br>";
    
    echo "<h4>📋 البيانات مع الجداول المرتبطة:</h4>";
    foreach ($joined_dossiers as $index => $dossier) {
        echo ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - " . $dossier['nom'] . " - " . ($dossier['acte_nom'] ?? 'N/A') . "<br>";
    }
    
    echo "<h3>📊 3. فحص التكرارات</h3>";
    
    $unique_basic = [];
    $unique_joined = [];
    
    // فحص التكرارات في الاستعلام الأساسي
    foreach ($basic_dossiers as $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($unique_basic[$key])) {
            echo "⚠️ تكرار في الاستعلام الأساسي: " . $key . "<br>";
        } else {
            $unique_basic[$key] = true;
        }
    }
    
    // فحص التكرارات في الاستعلام مع الجداول المرتبطة
    foreach ($joined_dossiers as $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($unique_joined[$key])) {
            echo "⚠️ تكرار في الاستعلام مع الجداول المرتبطة: " . $key . "<br>";
        } else {
            $unique_joined[$key] = true;
        }
    }
    
    if (count($unique_basic) == count($basic_dossiers)) {
        echo "✅ لا توجد تكرارات في الاستعلام الأساسي<br>";
    }
    
    if (count($unique_joined) == count($joined_dossiers)) {
        echo "✅ لا توجد تكرارات في الاستعلام مع الجداول المرتبطة<br>";
    }
    
    echo "<h3>📊 4. مقارنة النتائج</h3>";
    
    if (count($basic_dossiers) == count($joined_dossiers)) {
        echo "✅ عدد الدوسيهات متطابق في الاستعلامين<br>";
    } else {
        echo "❌ عدد الدوسيهات غير متطابق:<br>";
        echo "• الاستعلام الأساسي: " . count($basic_dossiers) . "<br>";
        echo "• الاستعلام مع الجداول المرتبطة: " . count($joined_dossiers) . "<br>";
    }
    
    echo "<h3>📊 5. فحص البيانات المفصلة</h3>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='padding: 5px;'>المصدر</th>";
    echo "<th style='padding: 5px;'>#</th>";
    echo "<th style='padding: 5px;'>ID</th>";
    echo "<th style='padding: 5px;'>N° Dossier</th>";
    echo "<th style='padding: 5px;'>N° Adhérent</th>";
    echo "<th style='padding: 5px;'>Nom</th>";
    echo "</tr>";
    
    // عرض البيانات من الاستعلام الأساسي
    foreach ($basic_dossiers as $index => $dossier) {
        echo "<tr style='background-color: #e8f5e8;'>";
        echo "<td style='padding: 5px;'>أساسي</td>";
        echo "<td style='padding: 5px;'>" . ($index + 1) . "</td>";
        echo "<td style='padding: 5px;'>" . $dossier['id'] . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_dossier']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_adherent']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['nom']) . "</td>";
        echo "</tr>";
    }
    
    // عرض البيانات من الاستعلام مع الجداول المرتبطة
    foreach ($joined_dossiers as $index => $dossier) {
        echo "<tr style='background-color: #f0f8ff;'>";
        echo "<td style='padding: 5px;'>مرتبط</td>";
        echo "<td style='padding: 5px;'>" . ($index + 1) . "</td>";
        echo "<td style='padding: 5px;'>" . $dossier['id'] . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_dossier']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_adherent']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['nom']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🎉 النتيجة</h3>";
    
    if (count($basic_dossiers) == count($joined_dossiers) && 
        count($unique_basic) == count($basic_dossiers) && 
        count($unique_joined) == count($joined_dossiers)) {
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ البيانات صحيحة!</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ عدد الدوسيهات: " . count($basic_dossiers) . "</li>";
        echo "<li>✅ لا توجد تكرارات</li>";
        echo "<li>✅ الاستعلامات تعطي نفس النتائج</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<p><strong>المشكلة ليست في البيانات، بل في كود الاختبار السابق.</strong></p>";
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24; margin-top: 0;'>❌ توجد مشكلة في البيانات</h4>";
        echo "</div>";
    }
    
    echo "<h3>🔗 اختبار صفحة الطباعة الفعلية:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank' style='color: #28a745; font-weight: bold;'>🖨️ صفحة الطباعة</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank' style='color: #17a2b8; font-weight: bold;'>📝 صفحة التحرير</a></li>";
    echo "<li><a href='index.php?page=retours/export&numero_bordereau=10001&cdm_id=1' target='_blank' style='color: #ffc107; font-weight: bold;'>📊 تصدير Excel</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>التتبع: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مبسط - LOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        table {
            background: white;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            direction: ltr;
        }
    </style>
</head>
<body>
</body>
</html>
