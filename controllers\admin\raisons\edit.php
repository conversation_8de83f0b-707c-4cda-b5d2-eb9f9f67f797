<?php
// Contrôleur pour la modification d'une raison de retour

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Vérifier si l'ID est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=admin/raisons', 'ID de la raison non spécifié.', 'danger');
}

$id = intval($_GET['id']);

// Titre de la page
$page_title = 'Modifier une raison de retour';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations de la raison
$sql = "SELECT * FROM raisons_retour WHERE id = ?";
$raison = $db->single($sql, [$id]);

if (!$raison) {
    redirect('index.php?page=admin/raisons', 'Raison non trouvée.', 'danger');
}

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=admin/raisons/edit&id=' . $id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupération des données du formulaire
    $raison_libelle = clean($_POST['raison'] ?? '');
    $description = clean($_POST['description'] ?? '');
    $active = isset($_POST['active']) ? 1 : 0;

    // Validation des données
    $errors = [];

    if (empty($raison_libelle)) {
        $errors[] = 'Le libellé de la raison est obligatoire.';
    }

    // Vérifier si la raison existe déjà (sauf pour cette raison)
    $sql = "SELECT COUNT(*) as count FROM raisons_retour WHERE raison = ? AND id != ?";
    $result = $db->single($sql, [$raison_libelle, $id]);

    if ($result['count'] > 0) {
        $errors[] = 'Une raison avec ce libellé existe déjà.';
    }

    // Vérifier si la colonne 'active' existe
    try {
        $check_column = "SHOW COLUMNS FROM raisons_retour LIKE 'active'";
        $column_exists = $db->query($check_column)->rowCount() > 0;
    } catch (Exception $e) {
        $column_exists = false;
    }

    // Si aucune erreur, mettre à jour la raison
    if (empty($errors)) {
        if ($column_exists) {
            $sql = "UPDATE raisons_retour
                    SET raison = ?, description = ?, active = ?, updated_at = NOW()
                    WHERE id = ?";
            $params = [$raison_libelle, $description, $active, $id];
        } else {
            $sql = "UPDATE raisons_retour
                    SET raison = ?, description = ?, updated_at = NOW()
                    WHERE id = ?";
            $params = [$raison_libelle, $description, $id];
        }

        if ($db->query($sql, $params)) {
            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Modification d\'une raison de retour', 'ID: ' . $id . ', Raison: ' . $raison_libelle);

            redirect('index.php?page=admin/raisons', 'Raison modifiée avec succès.', 'success');
        } else {
            $errors[] = 'Erreur lors de la modification de la raison.';
        }
    }
}

// Récupérer les données du formulaire en cas d'erreur
$default_data = [
    'raison' => $raison['raison'],
    'description' => $raison['description'] ?? '',
    'active' => $raison['active'] ?? 1
];

$form_data = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $form_data = [
        'raison' => $_POST['raison'] ?? '',
        'description' => $_POST['description'] ?? '',
        'active' => isset($_POST['active']) ? 1 : 0
    ];
} else {
    $form_data = $default_data;
}

// Charger la vue
require_once VIEWS_PATH . 'admin/raisons/edit.php';
?>
