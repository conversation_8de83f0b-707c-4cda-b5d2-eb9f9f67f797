<?php
// Contrôleur pour l'exportation des CDM vers Excel

// Inclure la classe ExcelExporter
require_once __DIR__ . '/../../includes/ExcelExporter.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer tous les CDM
$sql = "SELECT * FROM cdm ORDER BY nom ASC";
$cdms = $db->all($sql);

// Préparer les données pour l'exportation
$headers = [
    'ID',
    'Nom',
    'Adresse',
    'Contact',
    'Téléphone',
    'Email',
    'Date de création'
];

$data = [];
foreach ($cdms as $cdm) {
    $data[] = [
        $cdm['id'],
        $cdm['nom'],
        $cdm['adresse'],
        $cdm['contact_nom'],
        $cdm['telephone'],
        $cdm['email'],
        formatDate($cdm['created_at'])
    ];
}

// Nom du fichier
$filename = 'Liste_CDM_' . date('Ymd');

// Format d'exportation (csv par défaut)
$format = isset($_GET['format']) && $_GET['format'] === 'xlsx' ? 'xlsx' : 'csv';

// Journaliser l'action
logActivity($_SESSION['user_id'], 'Exportation de la liste des CDM', "Format: $format");

// Exporter les données
ExcelExporter::export($data, $headers, $filename, $format);
?>
