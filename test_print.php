<?php
/**
 * Test de la page d'impression avec connexion automatique
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // Créer la session admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🖨️ Test de la Page d'Impression</h2>";
    echo "<p>✅ Session admin créée</p>";
    
    // Paramètres du test
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>📋 Vérification des données avant impression</h3>";
    
    // Récupérer les informations du CDM
    $sql = "SELECT * FROM cdm WHERE id = ?";
    $cdm = $db->single($sql, [$cdm_id]);
    
    if ($cdm) {
        echo "✅ CDM trouvé: " . $cdm['nom'] . "<br>";
    } else {
        echo "❌ CDM non trouvé<br>";
    }
    
    // Récupérer les dossiers avec toutes les informations (même requête que dans print.php)
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "<h4>📊 Dossiers trouvés: " . count($dossiers) . "</h4>";
    
    // Enrichir les dossiers avec les raisons multiples (même logique que dans print.php)
    foreach ($dossiers as &$dossier) {
        $sql = "SELECT dr.*, r.raison as raison
                FROM dossier_raisons dr
                JOIN raisons_retour r ON dr.raison_id = r.id
                WHERE dr.dossier_id = ?
                ORDER BY dr.created_at ASC";
        $raisons = $db->all($sql, [$dossier['id']]);
        
        // Si aucune raison n'est trouvée dans la nouvelle table, utiliser la raison principale
        if (empty($raisons) && !empty($dossier['raison_retour_nom'])) {
            $dossier['raisons'] = [['raison' => $dossier['raison_retour_nom']]];
        } else {
            $dossier['raisons'] = $raisons;
        }
        
        // Créer une chaîne de toutes les raisons pour l'affichage
        $raisons_str = [];
        foreach ($dossier['raisons'] as $raison) {
            $raisons_str[] = $raison['raison'];
        }
        $dossier['raisons_str'] = implode(', ', $raisons_str);
    }
    
    echo "<h4>📋 Aperçu des données pour impression:</h4>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>#</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>N° Dossier</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>N° Adhérent</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>Nom</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>Bénéficiaire</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>Acte</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>Montant</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>Raisons</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>Statut</th>";
    echo "</tr>";
    
    foreach ($dossiers as $index => $dossier) {
        echo "<tr>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>" . ($index + 1) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($dossier['numero_dossier']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($dossier['numero_adherent']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($dossier['nom']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($dossier['beneficiaire']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($dossier['acte_nom'] ?? 'N/A') . " (" . htmlspecialchars($dossier['acte_code'] ?? 'N/A') . ")</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: right;'>" . number_format($dossier['montant'], 2, ',', ' ') . " DH</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($dossier['raisons_str']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'>" . ($dossier['corrige'] ? '✓ Corrigé' : '✗ En attente') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Statistiques
    $total_dossiers = count($dossiers);
    $dossiers_corriges = 0;
    $dossiers_non_corriges = 0;
    $montant_total = 0;
    
    foreach ($dossiers as $dossier) {
        if ($dossier['corrige']) {
            $dossiers_corriges++;
        } else {
            $dossiers_non_corriges++;
        }
        $montant_total += $dossier['montant'];
    }
    
    echo "<h4>📊 Statistiques:</h4>";
    echo "<ul>";
    echo "<li><strong>Total dossiers:</strong> $total_dossiers</li>";
    echo "<li><strong>Dossiers corrigés:</strong> $dossiers_corriges</li>";
    echo "<li><strong>Dossiers en attente:</strong> $dossiers_non_corriges</li>";
    echo "<li><strong>Montant total:</strong> " . number_format($montant_total, 2, ',', ' ') . " DH</li>";
    echo "</ul>";
    
    echo "<h3>🖨️ Liens de test d'impression:</h3>";
    
    // Créer un lien direct avec session
    $print_url = "index.php?page=retours/print&numero=$numero_bordereau&cdm_id=$cdm_id";
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>🔗 Liens disponibles:</h4>";
    echo "<ul>";
    echo "<li><a href='$print_url' target='_blank' class='btn'>🖨️ Page d'impression (avec session)</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=$numero_bordereau&cdm_id=$cdm_id' target='_blank' class='btn'>📝 Page d'édition</a></li>";
    echo "<li><a href='index.php?page=retours/view&numero=$numero_bordereau&cdm_id=$cdm_id' target='_blank' class='btn'>👁️ Page de visualisation</a></li>";
    echo "<li><a href='index.php?page=retours/export&numero_bordereau=$numero_bordereau&cdm_id=$cdm_id' target='_blank' class='btn'>📥 Export Excel</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🔍 Vérifications supplémentaires:</h3>";
    
    // Vérifier les données uniques
    $unique_dossiers = [];
    foreach ($dossiers as $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($unique_dossiers[$key])) {
            echo "⚠️ <strong>Duplication détectée:</strong> " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . "<br>";
        } else {
            $unique_dossiers[$key] = true;
        }
    }
    
    if (count($unique_dossiers) == count($dossiers)) {
        echo "✅ <strong>Aucune duplication détectée</strong> - Tous les dossiers sont uniques<br>";
    }
    
    echo "<h3>🧪 Tests automatiques:</h3>";
    echo "<ul>";
    echo "<li><a href='test_add_dossier.php' target='_blank'>➕ Test ajout dossier</a></li>";
    echo "<li><a href='test_export.php' target='_blank'>📊 Test exportation</a></li>";
    echo "<li><a href='fix_data.php' target='_blank'>🔧 Correction des données</a></li>";
    echo "<li><a href='final_report.php' target='_blank'>📋 Rapport final</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Erreur</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
    echo "<p>Trace: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Impression - LOT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #6f42c1;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        .btn {
            display: inline-block;
            background: #6f42c1;
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            margin: 5px;
        }
        
        .btn:hover {
            background: #5a32a3;
            color: white;
            text-decoration: none;
        }
        
        table {
            background: white;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
</body>
</html>
