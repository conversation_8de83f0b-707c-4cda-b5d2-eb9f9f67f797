<?php
// Contrôleur pour la déconnexion

// Vérifier si l'utilisateur est connecté
if (isset($_SESSION['user_id'])) {
    // Enregistrer l'activité
    logActivity($_SESSION['user_id'], 'Déconnexion', 'Déconnexion réussie');
    
    // Supprimer le cookie de session
    if (isset($_COOKIE['remember_token'])) {
        // Supprimer le token de la base de données
        $db = Database::getInstance();
        $sql = "DELETE FROM user_tokens WHERE user_id = ?";
        $db->query($sql, [$_SESSION['user_id']]);
        
        // Supprimer le cookie
        setcookie('remember_token', '', time() - 3600, '/', '', false, true);
    }
    
    // Détruire la session
    session_unset();
    session_destroy();
}

// Rediriger vers la page de connexion
redirect('index.php?page=login', 'Vous avez été déconnecté avec succès.', 'info');
?>
