<?php
// Contrôleur pour la modification d'un acte médical

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Vérifier si l'ID est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=admin/actes', 'ID de l\'acte non spécifié.', 'danger');
}

$id = intval($_GET['id']);

// Titre de la page
$page_title = 'Modifier un acte médical';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations de l'acte
$sql = "SELECT * FROM actes WHERE id = ?";
$acte = $db->single($sql, [$id]);

if (!$acte) {
    redirect('index.php?page=admin/actes', 'Acte non trouvé.', 'danger');
}

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=admin/actes/edit&id=' . $id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupération des données du formulaire
    $nom = isset($_POST['nom']) ? clean($_POST['nom']) : '';
    $description = isset($_POST['description']) ? clean($_POST['description']) : '';
    $prix = isset($_POST['prix']) ? floatval(str_replace(',', '.', $_POST['prix'])) : 0;
    $active = isset($_POST['active']) ? 1 : 0;

    // Debug - Afficher les valeurs pour vérification
    // echo "Nom: $nom, Description: $description, Active: $active"; exit;

    // Validation des données
    $errors = [];

    if (empty($nom)) {
        $errors[] = 'Le nom de l\'acte est obligatoire.';
    }

    // Vérifier si le nom existe déjà (sauf pour cet acte)
    $sql = "SELECT COUNT(*) as count FROM actes WHERE nom = ? AND id != ?";
    $result = $db->single($sql, [$nom, $id]);

    if ($result['count'] > 0) {
        $errors[] = 'Un acte avec ce nom existe déjà.';
    }

    // Si aucune erreur, mettre à jour l'acte
    if (empty($errors)) {
        // Vérifier si la colonne 'active' existe
        try {
            $check_column = "SHOW COLUMNS FROM actes LIKE 'active'";
            $column_exists = $db->query($check_column)->rowCount() > 0;
        } catch (Exception $e) {
            $column_exists = false;
        }

        if ($column_exists) {
            $sql = "UPDATE actes
                    SET nom = ?, description = ?, prix = ?, active = ?, updated_at = NOW()
                    WHERE id = ?";
            $params = [$nom, $description, $prix, $active, $id];
        } else {
            $sql = "UPDATE actes
                    SET nom = ?, description = ?, prix = ?, updated_at = NOW()
                    WHERE id = ?";
            $params = [$nom, $description, $prix, $id];
        }

        if ($db->query($sql, $params)) {
            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Modification d\'un acte', 'ID: ' . $id . ', Nom: ' . $nom);

            redirect('index.php?page=admin/actes', 'Acte modifié avec succès.', 'success');
        } else {
            $errors[] = 'Erreur lors de la modification de l\'acte.';
        }
    }
}

// Récupérer les données du formulaire en cas d'erreur
$default_data = [
    'nom' => $acte['nom'],
    'description' => $acte['description'] ?? '',
    'prix' => isset($acte['prix']) ? $acte['prix'] : '0.00',
    'active' => isset($acte['active']) ? (int)$acte['active'] : 1
];

$form_data = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $form_data = [
        'nom' => isset($_POST['nom']) ? $_POST['nom'] : '',
        'description' => isset($_POST['description']) ? $_POST['description'] : '',
        'prix' => isset($_POST['prix']) ? $_POST['prix'] : '0.00',
        'active' => isset($_POST['active']) ? 1 : 0
    ];
} else {
    $form_data = $default_data;
}

// Charger la vue
require_once VIEWS_PATH . 'admin/actes/edit.php';
?>
