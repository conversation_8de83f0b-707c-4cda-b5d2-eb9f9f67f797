<?php
// Contrôleur pour la mise à jour des informations d'un bordereau de retour

// Vérifier si les paramètres du bordereau sont fournis
if (!isset($_GET['numero']) || empty($_GET['numero']) || !isset($_GET['cdm_id']) || empty($_GET['cdm_id'])) {
    redirect('index.php?page=retours', 'Paramètres du bordereau non spécifiés.', 'danger');
}

$numero_bordereau = intval($_GET['numero']);
$cdm_id = intval($_GET['cdm_id']);

// Titre de la page
$page_title = 'Modifier les informations du bordereau #' . $numero_bordereau;
$page = 'retours/update_bordereau';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Vérifier si le bordereau existe
$sql = "SELECT * FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
$bordereau = $db->single($sql, [$numero_bordereau, $cdm_id]);

if (!$bordereau) {
    // Vérifier si des dossiers existent avec ce numéro de bordereau (pour la rétrocompatibilité)
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $result = $db->single($sql, [$numero_bordereau, $cdm_id]);

    if ($result['count'] === 0) {
        redirect('index.php?page=retours', 'Bordereau non trouvé.', 'danger');
    } else {
        // Récupérer la date du bordereau et créer l'entrée dans bordereaux_retour
        $sql = "SELECT date_bordereau, created_by, created_at FROM dossiers_retournes
                WHERE numero_bordereau = ? AND cdm_id = ? LIMIT 1";
        $result = $db->single($sql, [$numero_bordereau, $cdm_id]);
        $date_bordereau = $result['date_bordereau'];

        // Insérer dans bordereaux_retour
        $sql = "INSERT INTO bordereaux_retour (numero, cdm_id, date_bordereau, created_by, created_at)
                VALUES (?, ?, ?, ?, ?)";
        $params = [$numero_bordereau, $cdm_id, $date_bordereau, $result['created_by'], $result['created_at']];
        $db->query($sql, $params);
        
        // Récupérer le bordereau nouvellement créé
        $sql = "SELECT * FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
        $bordereau = $db->single($sql, [$numero_bordereau, $cdm_id]);
    }
}

// Récupérer la liste des CDMs
$sql = "SELECT * FROM cdm WHERE active = 1 ORDER BY nom ASC";
$cdms = $db->all($sql);

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=retours/update_bordereau&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupérer et nettoyer les données du formulaire
    $new_cdm_id = isset($_POST['cdm_id']) ? intval($_POST['cdm_id']) : $cdm_id;
    $date_bordereau = isset($_POST['date_bordereau']) ? clean($_POST['date_bordereau']) : '';
    $notes = isset($_POST['notes']) ? clean($_POST['notes']) : '';

    // Valider les données
    $errors = [];

    if ($new_cdm_id <= 0) {
        $errors[] = 'Veuillez sélectionner un CDM.';
    }

    if (empty($date_bordereau)) {
        $errors[] = 'La date du bordereau est requise.';
    } else {
        // Convertir la date au format MySQL (YYYY-MM-DD)
        $date_bordereau = date('Y-m-d', strtotime(str_replace('/', '-', $date_bordereau)));
    }

    // S'il y a des erreurs, rediriger vers la page avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = $_POST;
        redirect('index.php?page=retours/update_bordereau&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Veuillez corriger les erreurs.', 'danger');
    }

    try {
        // Mettre à jour le bordereau dans la base de données
        $sql = "UPDATE bordereaux_retour 
                SET cdm_id = ?, date_bordereau = ?, notes = ?, updated_at = NOW()
                WHERE numero = ? AND cdm_id = ?";
        $params = [$new_cdm_id, $date_bordereau, $notes, $numero_bordereau, $cdm_id];
        $db->query($sql, $params);

        // Mettre à jour les dossiers associés si le CDM a changé
        if ($new_cdm_id != $cdm_id) {
            $sql = "UPDATE dossiers_retournes 
                    SET cdm_id = ?, date_bordereau = ?
                    WHERE numero_bordereau = ? AND cdm_id = ?";
            $params = [$new_cdm_id, $date_bordereau, $numero_bordereau, $cdm_id];
            $db->query($sql, $params);
        } else {
            // Mettre à jour uniquement la date du bordereau
            $sql = "UPDATE dossiers_retournes 
                    SET date_bordereau = ?
                    WHERE numero_bordereau = ? AND cdm_id = ?";
            $params = [$date_bordereau, $numero_bordereau, $cdm_id];
            $db->query($sql, $params);
        }

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Modification d\'un bordereau de retour', "Bordereau #$numero_bordereau modifié");

        // Rediriger vers la page d'édition du bordereau
        if ($new_cdm_id != $cdm_id) {
            redirect('index.php?page=retours/edit&numero=' . $numero_bordereau . '&cdm_id=' . $new_cdm_id, 'Le bordereau a été modifié avec succès.', 'success');
        } else {
            redirect('index.php?page=retours/edit&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Le bordereau a été modifié avec succès.', 'success');
        }

    } catch (Exception $e) {
        redirect('index.php?page=retours/update_bordereau&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Erreur lors de la modification du bordereau: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'cdm_id' => $bordereau['cdm_id'],
    'date_bordereau' => formatDate($bordereau['date_bordereau']),
    'notes' => $bordereau['notes'] ?? ''
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Charger la vue
require_once VIEWS_PATH . 'retours/update_bordereau.php';
?>
