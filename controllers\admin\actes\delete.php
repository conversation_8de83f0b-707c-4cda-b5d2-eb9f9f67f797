<?php
// Contrôleur pour la suppression d'un acte médical

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Vérifier si l'ID est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=admin/actes', 'ID de l\'acte non spécifié.', 'danger');
}

$id = intval($_GET['id']);

// Titre de la page
$page_title = 'Supprimer un acte médical';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations de l'acte
$sql = "SELECT * FROM actes WHERE id = ?";
$acte = $db->single($sql, [$id]);

if (!$acte) {
    redirect('index.php?page=admin/actes', 'Acte non trouvé.', 'danger');
}

// Vérifier si l'acte est utilisé dans des dossiers
$sql = "SELECT COUNT(*) as count FROM dossiers WHERE acte_id = ?";
$result = $db->single($sql, [$id]);
$is_used_dossiers = $result['count'] > 0;

$sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE acte_id = ?";
$result = $db->single($sql, [$id]);
$is_used_retours = $result['count'] > 0;

$is_used = $is_used_dossiers || $is_used_retours;

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=admin/actes/delete&id=' . $id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    try {
        // Vérifier si la colonne 'active' existe
        try {
            $check_column = "SHOW COLUMNS FROM actes LIKE 'active'";
            $column_exists = $db->query($check_column)->rowCount() > 0;
        } catch (Exception $e) {
            $column_exists = false;
        }

        if ($is_used) {
            if ($column_exists) {
                // Si l'acte est utilisé et que la colonne 'active' existe, on le désactive
                $sql = "UPDATE actes SET active = 0, updated_at = NOW() WHERE id = ?";
                $db->query($sql, [$id]);

                // Journaliser l'action
                logActivity($_SESSION['user_id'], 'Désactivation d\'un acte', 'ID: ' . $id . ', Nom: ' . $acte['nom']);

                redirect('index.php?page=admin/actes', 'L\'acte a été désactivé car il est utilisé dans des dossiers.', 'warning');
            } else {
                // Si la colonne 'active' n'existe pas, on ne peut pas désactiver l'acte
                redirect('index.php?page=admin/actes', 'Impossible de désactiver l\'acte car la colonne \'active\' n\'existe pas. Veuillez exécuter le script d\'ajout de la colonne.', 'danger');
            }

        } else {
            // Si l'acte n'est pas utilisé, on peut le supprimer
            $sql = "DELETE FROM actes WHERE id = ?";
            $db->query($sql, [$id]);

            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Suppression d\'un acte', 'ID: ' . $id . ', Nom: ' . $acte['nom']);

            redirect('index.php?page=admin/actes', 'L\'acte a été supprimé avec succès.', 'success');
        }
    } catch (Exception $e) {
        redirect('index.php?page=admin/actes/delete&id=' . $id, 'Erreur lors de la suppression: ' . $e->getMessage(), 'danger');
    }
}

// Charger la vue
require_once VIEWS_PATH . 'admin/actes/delete.php';
?>
