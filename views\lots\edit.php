<?php
// Vue pour la modification d'un lot
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-edit"></i> Modifier le lot #<?php echo $lot['numero_lot']; ?></h1>
        <div>
            <a href="index.php?page=lots/view&id=<?php echo $lot_id; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour au lot
            </a>
        </div>
    </div>

    <?php if (!empty($form_errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($form_errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-box"></i> Informations du lot</h5>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=lots/edit&id=<?php echo $lot_id; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="numero_lot" class="form-label">Numéro de lot <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="numero_lot" name="numero_lot" value="<?php echo $form_data['numero_lot']; ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="cdm_id" class="form-label">CDM <span class="text-danger">*</span></label>
                        <select class="form-select" id="cdm_id" name="cdm_id" required>
                            <option value="">Sélectionner un CDM</option>
                            <?php foreach ($cdm_list as $cdm): ?>
                                <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm['id'] == $form_data['cdm_id'] ? 'selected' : ''; ?>>
                                    <?php echo $cdm['nom']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="date_demande" class="form-label">Date de demande <span class="text-danger">*</span></label>
                        <input type="text" class="form-control datepicker" id="date_demande" name="date_demande" value="<?php echo $form_data['date_demande']; ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="date_reception" class="form-label">Date de réception</label>
                        <input type="text" class="form-control datepicker" id="date_reception" name="date_reception" value="<?php echo $form_data['date_reception']; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="statut" class="form-label">Statut <span class="text-danger">*</span></label>
                        <select class="form-select" id="statut" name="statut" required>
                            <option value="en_attente" <?php echo $form_data['statut'] === 'en_attente' ? 'selected' : ''; ?>>En attente</option>
                            <option value="recu" <?php echo $form_data['statut'] === 'recu' ? 'selected' : ''; ?>>Reçu</option>
                            <option value="traite" <?php echo $form_data['statut'] === 'traite' ? 'selected' : ''; ?>>Traité</option>
                            <option value="archive" <?php echo $form_data['statut'] === 'archive' ? 'selected' : ''; ?>>Archivé</option>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $form_data['notes']; ?></textarea>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="index.php?page=lots/view&id=<?php echo $lot_id; ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Initialiser les sélecteurs de date
        $('.datepicker').datepicker({
            format: 'dd/mm/yyyy',
            autoclose: true,
            todayHighlight: true,
            language: 'fr'
        });
    });
</script>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
