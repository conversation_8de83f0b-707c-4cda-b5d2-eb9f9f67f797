<?php
// Vue pour le tableau de bord d'analyse financière
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container-fluid">
    <!-- En-tête moderne -->
    <div class="card mb-4 border-0" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); border-radius: 20px;">
        <div class="card-body text-white p-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2" style="font-weight: 700;">
                        <i class="fas fa-chart-line me-3"></i>Analyse Financière
                    </h1>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-coins me-2"></i>Tableau de bord des performances financières
                    </p>
                </div>
                <div class="text-end">
                    <button id="printPage" class="btn btn-outline-light btn-lg rounded-pill me-2">
                        <i class="fas fa-print me-2"></i>Imprimer
                    </button>
                    <a href="index.php?page=dashboard/finance/export&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>&cdm_id=<?php echo $cdm_id; ?>&acte_id=<?php echo $acte_id; ?>"
                       class="btn btn-light btn-lg rounded-pill">
                        <i class="fas fa-file-excel me-2"></i>Exporter Excel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres modernes -->
    <div class="card mb-5 border-0 shadow-sm" style="border-radius: 15px;">
        <div class="card-header border-0 text-white" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 15px 15px 0 0;">
            <h6 class="mb-0 fw-bold">
                <i class="fas fa-filter me-2"></i>Filtres de Recherche
            </h6>
        </div>
        <div class="card-body p-4">
            <form action="index.php" method="get" class="row g-4">
                <input type="hidden" name="page" value="dashboard/finance">

                <div class="col-lg-3 col-md-6">
                    <label for="date_debut" class="form-label fw-bold">
                        <i class="fas fa-calendar-alt text-primary me-1"></i>Date de début
                    </label>
                    <input type="date" class="form-control form-control-lg" id="date_debut" name="date_debut"
                           value="<?php echo $date_debut; ?>" style="border-radius: 10px;">
                </div>

                <div class="col-lg-3 col-md-6">
                    <label for="date_fin" class="form-label fw-bold">
                        <i class="fas fa-calendar-check text-success me-1"></i>Date de fin
                    </label>
                    <input type="date" class="form-control form-control-lg" id="date_fin" name="date_fin"
                           value="<?php echo $date_fin; ?>" style="border-radius: 10px;">
                </div>

                <div class="col-lg-3 col-md-6">
                    <label for="cdm_id" class="form-label fw-bold">
                        <i class="fas fa-hospital text-info me-1"></i>CDM
                    </label>
                    <select class="form-select form-select-lg" id="cdm_id" name="cdm_id" style="border-radius: 10px;">
                        <option value="0">🏥 Tous les CDM</option>
                        <?php foreach ($cdm_list as $cdm): ?>
                            <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm_id == $cdm['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cdm['nom']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-lg-3 col-md-6">
                    <label for="acte_id" class="form-label fw-bold">
                        <i class="fas fa-procedures text-warning me-1"></i>Acte
                    </label>
                    <select class="form-select form-select-lg" id="acte_id" name="acte_id" style="border-radius: 10px;">
                        <option value="0">🩺 Tous les actes</option>
                        <?php foreach ($actes_list as $acte): ?>
                            <option value="<?php echo $acte['id']; ?>" <?php echo $acte_id == $acte['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($acte['nom']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary btn-lg rounded-pill me-2"
                            style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;">
                        <i class="fas fa-search me-2"></i>Filtrer
                    </button>
                    <a href="index.php?page=dashboard/finance" class="btn btn-outline-secondary btn-lg rounded-pill">
                        <i class="fas fa-undo me-2"></i>Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Résumé global moderne -->
    <div class="row mb-5">
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px;">
                <div class="card-header border-0 text-white" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); border-radius: 15px 15px 0 0;">
                    <h6 class="mb-0 fw-bold">
                        <i class="fas fa-chart-pie me-2"></i>Résumé Global
                    </h6>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <div class="card border-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px;">
                                <div class="card-body text-white text-center p-3">
                                    <i class="fas fa-folder-open fa-2x mb-2 opacity-75"></i>
                                    <h6 class="card-title mb-1">Nombre de dossiers</h6>
                                    <h3 class="mb-0 fw-bold"><?php echo number_format($total_data['nb_dossiers_total'] ?? 0, 0, ',', ' '); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-0" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); border-radius: 12px;">
                                <div class="card-body text-white text-center p-3">
                                    <i class="fas fa-coins fa-2x mb-2 opacity-75"></i>
                                    <h6 class="card-title mb-1">Montant total</h6>
                                    <h3 class="mb-0 fw-bold"><?php echo number_format($total_data['montant_total_global'] ?? 0, 2, ',', ' '); ?> DH</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-light p-3 rounded">
                        <h6 class="fw-bold mb-2"><i class="fas fa-info-circle text-primary me-1"></i>Détails de la période</h6>
                        <p class="mb-1">
                            <i class="fas fa-calendar text-primary me-2"></i>
                            <strong>Période:</strong> <?php echo date('d/m/Y', strtotime($date_debut)); ?> - <?php echo date('d/m/Y', strtotime($date_fin)); ?>
                        </p>
                        <?php if ($cdm_id > 0): ?>
                            <?php foreach ($cdm_list as $cdm): ?>
                                <?php if ($cdm['id'] == $cdm_id): ?>
                                    <p class="mb-1">
                                        <i class="fas fa-hospital text-success me-2"></i>
                                        <strong>CDM:</strong> <?php echo htmlspecialchars($cdm['nom']); ?>
                                    </p>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <?php if ($acte_id > 0): ?>
                            <?php foreach ($actes_list as $acte): ?>
                                <?php if ($acte['id'] == $acte_id): ?>
                                    <p class="mb-0">
                                        <i class="fas fa-procedures text-warning me-2"></i>
                                        <strong>Acte:</strong> <?php echo htmlspecialchars($acte['nom']); ?>
                                    </p>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px;">
                <div class="card-header border-0 text-white" style="background: linear-gradient(135deg, #fc4a1a 0%, #f7b733 100%); border-radius: 15px 15px 0 0;">
                    <h6 class="mb-0 fw-bold">
                        <i class="fas fa-chart-line me-2"></i>Évolution Mensuelle
                    </h6>
                </div>
                <div class="card-body p-3">
                    <div style="height: 300px; position: relative;">
                        <canvas id="monthlyChart" style="max-height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rapports d'analyse financière mensuels -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-table"></i> Rapports d'analyse financière mensuels</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Mois</th>
                            <th>Nombre de dossiers</th>
                            <th>Montant total</th>
                            <th>Montant moyen par dossier</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($monthly_data)): ?>
                            <?php foreach ($monthly_data as $month): ?>
                                <tr>
                                    <td><?php echo date('F Y', strtotime($month['mois'] . '-01')); ?></td>
                                    <td><?php echo number_format($month['nb_dossiers'], 0, ',', ' '); ?></td>
                                    <td><?php echo number_format($month['montant_total'], 2, ',', ' '); ?> DH</td>
                                    <td>
                                        <?php 
                                            $avg = $month['nb_dossiers'] > 0 ? $month['montant_total'] / $month['nb_dossiers'] : 0;
                                            echo number_format($avg, 2, ',', ' ') . ' DH';
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-center">Aucune donnée disponible pour la période sélectionnée.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Résumé de l'analyse financière -->
    <div class="row mb-4">
        <!-- Par acte -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-procedures"></i> Analyse par acte</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Acte</th>
                                    <th>Nombre de dossiers</th>
                                    <th>Montant total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($actes_data)): ?>
                                    <?php foreach ($actes_data as $acte): ?>
                                        <?php if ($acte['nb_dossiers'] > 0): ?>
                                            <tr>
                                                <td><?php echo $acte['acte_nom']; ?></td>
                                                <td><?php echo number_format($acte['nb_dossiers'], 0, ',', ' '); ?></td>
                                                <td><?php echo number_format($acte['montant_total'], 2, ',', ' '); ?> DH</td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="3" class="text-center">Aucune donnée disponible.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Par CDM -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-hospital"></i> Analyse par CDM</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>CDM</th>
                                    <th>Nombre de dossiers</th>
                                    <th>Montant total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($cdm_data)): ?>
                                    <?php foreach ($cdm_data as $cdm): ?>
                                        <?php if ($cdm['nb_dossiers'] > 0): ?>
                                            <tr>
                                                <td><?php echo $cdm['cdm_nom']; ?></td>
                                                <td><?php echo number_format($cdm['nb_dossiers'], 0, ',', ' '); ?></td>
                                                <td><?php echo number_format($cdm['montant_total'], 2, ',', ' '); ?> DH</td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="3" class="text-center">Aucune donnée disponible.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques modernes -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuration moderne pour Chart.js
    Chart.defaults.font.family = "'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#6c757d';

    // Graphique d'évolution mensuelle moderne
    var ctx = document.getElementById('monthlyChart');
    if (ctx) {
        var monthlyChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [
                    <?php foreach ($monthly_data as $month): ?>
                        '<?php echo date('M Y', strtotime($month['mois'] . '-01')); ?>',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    label: 'Montant total (DH)',
                    data: [
                        <?php foreach ($monthly_data as $month): ?>
                            <?php echo $month['montant_total']; ?>,
                        <?php endforeach; ?>
                    ],
                    backgroundColor: 'rgba(252, 74, 26, 0.1)',
                    borderColor: '#fc4a1a',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#fc4a1a',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    pointHoverBackgroundColor: '#f7b733',
                    pointHoverBorderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        titleFont: {
                            size: 13,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 12
                        },
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' +
                                       new Intl.NumberFormat('fr-FR', {
                                           style: 'currency',
                                           currency: 'MAD',
                                           minimumFractionDigits: 2
                                       }).format(context.parsed.y).replace('MAD', 'DH');
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                size: 11
                            },
                            callback: function(value) {
                                return new Intl.NumberFormat('fr-FR').format(value) + ' DH';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 11
                            },
                            maxRotation: 45
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    // Impression de la page
    document.getElementById('printPage').addEventListener('click', function() {
        window.print();
    });
});
</script>

<!-- Styles pour l'impression -->
<style media="print">
    @page {
        size: A4;
        margin: 1cm;
    }
    body {
        font-size: 12pt;
    }
    .container-fluid {
        width: 100%;
        padding: 0;
    }
    .card {
        border: 1px solid #ddd;
        margin-bottom: 20px;
        break-inside: avoid;
    }
    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
        font-weight: bold;
        padding: 10px;
    }
    .btn, .form-control, .form-select, #printPage {
        display: none;
    }
    .table {
        width: 100%;
        border-collapse: collapse;
    }
    .table th, .table td {
        border: 1px solid #ddd;
        padding: 8px;
    }
    .table th {
        background-color: #f8f9fa;
    }
    canvas {
        max-width: 100%;
        height: auto !important;
    }
</style>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
