<?php
// Vue pour le tableau de bord d'analyse financière
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-chart-line"></i> Tableau de bord - Analyse financière</h1>
        <div>
            <button id="printPage" class="btn btn-outline-secondary">
                <i class="fas fa-print"></i> Imprimer
            </button>
            <a href="index.php?page=dashboard/finance/export&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>&cdm_id=<?php echo $cdm_id; ?>&acte_id=<?php echo $acte_id; ?>" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Exporter Excel
            </a>
        </div>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-filter"></i> Filtres</h5>
        </div>
        <div class="card-body">
            <form action="index.php" method="get" class="row g-3">
                <input type="hidden" name="page" value="dashboard/finance">
                
                <div class="col-md-3">
                    <label for="date_debut" class="form-label">Date de début</label>
                    <input type="date" class="form-control" id="date_debut" name="date_debut" value="<?php echo $date_debut; ?>">
                </div>
                
                <div class="col-md-3">
                    <label for="date_fin" class="form-label">Date de fin</label>
                    <input type="date" class="form-control" id="date_fin" name="date_fin" value="<?php echo $date_fin; ?>">
                </div>
                
                <div class="col-md-3">
                    <label for="cdm_id" class="form-label">CDM</label>
                    <select class="form-select" id="cdm_id" name="cdm_id">
                        <option value="0">Tous les CDM</option>
                        <?php foreach ($cdm_list as $cdm): ?>
                            <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm_id == $cdm['id'] ? 'selected' : ''; ?>>
                                <?php echo $cdm['nom']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="acte_id" class="form-label">Acte</label>
                    <select class="form-select" id="acte_id" name="acte_id">
                        <option value="0">Tous les actes</option>
                        <?php foreach ($actes_list as $acte): ?>
                            <option value="<?php echo $acte['id']; ?>" <?php echo $acte_id == $acte['id'] ? 'selected' : ''; ?>>
                                <?php echo $acte['nom']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrer
                    </button>
                    <a href="index.php?page=dashboard/finance" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Résumé global -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Résumé global</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Nombre de dossiers</h6>
                                    <h2 class="card-text"><?php echo number_format($total_data['nb_dossiers_total'] ?? 0, 0, ',', ' '); ?></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Montant total</h6>
                                    <h2 class="card-text"><?php echo number_format($total_data['montant_total_global'] ?? 0, 2, ',', ' '); ?> DH</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <p class="mb-0">
                            <strong>Période:</strong> <?php echo date('d/m/Y', strtotime($date_debut)); ?> - <?php echo date('d/m/Y', strtotime($date_fin)); ?>
                        </p>
                        <?php if ($cdm_id > 0): ?>
                            <?php foreach ($cdm_list as $cdm): ?>
                                <?php if ($cdm['id'] == $cdm_id): ?>
                                    <p class="mb-0"><strong>CDM:</strong> <?php echo $cdm['nom']; ?></p>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <?php if ($acte_id > 0): ?>
                            <?php foreach ($actes_list as $acte): ?>
                                <?php if ($acte['id'] == $acte_id): ?>
                                    <p class="mb-0"><strong>Acte:</strong> <?php echo $acte['nom']; ?></p>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Évolution mensuelle</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Rapports d'analyse financière mensuels -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-table"></i> Rapports d'analyse financière mensuels</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Mois</th>
                            <th>Nombre de dossiers</th>
                            <th>Montant total</th>
                            <th>Montant moyen par dossier</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($monthly_data)): ?>
                            <?php foreach ($monthly_data as $month): ?>
                                <tr>
                                    <td><?php echo date('F Y', strtotime($month['mois'] . '-01')); ?></td>
                                    <td><?php echo number_format($month['nb_dossiers'], 0, ',', ' '); ?></td>
                                    <td><?php echo number_format($month['montant_total'], 2, ',', ' '); ?> DH</td>
                                    <td>
                                        <?php 
                                            $avg = $month['nb_dossiers'] > 0 ? $month['montant_total'] / $month['nb_dossiers'] : 0;
                                            echo number_format($avg, 2, ',', ' ') . ' DH';
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-center">Aucune donnée disponible pour la période sélectionnée.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Résumé de l'analyse financière -->
    <div class="row mb-4">
        <!-- Par acte -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-procedures"></i> Analyse par acte</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Acte</th>
                                    <th>Nombre de dossiers</th>
                                    <th>Montant total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($actes_data)): ?>
                                    <?php foreach ($actes_data as $acte): ?>
                                        <?php if ($acte['nb_dossiers'] > 0): ?>
                                            <tr>
                                                <td><?php echo $acte['acte_nom']; ?></td>
                                                <td><?php echo number_format($acte['nb_dossiers'], 0, ',', ' '); ?></td>
                                                <td><?php echo number_format($acte['montant_total'], 2, ',', ' '); ?> DH</td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="3" class="text-center">Aucune donnée disponible.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Par CDM -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-hospital"></i> Analyse par CDM</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>CDM</th>
                                    <th>Nombre de dossiers</th>
                                    <th>Montant total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($cdm_data)): ?>
                                    <?php foreach ($cdm_data as $cdm): ?>
                                        <?php if ($cdm['nb_dossiers'] > 0): ?>
                                            <tr>
                                                <td><?php echo $cdm['cdm_nom']; ?></td>
                                                <td><?php echo number_format($cdm['nb_dossiers'], 0, ',', ' '); ?></td>
                                                <td><?php echo number_format($cdm['montant_total'], 2, ',', ' '); ?> DH</td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="3" class="text-center">Aucune donnée disponible.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Graphique d'évolution mensuelle
    var ctx = document.getElementById('monthlyChart').getContext('2d');
    var monthlyChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: [
                <?php foreach ($monthly_data as $month): ?>
                    '<?php echo date('M Y', strtotime($month['mois'] . '-01')); ?>',
                <?php endforeach; ?>
            ],
            datasets: [{
                label: 'Montant total (DH)',
                data: [
                    <?php foreach ($monthly_data as $month): ?>
                        <?php echo $month['montant_total']; ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Impression de la page
    document.getElementById('printPage').addEventListener('click', function() {
        window.print();
    });
});
</script>

<!-- Styles pour l'impression -->
<style media="print">
    @page {
        size: A4;
        margin: 1cm;
    }
    body {
        font-size: 12pt;
    }
    .container-fluid {
        width: 100%;
        padding: 0;
    }
    .card {
        border: 1px solid #ddd;
        margin-bottom: 20px;
        break-inside: avoid;
    }
    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
        font-weight: bold;
        padding: 10px;
    }
    .btn, .form-control, .form-select, #printPage {
        display: none;
    }
    .table {
        width: 100%;
        border-collapse: collapse;
    }
    .table th, .table td {
        border: 1px solid #ddd;
        padding: 8px;
    }
    .table th {
        background-color: #f8f9fa;
    }
    canvas {
        max-width: 100%;
        height: auto !important;
    }
</style>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
