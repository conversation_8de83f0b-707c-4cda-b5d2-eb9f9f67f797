<?php
// Contrôleur pour la page de connexion

// Titre de la page
$page_title = 'Connexion';

// Rediriger si déjà connecté
if (isAuthenticated()) {
    redirect('index.php?page=dashboard');
}

// Traitement du formulaire de connexion
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=login', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }
    
    // Récupérer et nettoyer les données du formulaire
    $username = clean($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']) && $_POST['remember'] === '1';
    
    // Valider les données
    $errors = [];
    
    if (empty($username)) {
        $errors[] = 'Le nom d\'utilisateur est requis.';
    }
    
    if (empty($password)) {
        $errors[] = 'Le mot de passe est requis.';
    }
    
    // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = ['username' => $username, 'remember' => $remember];
        redirect('index.php?page=login', 'Veuillez corriger les erreurs.', 'danger');
    }
    
    try {
        // Vérifier les identifiants
        $db = Database::getInstance();
        $sql = "SELECT id, username, password, role, active FROM users WHERE username = ?";
        $user = $db->single($sql, [$username]);
        
        if (!$user || !password_verify($password, $user['password'])) {
            redirect('index.php?page=login', 'Identifiants incorrects.', 'danger');
        }
        
        if (!$user['active']) {
            redirect('index.php?page=login', 'Ce compte est désactivé. Veuillez contacter l\'administrateur.', 'danger');
        }
        
        // Mettre à jour la date de dernière connexion
        $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
        $db->query($sql, [$user['id']]);
        
        // Créer la session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['user_role'] = $user['role'];
        
        // Créer un cookie "Se souvenir de moi" si demandé
        if ($remember) {
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', strtotime('+30 days'));
            
            $sql = "INSERT INTO user_tokens (user_id, token, expires_at, created_at) VALUES (?, ?, ?, NOW())";
            $db->query($sql, [$user['id'], $token, $expires]);
            
            setcookie('remember_token', $token, strtotime('+30 days'), '/', '', false, true);
        }
        
        // Journaliser la connexion
        logActivity($user['id'], 'Connexion', 'Connexion réussie');
        
        // Rediriger vers le tableau de bord
        redirect('index.php?page=dashboard', 'Connexion réussie. Bienvenue, ' . $user['username'] . '!', 'success');
        
    } catch (Exception $e) {
        redirect('index.php?page=login', 'Erreur lors de la connexion: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'username' => '',
    'remember' => false
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Charger la vue
require_once VIEWS_PATH . 'login.php';
?>
