<?php
// Vue pour la suppression d'un CDM
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-hospital"></i> Supprimer un CDM</h1>
        <div>
            <a href="index.php?page=admin/cdm" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Confirmation de suppression</h5>
        </div>
        <div class="card-body">
            <?php if ($is_used): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-circle"></i> Ce CDM est utilisé dans des lots. Il ne peut pas être supprimé, mais il sera désactivé.
                </div>
            <?php endif; ?>
            
            <p>Êtes-vous sûr de vouloir <?php echo $is_used ? 'désactiver' : 'supprimer'; ?> le CDM suivant ?</p>
            
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title"><?php echo $cdm['nom']; ?></h5>
                    <p class="card-text">
                        <strong>Adresse :</strong> <?php echo $cdm['adresse']; ?><br>
                        <strong>Contact :</strong> <?php echo $cdm['contact_nom']; ?><br>
                        <strong>Téléphone :</strong> <?php echo $cdm['telephone']; ?><br>
                        <strong>Email :</strong> <?php echo $cdm['email']; ?><br>
                        <strong>Statut :</strong> <?php echo $cdm['active'] ? 'Actif' : 'Inactif'; ?>
                    </p>
                </div>
            </div>
            
            <form method="post" action="index.php?page=admin/cdm/delete&id=<?php echo $cdm['id']; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="index.php?page=admin/cdm" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> <?php echo $is_used ? 'Désactiver' : 'Supprimer'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
