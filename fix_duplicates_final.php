<?php
/**
 * إصلاح التكرارات النهائي - حل المشكلة نهائياً
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // تسجيل دخول تلقائي
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🔧 إصلاح التكرارات النهائي</h2>";
    echo "<p>✅ تم تسجيل الدخول كـ: " . $_SESSION['username'] . "</p>";
    
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>🔍 1. فحص شامل للتكرارات</h3>";
    
    // فحص جميع الدوسيهات في قاعدة البيانات
    $sql = "SELECT id, numero_dossier, numero_adherent, nom, created_at
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            ORDER BY numero_dossier, numero_adherent, id ASC";
    $all_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• إجمالي الدوسيهات في قاعدة البيانات: " . count($all_dossiers) . "<br>";
    
    echo "<h4>📋 جميع الدوسيهات:</h4>";
    $duplicates_map = [];
    foreach ($all_dossiers as $index => $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (!isset($duplicates_map[$key])) {
            $duplicates_map[$key] = [];
        }
        $duplicates_map[$key][] = $dossier;
        
        $is_duplicate = count($duplicates_map[$key]) > 1;
        $color = $is_duplicate ? 'color: red; font-weight: bold;' : 'color: green;';
        
        echo "<span style='$color'>" . ($index + 1) . ". ID: " . $dossier['id'] . " - " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - " . $dossier['nom'] . " (" . $dossier['created_at'] . ")</span><br>";
    }
    
    echo "<h3>🗑️ 2. حذف التكرارات</h3>";
    
    $deleted_count = 0;
    foreach ($duplicates_map as $key => $dossiers_group) {
        if (count($dossiers_group) > 1) {
            list($numero_dossier, $numero_adherent) = explode('_', $key);
            echo "<h4>🔧 معالجة التكرار: $numero_dossier - $numero_adherent</h4>";
            
            // ترتيب حسب ID (الأقدم أولاً)
            usort($dossiers_group, function($a, $b) {
                return $a['id'] - $b['id'];
            });
            
            // الاحتفاظ بالأول وحذف الباقي
            $keep_dossier = $dossiers_group[0];
            echo "• الاحتفاظ بـ: ID " . $keep_dossier['id'] . " (الأقدم)<br>";
            
            for ($i = 1; $i < count($dossiers_group); $i++) {
                $delete_dossier = $dossiers_group[$i];
                $delete_id = $delete_dossier['id'];
                
                // حذف الأسباب المرتبطة أولاً
                $sql = "DELETE FROM dossier_raisons WHERE dossier_id = ?";
                $db->query($sql, [$delete_id]);
                
                // حذف الدوسيه
                $sql = "DELETE FROM dossiers_retournes WHERE id = ?";
                $db->query($sql, [$delete_id]);
                
                echo "• تم حذف: ID $delete_id<br>";
                $deleted_count++;
            }
        }
    }
    
    echo "• <strong>إجمالي الدوسيهات المحذوفة:</strong> $deleted_count<br>";
    
    echo "<h3>✅ 3. التحقق النهائي</h3>";
    
    // إعادة فحص البيانات
    $sql = "SELECT id, numero_dossier, numero_adherent, nom
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            ORDER BY id ASC";
    $final_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• عدد الدوسيهات بعد التنظيف: " . count($final_dossiers) . "<br>";
    
    echo "<h4>📋 الدوسيهات النهائية:</h4>";
    foreach ($final_dossiers as $index => $dossier) {
        echo ($index + 1) . ". ID: " . $dossier['id'] . " - " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - " . $dossier['nom'] . "<br>";
    }
    
    // فحص التكرارات مرة أخرى
    $sql = "SELECT numero_dossier, numero_adherent, COUNT(*) as count
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            GROUP BY numero_dossier, numero_adherent
            HAVING COUNT(*) > 1";
    $remaining_duplicates = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    if (empty($remaining_duplicates)) {
        echo "✅ <strong>لا توجد تكرارات متبقية</strong><br>";
    } else {
        echo "❌ <strong>تكرارات متبقية:</strong> " . count($remaining_duplicates) . "<br>";
        foreach ($remaining_duplicates as $dup) {
            echo "• " . $dup['numero_dossier'] . " - " . $dup['numero_adherent'] . " (x" . $dup['count'] . ")<br>";
        }
    }
    
    echo "<h3>🧪 4. اختبار صفحة الطباعة</h3>";
    
    // تنفيذ نفس الكود الموجود في print.php
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $print_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• دوسيهات للطباعة: " . count($print_dossiers) . "<br>";
    
    // إضافة الأسباب
    foreach ($print_dossiers as &$dossier) {
        $sql = "SELECT dr.*, r.raison as raison
                FROM dossier_raisons dr
                JOIN raisons_retour r ON dr.raison_id = r.id
                WHERE dr.dossier_id = ?
                ORDER BY dr.created_at ASC";
        $raisons = $db->all($sql, [$dossier['id']]);
        
        if (empty($raisons) && !empty($dossier['raison_retour_nom'])) {
            $dossier['raisons'] = [['raison' => $dossier['raison_retour_nom']]];
        } else {
            $dossier['raisons'] = $raisons;
        }
        
        $raisons_str = [];
        foreach ($dossier['raisons'] as $raison) {
            $raisons_str[] = $raison['raison'];
        }
        $dossier['raisons_str'] = implode(', ', $raisons_str);
    }
    
    echo "<h4>📋 نتيجة اختبار الطباعة:</h4>";
    $print_unique = [];
    $print_has_duplicates = false;
    
    foreach ($print_dossiers as $index => $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($print_unique[$key])) {
            $print_has_duplicates = true;
            echo "<span style='color: red; font-weight: bold;'>❌ " . ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " (تكرار)</span><br>";
        } else {
            $print_unique[$key] = true;
            echo "<span style='color: green;'>✅ " . ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . "</span><br>";
        }
    }
    
    if (!$print_has_duplicates) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>🎉 نجح الإصلاح!</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ تم حذف " . $deleted_count . " دوسيه مكرر</li>";
        echo "<li>✅ لا توجد تكرارات في قاعدة البيانات</li>";
        echo "<li>✅ صفحة الطباعة ستعمل بشكل صحيح</li>";
        echo "<li>✅ عدد الدوسيهات النهائي: " . count($final_dossiers) . "</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24; margin-top: 0;'>❌ لا تزال هناك مشكلة</h4>";
        echo "<p style='color: #721c24;'>توجد تكرارات في نتيجة الطباعة رغم تنظيف قاعدة البيانات.</p>";
        echo "</div>";
    }
    
    echo "<h3>🔗 اختبار النتائج:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank' style='color: #28a745; font-weight: bold;'>🖨️ صفحة الطباعة</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank' style='color: #17a2b8; font-weight: bold;'>📝 صفحة التحرير</a></li>";
    echo "<li><a href='index.php?page=retours/export&numero_bordereau=10001&cdm_id=1' target='_blank' style='color: #ffc107; font-weight: bold;'>📊 تصدير Excel</a></li>";
    echo "<li><a href='direct_print_test.php' target='_blank' style='color: #6f42c1; font-weight: bold;'>🔍 اختبار مباشر</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>التتبع: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح التكرارات النهائي - LOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
