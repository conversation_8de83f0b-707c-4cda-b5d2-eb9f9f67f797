<?php
// Contrôleur pour l'édition d'un bordereau de dossiers retournés

// Vérifier si les paramètres du bordereau sont fournis
if (!isset($_GET['numero']) || empty($_GET['numero']) || !isset($_GET['cdm_id']) || empty($_GET['cdm_id'])) {
    redirect('index.php?page=retours', 'Paramètres du bordereau non spécifiés.', 'danger');
}

$numero_bordereau = intval($_GET['numero']);
$cdm_id = intval($_GET['cdm_id']);
$is_new = isset($_GET['new']) && $_GET['new'] === '1';
$date_bordereau = isset($_GET['date_bordereau']) ? clean($_GET['date_bordereau']) : '';
$notes = isset($_GET['notes']) ? clean($_GET['notes']) : '';

// Titre de la page
$page_title = 'Édition du bordereau #' . $numero_bordereau;
$page = 'retours/edit';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Vérifier si la table bordereaux_retour existe
$sql = "SHOW TABLES LIKE 'bordereaux_retour'";
$result = $db->query($sql);
$table_exists = $result->rowCount() > 0;

if (!$table_exists) {
    // Créer la table bordereaux_retour
    $sql = "CREATE TABLE bordereaux_retour (
        id INT AUTO_INCREMENT PRIMARY KEY,
        numero INT NOT NULL,
        cdm_id INT NOT NULL,
        date_bordereau DATE NOT NULL,
        notes TEXT NULL,
        created_by INT NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NULL,
        UNIQUE KEY unique_bordereau (numero, cdm_id),
        FOREIGN KEY (cdm_id) REFERENCES cdm(id),
        FOREIGN KEY (created_by) REFERENCES users(id)
    )";

    $db->query($sql);

    // Migrer les données existantes
    $sql = "INSERT IGNORE INTO bordereaux_retour (numero, cdm_id, date_bordereau, created_by, created_at)
            SELECT DISTINCT numero_bordereau, cdm_id, date_bordereau, created_by, created_at
            FROM dossiers_retournes";

    $db->query($sql);
}

// Si c'est un nouveau bordereau, l'ajouter à la table bordereaux_retour
if ($is_new) {
    // Vérifier si le bordereau existe déjà
    $sql = "SELECT COUNT(*) as count FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
    $result = $db->single($sql, [$numero_bordereau, $cdm_id]);

    if ($result['count'] === 0) {
        // Insérer le nouveau bordereau
        $sql = "INSERT INTO bordereaux_retour (numero, cdm_id, date_bordereau, notes, created_by, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())";
        $params = [$numero_bordereau, $cdm_id, $date_bordereau, $notes, $_SESSION['user_id']];
        $db->query($sql, $params);

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Création d\'un bordereau de retour', "Bordereau #$numero_bordereau pour CDM #$cdm_id");
    }
} else {
    // Vérifier si le bordereau existe
    $sql = "SELECT COUNT(*) as count FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
    $result = $db->single($sql, [$numero_bordereau, $cdm_id]);

    if ($result['count'] === 0) {
        // Vérifier dans la table dossiers_retournes (pour la rétrocompatibilité)
        $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
        $result = $db->single($sql, [$numero_bordereau, $cdm_id]);

        if ($result['count'] === 0) {
            redirect('index.php?page=retours', 'Bordereau non trouvé.', 'danger');
        } else {
            // Récupérer la date du bordereau et créer l'entrée dans bordereaux_retour
            $sql = "SELECT date_bordereau, created_by, created_at FROM dossiers_retournes
                    WHERE numero_bordereau = ? AND cdm_id = ? LIMIT 1";
            $result = $db->single($sql, [$numero_bordereau, $cdm_id]);
            $date_bordereau = $result['date_bordereau'];

            // Insérer dans bordereaux_retour
            $sql = "INSERT INTO bordereaux_retour (numero, cdm_id, date_bordereau, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?)";
            $params = [$numero_bordereau, $cdm_id, $date_bordereau, $result['created_by'], $result['created_at']];
            $db->query($sql, $params);
        }
    } else {
        // Récupérer la date du bordereau et les notes
        $sql = "SELECT date_bordereau, notes FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
        $result = $db->single($sql, [$numero_bordereau, $cdm_id]);
        $date_bordereau = $result['date_bordereau'];
        $notes = $result['notes'] ?? '';
    }
}

// Récupérer les informations du CDM
$sql = "SELECT * FROM cdm WHERE id = ?";
$cdm = $db->single($sql, [$cdm_id]);

if (!$cdm) {
    redirect('index.php?page=retours', 'CDM non trouvé.', 'danger');
}

// Récupérer les dossiers associés à ce bordereau
$sql = "SELECT dr.*, a.nom as acte_nom, r.raison as raison_retour_nom
        FROM dossiers_retournes dr
        LEFT JOIN actes a ON dr.acte_id = a.id
        LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
        WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
        ORDER BY dr.created_at ASC";
$dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);

// Récupérer toutes les raisons de retour pour chaque dossier
foreach ($dossiers as &$dossier) {
    $sql = "SELECT dr.*, r.raison
            FROM dossier_raisons dr
            JOIN raisons_retour r ON dr.raison_id = r.id
            WHERE dr.dossier_id = ?
            ORDER BY dr.created_at ASC";
    $raisons = $db->all($sql, [$dossier['id']]);

    // Si aucune raison n'est trouvée dans la nouvelle table, utiliser la raison principale
    if (empty($raisons) && !empty($dossier['raison_retour_nom'])) {
        $dossier['raisons'] = [['raison' => $dossier['raison_retour_nom']]];
    } else {
        $dossier['raisons'] = $raisons;
    }

    // Créer une chaîne de toutes les raisons pour l'affichage
    $raisons_str = [];
    foreach ($dossier['raisons'] as $raison) {
        $raisons_str[] = $raison['raison'];
    }
    $dossier['raisons_str'] = implode(', ', $raisons_str);
}

// Obtenir la liste des actes et des raisons de retour pour le formulaire
$actes_list = getActesList();
$raisons_list = getRaisonsRetourList();

// Traitement du formulaire d'ajout de dossier
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_dossier') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=retours/edit&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupérer et nettoyer les données du formulaire
    $numero_dossier = clean($_POST['numero_dossier'] ?? '');
    $numero_adherent = clean($_POST['numero_adherent'] ?? '');
    $nom = clean($_POST['nom'] ?? '');
    $beneficiaire = clean($_POST['beneficiaire'] ?? '');

    // Gestion des actes multiples
    $acte_ids = isset($_POST['acte_ids']) ? clean($_POST['acte_ids']) : '';
    // Utiliser le premier acte comme acte principal pour la rétrocompatibilité
    $acte_id = !empty($acte_ids) ? intval(explode(',', $acte_ids)[0]) : 0;

    $montant = isset($_POST['montant']) ? floatval(str_replace(',', '.', $_POST['montant'])) : 0;
    $raisons_retour = isset($_POST['raisons_retour']) && is_array($_POST['raisons_retour']) ? $_POST['raisons_retour'] : [];
    // Utiliser la première raison comme raison principale pour la rétrocompatibilité
    $raison_retour_id = !empty($raisons_retour) ? intval($raisons_retour[0]) : 0;
    $date_retour = isset($_POST['date_retour']) ? clean($_POST['date_retour']) : '';
    $date_correction = isset($_POST['date_correction']) ? clean($_POST['date_correction']) : '';
    $corrige = isset($_POST['corrige']) && $_POST['corrige'] === '1' ? 1 : 0;
    $notes = clean($_POST['notes'] ?? '');

    // Valider les données
    $errors = [];

    if (empty($numero_dossier)) {
        $errors[] = 'Le numéro de dossier est requis.';
    }

    if (empty($numero_adherent)) {
        $errors[] = 'Le numéro d\'adhérent est requis.';
    }

    if (empty($nom)) {
        $errors[] = 'Le nom est requis.';
    }

    if (empty($raisons_retour)) {
        $errors[] = 'Au moins une raison du retour est requise.';
    }

    if (empty($date_retour)) {
        $errors[] = 'La date de retour est requise.';
    } else {
        // Convertir la date au format MySQL (YYYY-MM-DD)
        $date_retour = date('Y-m-d', strtotime(str_replace('/', '-', $date_retour)));
    }

    if (!empty($date_correction)) {
        // Convertir la date au format MySQL (YYYY-MM-DD)
        $date_correction = date('Y-m-d', strtotime(str_replace('/', '-', $date_correction)));
    } else {
        $date_correction = null;
    }

    // S'il y a des erreurs, rediriger vers la page avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = $_POST;
        redirect('index.php?page=retours/edit&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Veuillez corriger les erreurs.', 'danger');
    }

    try {
        // Insérer le dossier dans la base de données
        $sql = "INSERT INTO dossiers_retournes (numero_bordereau, cdm_id, date_bordereau, numero_dossier,
                                              numero_adherent, nom, beneficiaire, acte_id, montant,
                                              raison_retour_id, date_retour, date_correction, corrige,
                                              notes, created_by, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        $params = [$numero_bordereau, $cdm_id, $date_bordereau, $numero_dossier, $numero_adherent,
                  $nom, $beneficiaire, $acte_id ?: null, $montant, $raison_retour_id,
                  $date_retour, $date_correction, $corrige, $notes, $_SESSION['user_id']];
        $db->query($sql, $params);

        // Récupérer l'ID du dossier inséré
        $dossier_id = $db->lastInsertId();

        // Insérer les raisons de retour multiples
        foreach ($raisons_retour as $raison_id) {
            $sql = "INSERT INTO dossier_raisons (dossier_id, raison_id) VALUES (?, ?)";
            $db->query($sql, [$dossier_id, intval($raison_id)]);
        }

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Ajout de dossier retourné', "Dossier #$numero_dossier ajouté au bordereau #$numero_bordereau");

        // Rediriger vers la page d'édition du bordereau
        redirect('index.php?page=retours/edit&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Le dossier a été ajouté avec succès.', 'success');

    } catch (Exception $e) {
        redirect('index.php?page=retours/edit&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Erreur lors de l\'ajout du dossier: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'numero_dossier' => '',
    'numero_adherent' => '',
    'nom' => '',
    'beneficiaire' => '',
    'acte_id' => '',
    'acte_ids' => '',
    'montant' => '',
    'raisons_retour' => [],
    'date_retour' => date('d/m/Y'),
    'date_correction' => '',
    'corrige' => '0',
    'notes' => ''
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Charger la vue
require_once VIEWS_PATH . 'retours/edit.php';
?>
