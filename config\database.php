<?php
// Gestion de la connexion à la base de données

class Database {
    private $host;
    private $user;
    private $pass;
    private $dbname;
    private $conn;
    private static $instance = null;

    private function __construct() {
        // Utiliser les constantes de configuration ou les valeurs par défaut
        $this->host = defined('DB_HOST') ? DB_HOST : 'localhost';
        $this->user = defined('DB_USER') ? DB_USER : 'root';
        $this->pass = defined('DB_PASS') ? DB_PASS : '';
        $this->dbname = defined('DB_NAME') ? DB_NAME : 'lot2';
        
        $this->connect();
    }

    // Pattern Singleton pour une seule instance de connexion
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new Database();
        }
        return self::$instance;
    }

    // Établir la connexion à la base de données
    private function connect() {
        try {
            $this->conn = new PDO(
                "mysql:host={$this->host};dbname={$this->dbname};charset=utf8mb4",
                $this->user,
                $this->pass,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            // Si la base de données n'existe pas, on redirige vers l'installation
            if ($e->getCode() == 1049) { // Code MySQL pour "Unknown database"
                if (basename($_SERVER['PHP_SELF']) !== 'index.php' || 
                    (isset($_GET['page']) && $_GET['page'] !== 'install')) {
                    header('Location: index.php?page=install');
                    exit;
                }
            } else {
                // Autre erreur de connexion
                if (DEBUG_MODE) {
                    die("Erreur de connexion à la base de données: " . $e->getMessage());
                } else {
                    die("Erreur de connexion à la base de données. Veuillez contacter l'administrateur.");
                }
            }
        }
    }

    // Obtenir la connexion
    public function getConnection() {
        return $this->conn;
    }

    // Exécuter une requête
    public function query($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                die("Erreur d'exécution de la requête: " . $e->getMessage());
            } else {
                die("Une erreur est survenue lors de l'exécution de la requête.");
            }
        }
    }

    // Obtenir un seul enregistrement
    public function single($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    // Obtenir tous les enregistrements
    public function all($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    // Obtenir le dernier ID inséré
    public function lastInsertId() {
        return $this->conn->lastInsertId();
    }

    // Vérifier si la base de données existe
    public function databaseExists() {
        try {
            $this->conn->query("SELECT 1 FROM `users` LIMIT 1");
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }

    // Mettre à jour les paramètres de connexion
    public function updateConnectionParams($host, $user, $pass, $dbname) {
        $this->host = $host;
        $this->user = $user;
        $this->pass = $pass;
        $this->dbname = $dbname;
        
        // Fermer la connexion existante si elle existe
        $this->conn = null;
        
        // Établir une nouvelle connexion
        $this->connect();
    }
}
?>
