<?php
// Contrôleur pour l'exportation des lots vers Excel

// Inclure la classe ExcelExporter
require_once __DIR__ . '/../../includes/ExcelExporter.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de filtrage
$cdm_id = isset($_GET['cdm_id']) ? intval($_GET['cdm_id']) : 0;
$date_debut = isset($_GET['date_debut']) ? $_GET['date_debut'] : '';
$date_fin = isset($_GET['date_fin']) ? $_GET['date_fin'] : '';
$statut = isset($_GET['statut']) ? $_GET['statut'] : '';

// Construction de la requête SQL
$sql = "SELECT l.*, c.nom as cdm_nom, u.username as created_by_username
        FROM lots l
        JOIN cdm c ON l.cdm_id = c.id
        JOIN users u ON l.created_by = u.id
        WHERE 1=1";
$params = [];

// Appliquer les filtres
if ($cdm_id > 0) {
    $sql .= " AND l.cdm_id = ?";
    $params[] = $cdm_id;
}

if (!empty($date_debut)) {
    $sql .= " AND l.date_reception >= ?";
    $params[] = $date_debut;
}

if (!empty($date_fin)) {
    $sql .= " AND l.date_reception <= ?";
    $params[] = $date_fin;
}

if ($statut !== '') {
    $sql .= " AND l.statut = ?";
    $params[] = $statut;
}

$sql .= " ORDER BY l.date_reception DESC, l.numero DESC";

// Exécuter la requête
$lots = $db->all($sql, $params);

// Préparer les données pour l'exportation
$headers = [
    'Numéro',
    'CDM',
    'Date de réception',
    'Nombre de dossiers',
    'Statut',
    'Date de traitement',
    'Créé par',
    'Date de création'
];

$data = [];
foreach ($lots as $lot) {
    $statut_text = '';
    switch ($lot['statut']) {
        case 'recu':
            $statut_text = 'Reçu';
            break;
        case 'en_cours':
            $statut_text = 'En cours';
            break;
        case 'traite':
            $statut_text = 'Traité';
            break;
        case 'archive':
            $statut_text = 'Archivé';
            break;
    }
    
    $data[] = [
        $lot['numero'],
        $lot['cdm_nom'],
        formatDate($lot['date_reception']),
        $lot['nombre_dossiers'],
        $statut_text,
        formatDate($lot['date_traitement']),
        $lot['created_by_username'],
        formatDate($lot['created_at'])
    ];
}

// Nom du fichier
$filename = 'Liste_Lots_' . date('Ymd');

// Format d'exportation (csv par défaut)
$format = isset($_GET['format']) && $_GET['format'] === 'xlsx' ? 'xlsx' : 'csv';

// Journaliser l'action
logActivity($_SESSION['user_id'], 'Exportation de la liste des lots', "Format: $format");

// Exporter les données
ExcelExporter::export($data, $headers, $filename, $format);
?>
