<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        body {
            font-family: 'Arial', sans-serif;
            font-size: 9pt;
            line-height: 1.1;
        }

        .container {
            padding: 10px;
        }

        .header-title {
            font-size: 14pt;
            font-weight: 500;
            margin-bottom: 3px;
            text-align: center;
        }

        .subtitle {
            font-size: 12pt;
            font-weight: 400;
            margin-bottom: 3px;
            text-align: center;
        }

        .print-date {
            font-size: 8pt;
            text-align: right;
            margin-bottom: 10px;
        }

        .section-title {
            font-size: 10pt;
            font-weight: 500;
            margin-bottom: 3px;
            padding: 2px 3px;
            background-color: #f0f0f0;
            border-bottom: 1px solid #ddd;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }

        .info-table th, .info-table td {
            padding: 2px 3px;
            border: 1px solid #ddd;
            font-size: 8pt;
            line-height: 1;
        }

        .info-table th {
            background-color: #f8f9fa;
            width: 30%;
            font-weight: 500;
        }

        .dossiers-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            table-layout: fixed;
            page-break-inside: auto;
        }

        /* Assurer que les en-têtes se répètent sur chaque page */
        .dossiers-table thead {
            display: table-header-group;
        }

        .dossiers-table tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        .dossiers-table th, .dossiers-table td {
            padding: 1px 2px; /* Réduit davantage l'espacement */
            border: 1px solid #ddd;
            font-size: 8pt; /* Réduit la taille de la police */
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1; /* Réduit davantage la hauteur des lignes */
            white-space: nowrap; /* Empêche le retour à la ligne */
            max-height: 12px; /* Limite la hauteur des cellules */
        }

        .dossiers-table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }

        .dossiers-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        /* Largeurs des colonnes */
        .dossiers-table th:nth-child(1), .dossiers-table td:nth-child(1) { /* # */
            width: 3%;
        }
        .dossiers-table th:nth-child(2), .dossiers-table td:nth-child(2) { /* N° Dossier */
            width: 10%;
        }
        .dossiers-table th:nth-child(3), .dossiers-table td:nth-child(3) { /* N° Adhérent */
            width: 10%;
        }
        .dossiers-table th:nth-child(4), .dossiers-table td:nth-child(4) { /* Nom - Augmenté de 20% */
            width: 38%;
        }
        .dossiers-table th:nth-child(5), .dossiers-table td:nth-child(5) { /* Bénéficiaire - Réduit de 20% */
            width: 9%;
        }
        .dossiers-table th:nth-child(6), .dossiers-table td:nth-child(6) { /* Acte */
            width: 15%;
        }
        .dossiers-table th:nth-child(7), .dossiers-table td:nth-child(7) { /* Montant */
            width: 10%;
        }
        .dossiers-table th:nth-child(8), .dossiers-table td:nth-child(8) { /* N° Bon */
            width: 12%;
        }
        .dossiers-table th:nth-child(9), .dossiers-table td:nth-child(9) { /* Statut */
            width: 10%;
        }

        .footer {
            font-size: 8pt;
            text-align: center;
            margin-top: 10px;
            border-top: 1px solid #ddd;
            padding-top: 5px;
        }

        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            margin-bottom: 5px;
        }

        .signature-box {
            width: 45%;
        }

        .signature-line {
            border-top: 1px solid #ddd;
            margin-top: 20px;
            padding-top: 3px;
            font-size: 8pt;
        }

        @media print {
            body {
                font-size: 8pt;
                line-height: 1;
            }

            .no-print {
                display: none !important;
            }

            .container {
                width: 100%;
                max-width: 100%;
                padding: 5px;
                margin: 0;
            }

            .page-break {
                page-break-after: always;
            }

            /* Réduire les marges d'impression */
            @page {
                margin: 0.3cm;
                size: A4 portrait;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Boutons d'action (non imprimables) -->
        <div class="d-flex justify-content-between align-items-center mb-4 no-print">
            <h1><i class="fas fa-print"></i> Impression - Lot #<?php echo $lot['numero_lot']; ?></h1>
            <div>
                <button onclick="window.print();" class="btn btn-primary">
                    <i class="fas fa-print"></i> Imprimer
                </button>
                <a href="index.php?page=lots/view&id=<?php echo $lot_id; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour
                </a>
            </div>
        </div>

        <!-- En-tête du document -->
        <div class="mb-2 position-relative">
            <!-- Logo MGEN à gauche -->
            <div style="position: absolute; top: 0; left: 0; width: 50px; height: 50px;">
                <img src="MGEN.jpg" alt="MGEN Logo" style="width: 50px; height: 50px;">
            </div>

            <div class="text-center">
                <div class="header-title">Mutuelle Générale de l'Education National</div>
                <div class="subtitle" style="text-align: left; margin-left: 60px;">Gestion des Lots CDM</div>
                <div class="subtitle" style="text-align: left; margin-left: 60px;">Récapitulatif du Lot #<?php echo $lot['numero_lot']; ?></div>
                <div class="print-date">Imprimé le <?php echo date(DATETIME_FORMAT); ?></div>
            </div>
        </div>

        <!-- Informations du lot -->
        <div class="mb-3">
            <div class="section-title"><i class="fas fa-info-circle"></i> Informations du lot</div>
            <table class="info-table">
                <tbody>
                    <tr>
                        <th>Numéro de lot</th>
                        <td><?php echo $lot['numero_lot']; ?></td>
                        <th>Statut</th>
                        <td>
                            <?php
                                echo $lot['statut'] === 'en_attente' ? 'En attente' :
                                    ($lot['statut'] === 'recu' ? 'Reçu' :
                                    ($lot['statut'] === 'traite' ? 'Traité' : 'Archivé'));
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <th>CDM</th>
                        <td><?php echo $lot['cdm_nom']; ?></td>
                        <th>Total dossiers</th>
                        <td><?php echo $total_dossiers; ?></td>
                    </tr>
                    <tr>
                        <th>Dossiers reçus</th>
                        <td><?php echo $dossiers_recus; ?></td>
                        <th>Montant total</th>
                        <td><?php echo number_format($montant_total, 2, ',', ' '); ?> DH</td>
                    </tr>
                    <tr>
                        <th>Date de demande</th>
                        <td><?php echo formatDate($lot['date_demande']); ?></td>
                        <th>Dossiers en attente</th>
                        <td><?php echo $dossiers_en_attente; ?></td>
                    </tr>
                    <tr>
                        <th>Date de réception</th>
                        <td><?php echo formatDate($lot['date_reception']); ?></td>
                        <th></th>
                        <td></td>
                    </tr>
                </tbody>
            </table>

            <?php if (!empty($lot['notes'])): ?>
                <div style="border: 1px solid #ddd; padding: 3px; margin-top: 3px; font-size: 8pt;">
                    <strong>Notes:</strong> <?php echo nl2br($lot['notes']); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Liste des dossiers -->
        <div>
            <div class="section-title"><i class="fas fa-folder"></i> Liste des dossiers (<?php echo count($dossiers); ?>)</div>
            <?php if (count($dossiers) > 0): ?>
                <table class="dossiers-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>N° Dossier</th>
                            <th>N° Adhérent</th>
                            <th>Nom</th>
                            <th>Bénéficiaire</th>
                            <th>Acte</th>
                            <th>Montant</th>
                            <th>N° Bon</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($dossiers as $index => $dossier): ?>
                            <tr>
                                <td><?php echo $index + 1; ?></td>
                                <td><?php echo $dossier['numero_dossier']; ?></td>
                                <td><?php echo $dossier['numero_adherent']; ?></td>
                                <td><?php echo $dossier['nom']; ?></td>
                                <td><?php echo $dossier['beneficiaire']; ?></td>
                                <td><?php echo $dossier['acte_nom']; ?></td>
                                <td><?php echo !empty($dossier['montant']) ? number_format($dossier['montant'], 2, ',', ' ') . ' DH' : ''; ?></td>
                                <td><?php echo $dossier['numero_bon']; ?></td>
                                <td>
                                    <?php echo $dossier['recu'] ? 'Reçu' : 'Non reçu'; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div style="border: 1px solid #ddd; padding: 5px; margin-top: 5px; font-size: 9pt;">
                    <i class="fas fa-info-circle"></i> Aucun dossier n'a été ajouté à ce lot.
                </div>
            <?php endif; ?>
        </div>

        <!-- Section signature et date de réception -->
        <div class="signature-section">
            <div class="signature-box">
                <p>Date de réception ____/____/________</p>
                <div class="signature-line">Cachet et signature</div>
            </div>
            <div class="signature-box">
                <!-- Espace vide pour équilibrer la mise en page -->
            </div>
        </div>

        <!-- Pied de page -->
        <div class="footer">
            <p>Page 1/1</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Auto-print -->
    <script>
        // Imprimer automatiquement après 1 seconde
        setTimeout(function() {
            window.print();
        }, 1000);
    </script>
</body>
</html>
