<?php
/**
 * إصلاح التكرار النهائي - حذف الدوسيه المكرر وإنشاء الدوسيه الخامس الصحيح
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // Créer la session admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🔧 إصلاح التكرار النهائي</h2>";
    echo "<p>✅ Session admin créée</p>";
    
    // Paramètres du test
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>🔍 Étape 1: تحديد التكرار</h3>";
    
    // البحث عن الدوسيهات المكررة
    $sql = "SELECT id, numero_dossier, numero_adherent, nom, created_at
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            ORDER BY id ASC";
    $dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "<h4>📋 الدوسيهات الحالية:</h4>";
    foreach ($dossiers as $index => $dossier) {
        echo ($index + 1) . ". ID: " . $dossier['id'] . " - " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - " . $dossier['nom'] . "<br>";
    }
    
    // البحث عن التكرارات
    $duplicates = [];
    for ($i = 0; $i < count($dossiers); $i++) {
        for ($j = $i + 1; $j < count($dossiers); $j++) {
            if ($dossiers[$i]['numero_dossier'] == $dossiers[$j]['numero_dossier'] && 
                $dossiers[$i]['numero_adherent'] == $dossiers[$j]['numero_adherent']) {
                $duplicates[] = [
                    'original' => $dossiers[$i],
                    'duplicate' => $dossiers[$j]
                ];
            }
        }
    }
    
    echo "<h4>⚠️ التكرارات المكتشفة:</h4>";
    foreach ($duplicates as $dup) {
        echo "• " . $dup['original']['numero_dossier'] . " (ID: " . $dup['original']['id'] . ") مكرر في (ID: " . $dup['duplicate']['id'] . ")<br>";
    }
    
    echo "<h3>🗑️ Étape 2: حذف التكرارات</h3>";
    
    foreach ($duplicates as $dup) {
        $duplicate_id = $dup['duplicate']['id'];
        
        // حذف الأسباب المرتبطة
        $sql = "DELETE FROM dossier_raisons WHERE dossier_id = ?";
        $db->query($sql, [$duplicate_id]);
        
        // حذف الدوسيه المكرر
        $sql = "DELETE FROM dossiers_retournes WHERE id = ?";
        $db->query($sql, [$duplicate_id]);
        
        echo "• ✅ تم حذف التكرار: ID $duplicate_id<br>";
    }
    
    echo "<h3>📦 Étape 3: إنشاء الدوسيه الخامس الصحيح</h3>";
    
    // التحقق من عدد الدوسيهات المتبقية
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $count = $db->single($sql, [$numero_bordereau, $cdm_id]);
    echo "• عدد الدوسيهات المتبقية: " . $count['count'] . "<br>";
    
    if ($count['count'] < 5) {
        echo "📦 إنشاء الدوسيه الخامس...<br>";
        
        // الحصول على معرف الفعل والسبب
        $sql = "SELECT id FROM actes WHERE nom LIKE '%dentaire%' OR nom LIKE '%Soins%' ORDER BY id LIMIT 1";
        $acte_dentaire = $db->single($sql);
        if (!$acte_dentaire) {
            $sql = "SELECT id FROM actes ORDER BY id DESC LIMIT 1";
            $acte_dentaire = $db->single($sql);
        }
        
        $sql = "SELECT id FROM raisons_retour WHERE raison LIKE '%illisible%' OR raison LIKE '%Document%' ORDER BY id LIMIT 1";
        $raison_illisible = $db->single($sql);
        if (!$raison_illisible) {
            $sql = "SELECT id FROM raisons_retour ORDER BY id DESC LIMIT 1";
            $raison_illisible = $db->single($sql);
        }
        
        // إنشاء الدوسيه الخامس الصحيح
        $dossier_5 = [
            'numero_dossier' => 'FINAL-005-' . date('His'),
            'numero_adherent' => 'ADH-FINAL-005',
            'nom' => 'BENNANI Aicha',
            'beneficiaire' => 'BENNANI Aicha',
            'acte_id' => $acte_dentaire['id'],
            'montant' => 180.00,
            'raison_retour_id' => $raison_illisible['id'],
            'notes' => 'Soins dentaires - Document illisible'
        ];
        
        $sql = "INSERT INTO dossiers_retournes (numero_bordereau, cdm_id, date_bordereau, numero_dossier,
                                              numero_adherent, nom, beneficiaire, acte_id, montant,
                                              raison_retour_id, date_retour, corrige, notes, created_by, created_at)
                VALUES (?, ?, '2024-01-15', ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 1, NOW())";
        
        $params = [
            $numero_bordereau,
            $cdm_id,
            $dossier_5['numero_dossier'],
            $dossier_5['numero_adherent'],
            $dossier_5['nom'],
            $dossier_5['beneficiaire'],
            $dossier_5['acte_id'],
            $dossier_5['montant'],
            $dossier_5['raison_retour_id'],
            date('Y-m-d'),
            $dossier_5['notes']
        ];
        
        $db->query($sql, $params);
        $dossier_id = $db->lastInsertId();
        
        // إضافة السبب في جدول dossier_raisons
        $sql = "INSERT INTO dossier_raisons (dossier_id, raison_id, created_at) VALUES (?, ?, NOW())";
        $db->query($sql, [$dossier_id, $dossier_5['raison_retour_id']]);
        
        echo "• ✅ تم إنشاء الدوسيه الخامس: " . $dossier_5['numero_dossier'] . " (ID: $dossier_id)<br>";
    }
    
    echo "<h3>🔍 Étape 4: التحقق النهائي</h3>";
    
    // عرض جميع الدوسيهات النهائية
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.id ASC";
    $final_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "<h4>📋 الدوسيهات النهائية:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='padding: 5px;'>#</th>";
    echo "<th style='padding: 5px;'>N° Dossier</th>";
    echo "<th style='padding: 5px;'>N° Adhérent</th>";
    echo "<th style='padding: 5px;'>Nom</th>";
    echo "<th style='padding: 5px;'>Bénéficiaire</th>";
    echo "<th style='padding: 5px;'>Acte</th>";
    echo "<th style='padding: 5px;'>Montant</th>";
    echo "<th style='padding: 5px;'>Raison</th>";
    echo "</tr>";
    
    foreach ($final_dossiers as $index => $dossier) {
        echo "<tr>";
        echo "<td style='padding: 5px; text-align: center;'>" . ($index + 1) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_dossier']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_adherent']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['nom']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['beneficiaire']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['acte_nom'] ?? 'N/A') . "</td>";
        echo "<td style='padding: 5px; text-align: right;'>" . number_format($dossier['montant'], 2) . " DH</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['raison_nom'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // التحقق من عدم وجود تكرارات
    $sql = "SELECT numero_dossier, numero_adherent, COUNT(*) as count
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            GROUP BY numero_dossier, numero_adherent
            HAVING COUNT(*) > 1";
    $remaining_duplicates = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    if (empty($remaining_duplicates)) {
        echo "✅ <strong>لا توجد تكرارات - جميع الدوسيهات فريدة</strong><br>";
        echo "• <strong>العدد الإجمالي:</strong> " . count($final_dossiers) . " دوسيه<br>";
    } else {
        echo "⚠️ <strong>تكرارات متبقية:</strong> " . count($remaining_duplicates) . "<br>";
    }
    
    echo "<h3>🎉 تم الإصلاح بنجاح!</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
    echo "<h4 style='color: #155724; margin-top: 0;'>✅ النتائج:</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li><strong>التكرارات المحذوفة:</strong> " . count($duplicates) . "</li>";
    echo "<li><strong>الدوسيهات النهائية:</strong> " . count($final_dossiers) . "</li>";
    echo "<li><strong>جميع الدوسيهات فريدة:</strong> نعم</li>";
    echo "<li><strong>جاهز للطباعة:</strong> نعم</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🔗 اختبار النتائج:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank' style='color: #28a745; font-weight: bold;'>🖨️ صفحة الطباعة</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank' style='color: #17a2b8; font-weight: bold;'>📝 صفحة التحرير</a></li>";
    echo "<li><a href='test_export.php' target='_blank' style='color: #ffc107; font-weight: bold;'>📊 اختبار التصدير</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>التتبع: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح التكرار النهائي - LOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        table {
            background: white;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            direction: ltr;
        }
    </style>
</head>
<body>
</body>
</html>
