<?php
/**
 * Script لحذف التكرارات وإنشاء بيانات نظيفة
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // Créer la session admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🧹 Nettoyage des Duplications</h2>";
    echo "<p>✅ Session admin créée</p>";
    
    // Paramètres du test
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>🔍 Étape 1: Identification des duplications</h3>";
    
    // Trouver les duplications
    $sql = "SELECT numero_dossier, numero_adherent, COUNT(*) as count
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            GROUP BY numero_dossier, numero_adherent
            HAVING COUNT(*) > 1";
    $duplicates = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• Duplications trouvées: " . count($duplicates) . "<br>";
    
    foreach ($duplicates as $duplicate) {
        echo "  - " . $duplicate['numero_dossier'] . " / " . $duplicate['numero_adherent'] . " (x" . $duplicate['count'] . ")<br>";
    }
    
    echo "<h3>🗑️ Étape 2: Suppression des duplications</h3>";
    
    foreach ($duplicates as $duplicate) {
        // Garder seulement le premier enregistrement (le plus ancien)
        $sql = "SELECT id FROM dossiers_retournes 
                WHERE numero_bordereau = ? AND cdm_id = ? 
                AND numero_dossier = ? AND numero_adherent = ?
                ORDER BY created_at ASC";
        $records = $db->all($sql, [
            $numero_bordereau, 
            $cdm_id, 
            $duplicate['numero_dossier'], 
            $duplicate['numero_adherent']
        ]);
        
        // Supprimer tous sauf le premier
        for ($i = 1; $i < count($records); $i++) {
            $record_id = $records[$i]['id'];
            
            // Supprimer d'abord les raisons associées
            $sql = "DELETE FROM dossier_raisons WHERE dossier_id = ?";
            $db->query($sql, [$record_id]);
            
            // Supprimer le dossier
            $sql = "DELETE FROM dossiers_retournes WHERE id = ?";
            $db->query($sql, [$record_id]);
            
            echo "  ✅ Supprimé: " . $duplicate['numero_dossier'] . " (ID: $record_id)<br>";
        }
    }
    
    echo "<h3>🧹 Étape 3: Nettoyage complet et recréation</h3>";
    
    // Supprimer tous les dossiers du bordereau de test
    $sql = "DELETE FROM dossier_raisons WHERE dossier_id IN (
                SELECT id FROM dossiers_retournes 
                WHERE numero_bordereau = ? AND cdm_id = ?
            )";
    $db->query($sql, [$numero_bordereau, $cdm_id]);
    
    $sql = "DELETE FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $result = $db->query($sql, [$numero_bordereau, $cdm_id]);
    echo "• Tous les anciens dossiers supprimés<br>";
    
    echo "<h3>📦 Étape 4: Création de nouveaux dossiers uniques</h3>";
    
    // Récupérer les IDs des actes et raisons disponibles
    $sql = "SELECT id FROM actes ORDER BY id LIMIT 5";
    $actes = $db->all($sql);
    
    $sql = "SELECT id FROM raisons_retour ORDER BY id LIMIT 5";
    $raisons = $db->all($sql);
    
    // Créer des dossiers uniques
    $nouveaux_dossiers = [
        [
            'numero_dossier' => 'DOS-UNIQUE-001',
            'numero_adherent' => 'ADH-001-2024',
            'nom' => 'ALAMI Mohammed',
            'beneficiaire' => 'ALAMI Mohammed',
            'acte_id' => $actes[0]['id'] ?? 1,
            'montant' => 150.00,
            'raison_retour_id' => $raisons[0]['id'] ?? 1,
            'notes' => 'Dossier unique 1 - Consultation générale'
        ],
        [
            'numero_dossier' => 'DOS-UNIQUE-002',
            'numero_adherent' => 'ADH-002-2024',
            'nom' => 'BENNANI Fatima',
            'beneficiaire' => 'BENNANI Fatima',
            'acte_id' => $actes[1]['id'] ?? 2,
            'montant' => 200.00,
            'raison_retour_id' => $raisons[1]['id'] ?? 2,
            'notes' => 'Dossier unique 2 - Radiographie'
        ],
        [
            'numero_dossier' => 'DOS-UNIQUE-003',
            'numero_adherent' => 'ADH-003-2024',
            'nom' => 'TAZI Ahmed',
            'beneficiaire' => 'TAZI Aicha',
            'acte_id' => $actes[2]['id'] ?? 3,
            'montant' => 300.00,
            'raison_retour_id' => $raisons[2]['id'] ?? 3,
            'notes' => 'Dossier unique 3 - Échographie'
        ],
        [
            'numero_dossier' => 'DOS-UNIQUE-004',
            'numero_adherent' => 'ADH-004-2024',
            'nom' => 'IDRISSI Youssef',
            'beneficiaire' => 'IDRISSI Youssef',
            'acte_id' => $actes[3]['id'] ?? 4,
            'montant' => 120.00,
            'raison_retour_id' => $raisons[3]['id'] ?? 4,
            'notes' => 'Dossier unique 4 - Analyses biologiques'
        ],
        [
            'numero_dossier' => 'DOS-UNIQUE-005',
            'numero_adherent' => 'ADH-005-2024',
            'nom' => 'BENNANI Aicha',
            'beneficiaire' => 'BENNANI Aicha',
            'acte_id' => $actes[4]['id'] ?? 5,
            'montant' => 180.00,
            'raison_retour_id' => $raisons[4]['id'] ?? 5,
            'notes' => 'Dossier unique 5 - Consultation spécialisée'
        ]
    ];
    
    foreach ($nouveaux_dossiers as $dossier) {
        $sql = "INSERT INTO dossiers_retournes (numero_bordereau, cdm_id, date_bordereau, numero_dossier,
                                              numero_adherent, nom, beneficiaire, acte_id, montant,
                                              raison_retour_id, date_retour, corrige, notes, created_by, created_at)
                VALUES (?, ?, '2024-01-15', ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 1, NOW())";
        
        $params = [
            $numero_bordereau,
            $cdm_id,
            $dossier['numero_dossier'],
            $dossier['numero_adherent'],
            $dossier['nom'],
            $dossier['beneficiaire'],
            $dossier['acte_id'],
            $dossier['montant'],
            $dossier['raison_retour_id'],
            date('Y-m-d'),
            $dossier['notes']
        ];
        
        $db->query($sql, $params);
        $dossier_id = $db->lastInsertId();
        
        // Ajouter des raisons multiples pour certains dossiers
        if (in_array($dossier['numero_dossier'], ['DOS-UNIQUE-002', 'DOS-UNIQUE-004'])) {
            $raisons_multiples = [$dossier['raison_retour_id']];
            if (isset($raisons[1])) $raisons_multiples[] = $raisons[1]['id'];
            if (isset($raisons[2])) $raisons_multiples[] = $raisons[2]['id'];
            
            foreach ($raisons_multiples as $raison_id) {
                $sql = "INSERT IGNORE INTO dossier_raisons (dossier_id, raison_id, created_at) VALUES (?, ?, NOW())";
                $db->query($sql, [$dossier_id, $raison_id]);
            }
        }
        
        echo "  ✅ " . $dossier['numero_dossier'] . " - " . $dossier['nom'] . " (ID: $dossier_id)<br>";
    }
    
    echo "<h3>🔍 Étape 5: Vérification finale</h3>";
    
    // Vérifier qu'il n'y a plus de duplications
    $sql = "SELECT numero_dossier, numero_adherent, COUNT(*) as count
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            GROUP BY numero_dossier, numero_adherent
            HAVING COUNT(*) > 1";
    $remaining_duplicates = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    if (empty($remaining_duplicates)) {
        echo "✅ <strong>Aucune duplication restante</strong><br>";
    } else {
        echo "⚠️ <strong>Duplications restantes:</strong> " . count($remaining_duplicates) . "<br>";
    }
    
    // Compter le total des dossiers
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $total = $db->single($sql, [$numero_bordereau, $cdm_id]);
    echo "• <strong>Total dossiers:</strong> " . $total['count'] . "<br>";
    
    echo "<h3>🎉 Nettoyage terminé!</h3>";
    echo "<ul>";
    echo "<li>✅ Duplications supprimées</li>";
    echo "<li>✅ Nouveaux dossiers uniques créés</li>";
    echo "<li>✅ Raisons multiples ajoutées</li>";
    echo "<li>✅ Base de données nettoyée</li>";
    echo "</ul>";
    
    echo "<h3>🔗 Tests disponibles:</h3>";
    echo "<ul>";
    echo "<li><a href='test_print.php' target='_blank'>🖨️ Test d'impression</a></li>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank'>🖨️ Page d'impression directe</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank'>📝 Page d'édition</a></li>";
    echo "<li><a href='test_export.php' target='_blank'>📊 Test d'exportation</a></li>";
    echo "<li><a href='final_report.php' target='_blank'>📋 Rapport final</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Erreur</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
    echo "<p>Trace: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nettoyage Duplications - LOT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h2, h3 {
            color: #333;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
