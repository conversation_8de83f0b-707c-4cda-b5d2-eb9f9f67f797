<?php
/**
 * فحص قاعدة البيانات لتحديد مصدر التكرار
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // Créer la session admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🔍 Debug de la Base de Données</h2>";
    echo "<p>✅ Session admin créée</p>";
    
    // Paramètres du test
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>📊 Analyse directe de la base de données</h3>";
    
    // Requête directe sur la table dossiers_retournes
    $sql = "SELECT id, numero_dossier, numero_adherent, nom, beneficiaire, montant, created_at
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            ORDER BY id ASC";
    $dossiers_raw = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "<h4>📋 Données brutes de la table dossiers_retournes:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='padding: 5px;'>ID</th>";
    echo "<th style='padding: 5px;'>N° Dossier</th>";
    echo "<th style='padding: 5px;'>N° Adhérent</th>";
    echo "<th style='padding: 5px;'>Nom</th>";
    echo "<th style='padding: 5px;'>Bénéficiaire</th>";
    echo "<th style='padding: 5px;'>Montant</th>";
    echo "<th style='padding: 5px;'>Créé le</th>";
    echo "</tr>";
    
    $duplicates_found = [];
    foreach ($dossiers_raw as $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($duplicates_found[$key])) {
            $duplicates_found[$key]++;
            $bg_color = '#ffebee'; // Rouge clair pour les duplications
        } else {
            $duplicates_found[$key] = 1;
            $bg_color = '#ffffff'; // Blanc pour les uniques
        }
        
        echo "<tr style='background-color: $bg_color;'>";
        echo "<td style='padding: 5px;'>" . $dossier['id'] . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_dossier']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_adherent']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['nom']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['beneficiaire']) . "</td>";
        echo "<td style='padding: 5px;'>" . number_format($dossier['montant'], 2) . " DH</td>";
        echo "<td style='padding: 5px;'>" . $dossier['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>🔍 Analyse des duplications:</h4>";
    $has_duplicates = false;
    foreach ($duplicates_found as $key => $count) {
        if ($count > 1) {
            $has_duplicates = true;
            list($numero_dossier, $numero_adherent) = explode('_', $key);
            echo "⚠️ <strong>Duplication:</strong> $numero_dossier / $numero_adherent (x$count)<br>";
        }
    }
    
    if (!$has_duplicates) {
        echo "✅ <strong>Aucune duplication dans la base de données</strong><br>";
    }
    
    echo "<h3>🔧 Suppression manuelle des duplications</h3>";
    
    if ($has_duplicates) {
        echo "<p>Suppression des duplications détectées...</p>";
        
        foreach ($duplicates_found as $key => $count) {
            if ($count > 1) {
                list($numero_dossier, $numero_adherent) = explode('_', $key);
                
                // Récupérer tous les enregistrements pour cette combinaison
                $sql = "SELECT id FROM dossiers_retournes 
                        WHERE numero_bordereau = ? AND cdm_id = ? 
                        AND numero_dossier = ? AND numero_adherent = ?
                        ORDER BY id ASC";
                $records = $db->all($sql, [$numero_bordereau, $cdm_id, $numero_dossier, $numero_adherent]);
                
                // Garder le premier, supprimer les autres
                for ($i = 1; $i < count($records); $i++) {
                    $record_id = $records[$i]['id'];
                    
                    // Supprimer les raisons associées
                    $sql = "DELETE FROM dossier_raisons WHERE dossier_id = ?";
                    $db->query($sql, [$record_id]);
                    
                    // Supprimer le dossier
                    $sql = "DELETE FROM dossiers_retournes WHERE id = ?";
                    $db->query($sql, [$record_id]);
                    
                    echo "• ✅ Supprimé: $numero_dossier (ID: $record_id)<br>";
                }
            }
        }
    }
    
    echo "<h3>📊 Vérification finale</h3>";
    
    // Nouvelle vérification
    $sql = "SELECT id, numero_dossier, numero_adherent, nom, montant
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            ORDER BY id ASC";
    $final_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "<h4>📋 Données finales après nettoyage:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='padding: 5px;'>ID</th>";
    echo "<th style='padding: 5px;'>N° Dossier</th>";
    echo "<th style='padding: 5px;'>N° Adhérent</th>";
    echo "<th style='padding: 5px;'>Nom</th>";
    echo "<th style='padding: 5px;'>Montant</th>";
    echo "</tr>";
    
    $final_duplicates = [];
    foreach ($final_dossiers as $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($final_duplicates[$key])) {
            $final_duplicates[$key]++;
            $bg_color = '#ffebee';
        } else {
            $final_duplicates[$key] = 1;
            $bg_color = '#e8f5e8';
        }
        
        echo "<tr style='background-color: $bg_color;'>";
        echo "<td style='padding: 5px;'>" . $dossier['id'] . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_dossier']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_adherent']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['nom']) . "</td>";
        echo "<td style='padding: 5px;'>" . number_format($dossier['montant'], 2) . " DH</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Vérification finale des duplications
    $final_has_duplicates = false;
    foreach ($final_duplicates as $key => $count) {
        if ($count > 1) {
            $final_has_duplicates = true;
            list($numero_dossier, $numero_adherent) = explode('_', $key);
            echo "⚠️ <strong>Duplication restante:</strong> $numero_dossier / $numero_adherent (x$count)<br>";
        }
    }
    
    if (!$final_has_duplicates) {
        echo "✅ <strong>AUCUNE DUPLICATION RESTANTE</strong><br>";
        echo "• <strong>Total dossiers uniques:</strong> " . count($final_dossiers) . "<br>";
    }
    
    echo "<h3>🧪 Test de la requête d'impression</h3>";
    
    // Tester la même requête que dans print.php
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $print_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "<h4>📋 Résultat de la requête d'impression:</h4>";
    echo "• <strong>Nombre de dossiers retournés:</strong> " . count($print_dossiers) . "<br>";
    
    if (count($print_dossiers) != count($final_dossiers)) {
        echo "⚠️ <strong>PROBLÈME:</strong> La requête d'impression retourne un nombre différent de dossiers!<br>";
        echo "• Dossiers dans la table: " . count($final_dossiers) . "<br>";
        echo "• Dossiers dans la requête d'impression: " . count($print_dossiers) . "<br>";
    } else {
        echo "✅ <strong>OK:</strong> La requête d'impression retourne le bon nombre de dossiers<br>";
    }
    
    echo "<h3>🔗 Tests finaux:</h3>";
    echo "<ul>";
    echo "<li><a href='test_print.php' target='_blank'>🖨️ Test d'impression</a></li>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank'>🖨️ Page d'impression directe</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank'>📝 Page d'édition</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Erreur</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
    echo "<p>Trace: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Base de Données - LOT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        table {
            background: white;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
</body>
</html>
