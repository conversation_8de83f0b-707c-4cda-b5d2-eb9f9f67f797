<?php
// Contrôleur pour l'installation de l'application

// Titre de la page
$page_title = 'Installation';

// Vérifier si l'application est déjà installée
if (!needsInstallation()) {
    redirect('index.php?page=dashboard', 'L\'application est déjà installée.', 'info');
}

// Traitement du formulaire d'installation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=install', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }
    
    // Récupérer et nettoyer les données du formulaire
    $admin_username = clean($_POST['admin_username'] ?? '');
    $admin_password = $_POST['admin_password'] ?? '';
    $admin_password_confirm = $_POST['admin_password_confirm'] ?? '';
    
    // Valider les données
    $errors = [];
    
    if (empty($admin_username)) {
        $errors[] = 'Le nom d\'utilisateur administrateur est requis.';
    }
    
    if (empty($admin_password)) {
        $errors[] = 'Le mot de passe administrateur est requis.';
    } elseif (strlen($admin_password) < 8) {
        $errors[] = 'Le mot de passe doit contenir au moins 8 caractères.';
    }
    
    if ($admin_password !== $admin_password_confirm) {
        $errors[] = 'Les mots de passe ne correspondent pas.';
    }
    
    // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = $_POST;
        redirect('index.php?page=install', 'Veuillez corriger les erreurs.', 'danger');
    }
    
    try {
        // Créer les tables de la base de données
        $db = Database::getInstance();
        
        // Démarrer une transaction
        $db->beginTransaction();
        
        // Table des utilisateurs
        $db->query("
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                email VARCHAR(100),
                role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
                active TINYINT(1) NOT NULL DEFAULT 1,
                last_login DATETIME,
                created_at DATETIME NOT NULL,
                updated_at DATETIME
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // Table des jetons d'utilisateur (pour "Se souvenir de moi")
        $db->query("
            CREATE TABLE IF NOT EXISTS user_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token VARCHAR(255) NOT NULL,
                expires_at DATETIME NOT NULL,
                created_at DATETIME NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // Table des CDM
        $db->query("
            CREATE TABLE IF NOT EXISTS cdm (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nom VARCHAR(100) NOT NULL,
                adresse TEXT,
                telephone VARCHAR(20),
                email VARCHAR(100),
                contact_nom VARCHAR(100),
                active TINYINT(1) NOT NULL DEFAULT 1,
                created_at DATETIME NOT NULL,
                updated_at DATETIME
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // Table des actes
        $db->query("
            CREATE TABLE IF NOT EXISTS actes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nom VARCHAR(100) NOT NULL,
                description TEXT,
                active TINYINT(1) NOT NULL DEFAULT 1,
                created_at DATETIME NOT NULL,
                updated_at DATETIME
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // Table des raisons de retour
        $db->query("
            CREATE TABLE IF NOT EXISTS raisons_retour (
                id INT AUTO_INCREMENT PRIMARY KEY,
                raison VARCHAR(100) NOT NULL,
                description TEXT,
                active TINYINT(1) NOT NULL DEFAULT 1,
                created_at DATETIME NOT NULL,
                updated_at DATETIME
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // Table des lots
        $db->query("
            CREATE TABLE IF NOT EXISTS lots (
                id INT AUTO_INCREMENT PRIMARY KEY,
                numero_lot VARCHAR(20) NOT NULL UNIQUE,
                cdm_id INT NOT NULL,
                date_demande DATE NOT NULL,
                date_reception DATE,
                statut ENUM('en_attente', 'recu', 'traite', 'archive') NOT NULL DEFAULT 'en_attente',
                notes TEXT,
                created_by INT NOT NULL,
                created_at DATETIME NOT NULL,
                updated_at DATETIME,
                FOREIGN KEY (cdm_id) REFERENCES cdm(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // Table des dossiers
        $db->query("
            CREATE TABLE IF NOT EXISTS dossiers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                lot_id INT NOT NULL,
                numero_dossier VARCHAR(50) NOT NULL,
                numero_adherent VARCHAR(50) NOT NULL,
                nom VARCHAR(100) NOT NULL,
                beneficiaire VARCHAR(100),
                acte_id INT,
                montant DECIMAL(10,2),
                numero_bon VARCHAR(50),
                recu TINYINT(1) NOT NULL DEFAULT 0,
                notes TEXT,
                created_at DATETIME NOT NULL,
                updated_at DATETIME,
                FOREIGN KEY (lot_id) REFERENCES lots(id) ON DELETE CASCADE,
                FOREIGN KEY (acte_id) REFERENCES actes(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // Table des dossiers retournés
        $db->query("
            CREATE TABLE IF NOT EXISTS dossiers_retournes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                numero_bordereau VARCHAR(20) NOT NULL,
                cdm_id INT NOT NULL,
                date_bordereau DATE NOT NULL,
                numero_dossier VARCHAR(50) NOT NULL,
                numero_adherent VARCHAR(50) NOT NULL,
                nom VARCHAR(100) NOT NULL,
                beneficiaire VARCHAR(100),
                acte_id INT,
                montant DECIMAL(10,2),
                raison_retour_id INT NOT NULL,
                date_retour DATE NOT NULL,
                date_correction DATE,
                corrige TINYINT(1) NOT NULL DEFAULT 0,
                notes TEXT,
                created_by INT NOT NULL,
                created_at DATETIME NOT NULL,
                updated_at DATETIME,
                FOREIGN KEY (cdm_id) REFERENCES cdm(id),
                FOREIGN KEY (acte_id) REFERENCES actes(id),
                FOREIGN KEY (raison_retour_id) REFERENCES raisons_retour(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // Table des journaux d'activité
        $db->query("
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                action VARCHAR(100) NOT NULL,
                details TEXT,
                ip_address VARCHAR(45),
                created_at DATETIME NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // Créer l'utilisateur administrateur
        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, role, active, created_at) VALUES (?, ?, 'admin', 1, NOW())";
        $db->query($sql, [$admin_username, $hashed_password]);
        $admin_id = $db->lastInsertId();
        
        // Insérer des données de base
        
        // Quelques CDM
        $cdm_data = [
            ['CDM Casablanca', 'Avenue Hassan II, Casablanca', '0522123456', '<EMAIL>', 'Dr. Mohammed'],
            ['CDM Rabat', 'Avenue Mohammed V, Rabat', '0537123456', '<EMAIL>', 'Dr. Ahmed'],
            ['CDM Marrakech', 'Avenue Mohammed VI, Marrakech', '0524123456', '<EMAIL>', 'Dr. Fatima'],
            ['CDM Tanger', 'Boulevard Mohammed VI, Tanger', '0539123456', '<EMAIL>', 'Dr. Karim'],
            ['CDM Fès', 'Avenue Hassan II, Fès', '0535123456', '<EMAIL>', 'Dr. Samira']
        ];
        
        $sql = "INSERT INTO cdm (nom, adresse, telephone, email, contact_nom, active, created_at) VALUES (?, ?, ?, ?, ?, 1, NOW())";
        foreach ($cdm_data as $cdm) {
            $db->query($sql, $cdm);
        }
        
        // Quelques actes médicaux
        $actes_data = [
            ['Consultation générale', 'Consultation médicale générale'],
            ['Radiographie', 'Examen radiographique standard'],
            ['Échographie', 'Examen échographique'],
            ['Analyse de sang', 'Analyse sanguine complète'],
            ['Scanner', 'Examen tomodensitométrique'],
            ['IRM', 'Imagerie par résonance magnétique'],
            ['Électrocardiogramme', 'ECG standard'],
            ['Consultation spécialiste', 'Consultation avec un médecin spécialiste']
        ];
        
        $sql = "INSERT INTO actes (nom, description, active, created_at) VALUES (?, ?, 1, NOW())";
        foreach ($actes_data as $acte) {
            $db->query($sql, $acte);
        }
        
        // Quelques raisons de retour
        $raisons_data = [
            ['Document manquant', 'Un ou plusieurs documents requis sont manquants'],
            ['Signature manquante', 'Absence de signature sur un document'],
            ['Information incomplète', 'Informations requises non fournies ou incomplètes'],
            ['Erreur d\'identification', 'Erreur dans les informations d\'identification du patient'],
            ['Prescription invalide', 'Prescription médicale non valide ou expirée'],
            ['Montant incorrect', 'Erreur dans le montant facturé'],
            ['Acte non couvert', 'Acte médical non couvert par l\'assurance'],
            ['Doublon', 'Dossier en double']
        ];
        
        $sql = "INSERT INTO raisons_retour (raison, description, active, created_at) VALUES (?, ?, 1, NOW())";
        foreach ($raisons_data as $raison) {
            $db->query($sql, $raison);
        }
        
        // Valider la transaction
        $db->commit();
        
        // Journaliser l'installation
        logActivity($admin_id, 'Installation', 'Installation de l\'application');
        
        // Rediriger vers la page de connexion
        redirect('index.php?page=login', 'Installation réussie. Veuillez vous connecter.', 'success');
        
    } catch (Exception $e) {
        // Annuler la transaction en cas d'erreur
        if ($db->inTransaction()) {
            $db->rollBack();
        }
        
        redirect('index.php?page=install', 'Erreur lors de l\'installation: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'admin_username' => 'admin',
    'admin_password' => '',
    'admin_password_confirm' => ''
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Charger la vue
require_once VIEWS_PATH . 'install.php';
?>
