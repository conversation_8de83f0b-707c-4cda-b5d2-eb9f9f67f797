<?php
// Vue pour la modification d'un dossier retourné
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-edit"></i> Modifier un dossier retourné</h1>
        <div>
            <a href="index.php?page=retours/view&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour au bordereau
            </a>
        </div>
    </div>

    <?php if (!empty($form_errors)): ?>
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle"></i> Erreurs:</h5>
            <ul class="mb-0">
                <?php foreach ($form_errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-folder"></i> Informations du dossier</h5>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=retours/edit_dossier&id=<?php echo $dossier_id; ?>&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="numero_dossier" class="form-label required">Numéro de dossier</label>
                        <input type="text" class="form-control" id="numero_dossier" name="numero_dossier" value="<?php echo $form_data['numero_dossier']; ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="numero_adherent" class="form-label required">Numéro d'adhérent</label>
                        <input type="text" class="form-control" id="numero_adherent" name="numero_adherent" value="<?php echo $form_data['numero_adherent']; ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="date_retour" class="form-label required">Date de retour</label>
                        <input type="text" class="form-control datepicker" id="date_retour" name="date_retour" value="<?php echo $form_data['date_retour']; ?>" required>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="nom" class="form-label required">Nom</label>
                        <input type="text" class="form-control" id="nom" name="nom" value="<?php echo $form_data['nom']; ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="beneficiaire" class="form-label">Bénéficiaire</label>
                        <input type="text" class="form-control" id="beneficiaire" name="beneficiaire" value="<?php echo $form_data['beneficiaire']; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="acte_id" class="form-label">Acte</label>
                        <select class="form-select" id="acte_id" name="acte_id">
                            <option value="">Sélectionnez un acte</option>
                            <?php foreach ($actes_list as $acte): ?>
                                <option value="<?php echo $acte['id']; ?>" <?php echo $form_data['acte_id'] == $acte['id'] ? 'selected' : ''; ?>>
                                    <?php echo $acte['nom']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="montant" class="form-label">Montant</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="montant" name="montant" value="<?php echo $form_data['montant']; ?>">
                            <span class="input-group-text">DH</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="raison_retour_id" class="form-label required">Motif de retour</label>
                        <select class="form-select" id="raison_retour_id" name="raison_retour_id" required>
                            <option value="">Sélectionnez une raison</option>
                            <?php foreach ($raisons_list as $raison): ?>
                                <option value="<?php echo $raison['id']; ?>" <?php echo $form_data['raison_retour_id'] == $raison['id'] ? 'selected' : ''; ?>>
                                    <?php echo $raison['raison']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="corrige" name="corrige" <?php echo $form_data['corrige'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="corrige">Dossier corrigé</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="date_correction" class="form-label">Date de correction</label>
                        <input type="text" class="form-control datepicker" id="date_correction" name="date_correction" value="<?php echo $form_data['date_correction']; ?>">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="commentaire" class="form-label">Commentaire</label>
                    <textarea class="form-control" id="commentaire" name="commentaire" rows="3"><?php echo $form_data['commentaire']; ?></textarea>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="index.php?page=retours/view&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Initialiser les sélecteurs de date
        $('.datepicker').datepicker({
            format: 'dd/mm/yyyy',
            autoclose: true,
            todayHighlight: true,
            language: 'fr'
        });
    });
</script>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
