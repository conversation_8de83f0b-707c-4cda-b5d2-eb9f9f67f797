<?php
// Contrôleur pour la gestion des utilisateurs

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Titre de la page
$page_title = 'Gestion des utilisateurs';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de pagination
$page_num = isset($_GET['page_num']) ? intval($_GET['page_num']) : 1;
$items_per_page = ITEMS_PER_PAGE;
$offset = ($page_num - 1) * $items_per_page;

// Recherche
$search = isset($_GET['search']) ? clean($_GET['search']) : '';

// Construction de la requête SQL
$sql_count = "SELECT COUNT(*) as total FROM users";
$sql = "SELECT * FROM users";

$params = [];

// Appliquer le filtre de recherche
if (!empty($search)) {
    $sql_count .= " WHERE username LIKE ? OR email LIKE ?";
    $sql .= " WHERE username LIKE ? OR email LIKE ?";
    $search_param = "%$search%";
    $params = [$search_param, $search_param];
}

// Obtenir le nombre total d'utilisateurs
$result = $db->single($sql_count, $params);
$total_items = $result['total'];
$total_pages = ceil($total_items / $items_per_page);

// Ajouter l'ordre et la limite à la requête principale
$sql .= " ORDER BY username ASC LIMIT ?, ?";
$params[] = $offset;
$params[] = $items_per_page;

// Exécuter la requête
$users_list = $db->all($sql, $params);

// Charger la vue
require_once VIEWS_PATH . 'admin/users/index.php';
?>
