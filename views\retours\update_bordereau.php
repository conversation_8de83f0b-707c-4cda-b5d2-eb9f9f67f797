<?php
// Vue pour la modification des informations d'un bordereau de retour
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-edit"></i> Modifier le bordereau #<?php echo $numero_bordereau; ?></h1>
        <div>
            <a href="index.php?page=retours/edit&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour au bordereau
            </a>
        </div>
    </div>

    <?php if (!empty($form_errors)): ?>
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle"></i> Erreurs:</h5>
            <ul class="mb-0">
                <?php foreach ($form_errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-info-circle"></i> Informations du bordereau</h5>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=retours/update_bordereau&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="cdm_id" class="form-label required">CDM</label>
                        <select class="form-select" id="cdm_id" name="cdm_id" required>
                            <option value="">Sélectionnez un CDM</option>
                            <?php foreach ($cdms as $cdm): ?>
                                <option value="<?php echo $cdm['id']; ?>" <?php echo $form_data['cdm_id'] == $cdm['id'] ? 'selected' : ''; ?>>
                                    <?php echo $cdm['nom']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="date_bordereau" class="form-label required">Date du bordereau</label>
                        <input type="text" class="form-control datepicker" id="date_bordereau" name="date_bordereau" value="<?php echo $form_data['date_bordereau']; ?>" required placeholder="jj/mm/aaaa">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="4"><?php echo $form_data['notes']; ?></textarea>
                    <div class="form-text">Ajoutez ici des notes ou des commentaires concernant ce bordereau.</div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="index.php?page=retours/edit&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
