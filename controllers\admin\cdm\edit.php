<?php
// Contrôleur pour la modification d'un CDM

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Vérifier si l'ID est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=admin/cdm', 'ID du CDM non spécifié.', 'danger');
}

$id = intval($_GET['id']);

// Titre de la page
$page_title = 'Modifier un CDM';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du CDM
$sql = "SELECT * FROM cdm WHERE id = ?";
$cdm = $db->single($sql, [$id]);

if (!$cdm) {
    redirect('index.php?page=admin/cdm', 'CDM non trouvé.', 'danger');
}

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        redirect('index.php?page=admin/cdm', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }
    
    // Récupération des données du formulaire
    $nom = clean($_POST['nom']);
    $adresse = clean($_POST['adresse']);
    $contact_nom = clean($_POST['contact_nom']);
    $telephone = clean($_POST['telephone']);
    $email = clean($_POST['email']);
    $active = isset($_POST['active']) ? 1 : 0;
    
    // Validation des données
    $errors = [];
    
    if (empty($nom)) {
        $errors[] = 'Le nom du CDM est obligatoire.';
    }
    
    if (empty($adresse)) {
        $errors[] = 'L\'adresse du CDM est obligatoire.';
    }
    
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'L\'adresse email n\'est pas valide.';
    }
    
    // Vérifier si le CDM existe déjà avec ce nom (sauf lui-même)
    $sql = "SELECT id FROM cdm WHERE nom = ? AND id != ?";
    $result = $db->single($sql, [$nom, $id]);
    
    if ($result) {
        $errors[] = 'Un CDM avec ce nom existe déjà.';
    }
    
    // Si aucune erreur, mettre à jour le CDM
    if (empty($errors)) {
        $sql = "UPDATE cdm 
                SET nom = ?, adresse = ?, contact_nom = ?, telephone = ?, email = ?, active = ?, updated_at = NOW() 
                WHERE id = ?";
        $params = [$nom, $adresse, $contact_nom, $telephone, $email, $active, $id];
        
        if ($db->query($sql, $params)) {
            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Modification d\'un CDM', 'ID: ' . $id . ', Nom: ' . $nom);
            
            redirect('index.php?page=admin/cdm', 'CDM modifié avec succès.', 'success');
        } else {
            $errors[] = 'Erreur lors de la modification du CDM.';
        }
    }
}

// Charger la vue
require_once VIEWS_PATH . 'admin/cdm/edit.php';
?>
