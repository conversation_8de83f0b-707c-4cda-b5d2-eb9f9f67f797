<?php
// Point d'entrée principal de l'application
session_start();
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/functions.php';
require_once 'config/routes.php';
require_once 'includes/ExcelExporter.php';

// Routage simple
$page = isset($_GET['page']) ? $_GET['page'] : 'login';

// Vérification de l'authentification pour les pages protégées
$public_pages = ['login', 'install', 'setup'];

if (!in_array($page, $public_pages) && !isset($_SESSION['user_id'])) {
    header('Location: index.php?page=login');
    exit;
}

// Chargement de la page demandée
if (isset($routes[$page])) {
    require_once $routes[$page];
} else {
    require_once 'views/404.php';
}
?>
