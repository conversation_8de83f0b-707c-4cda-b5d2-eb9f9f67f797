<?php
// Contrôleur pour la gestion de la base de données

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Titre de la page
$page_title = 'Gestion de la base de données';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Informations sur la base de données
$db_info = [];

// Taille de la base de données
$sql = "SELECT
            table_schema AS 'database',
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'size_mb'
        FROM information_schema.tables
        WHERE table_schema = ?
        GROUP BY table_schema";
$result = $db->single($sql, [DB_NAME]);
$db_info['size'] = $result['size_mb'] ?? 0;

// Nombre de tables
$sql = "SELECT COUNT(*) as total FROM information_schema.tables WHERE table_schema = ?";
$result = $db->single($sql, [DB_NAME]);
$db_info['tables'] = $result['total'] ?? 0;

// Liste des tables avec leur taille et nombre d'enregistrements
$sql = "SELECT
            t.table_name AS 'table',
            ROUND((t.data_length + t.index_length) / 1024 / 1024, 2) AS 'size_mb',
            t.table_rows AS 'rows',
            t.create_time AS 'created',
            t.update_time AS 'updated'
        FROM information_schema.tables t
        WHERE t.table_schema = ?
        ORDER BY t.table_name ASC";
$tables = $db->all($sql, [DB_NAME]);

// Traitement des actions
$message = '';
$message_type = '';

if (isset($_POST['action'])) {
    $action = $_POST['action'];

    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        $message = 'Erreur de sécurité. Veuillez réessayer.';
        $message_type = 'danger';
    } else {
        switch ($action) {
            case 'optimize':
                // Optimiser les tables
                $tables_to_optimize = isset($_POST['tables']) ? $_POST['tables'] : [];

                if (empty($tables_to_optimize)) {
                    $message = 'Veuillez sélectionner au moins une table à optimiser.';
                    $message_type = 'warning';
                } else {
                    $optimized = 0;

                    foreach ($tables_to_optimize as $table) {
                        $sql = "OPTIMIZE TABLE " . $table;
                        $db->query($sql);
                        $optimized++;
                    }

                    $message = $optimized . ' table(s) optimisée(s) avec succès.';
                    $message_type = 'success';
                }
                break;

            case 'backup':
                // Créer une sauvegarde de la base de données
                $backup_dir = ROOT_PATH . 'backups/';

                // Créer le répertoire de sauvegarde s'il n'existe pas
                if (!file_exists($backup_dir)) {
                    mkdir($backup_dir, 0755, true);
                }

                // Nom du fichier de sauvegarde
                $backup_file = $backup_dir . 'backup_' . date('Y-m-d_H-i-s') . '.sql';

                // Commande pour créer la sauvegarde
                $command = sprintf(
                    'mysqldump --user=%s --password=%s --host=%s %s > %s',
                    DB_USER,
                    DB_PASS,
                    DB_HOST,
                    DB_NAME,
                    $backup_file
                );

                // Exécuter la commande
                $output = [];
                $return_var = 0;
                exec($command, $output, $return_var);

                if ($return_var === 0) {
                    $message = 'Sauvegarde créée avec succès : ' . basename($backup_file);
                    $message_type = 'success';
                } else {
                    $message = 'Erreur lors de la création de la sauvegarde.';
                    $message_type = 'danger';
                }
                break;

            default:
                $message = 'Action non reconnue.';
                $message_type = 'danger';
                break;
        }
    }
}

// Liste des sauvegardes existantes
$backups = [];
$backup_dir = ROOT_PATH . 'backups/';

// Créer le répertoire de sauvegarde s'il n'existe pas
if (!file_exists($backup_dir)) {
    mkdir($backup_dir, 0755, true);
}

if (is_dir($backup_dir)) {
    $files = scandir($backup_dir);

    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..' && pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $backup_path = $backup_dir . $file;
            $backups[] = [
                'name' => $file,
                'size' => round(filesize($backup_path) / 1024 / 1024, 2),
                'date' => date('Y-m-d H:i:s', filemtime($backup_path))
            ];
        }
    }

    // Trier les sauvegardes par date (la plus récente en premier)
    usort($backups, function($a, $b) {
        return strtotime($b['date']) - strtotime($a['date']);
    });
}

// Charger la vue
require_once VIEWS_PATH . 'admin/database/index.php';
?>
