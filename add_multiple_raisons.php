<?php
// Script pour ajouter la prise en charge de plusieurs raisons de retour par dossier
require_once 'config/config.php';
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    
    // Vérifier si la table dossier_raisons existe déjà
    $sql = "SHOW TABLES LIKE 'dossier_raisons'";
    $result = $db->query($sql);
    
    if ($result->rowCount() === 0) {
        // Créer la table dossier_raisons pour stocker les relations many-to-many
        $sql = "CREATE TABLE dossier_raisons (
            id INT AUTO_INCREMENT PRIMARY KEY,
            dossier_id INT NOT NULL,
            raison_id INT NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_dossier_raison (dossier_id, raison_id),
            FOREIGN KEY (dossier_id) REFERENCES dossiers_retournes(id) ON DELETE CASCADE,
            <PERSON>OR<PERSON><PERSON><PERSON> KEY (raison_id) REFERENCES raisons_retour(id) ON DELETE CASCADE
        )";
        $db->query($sql);
        echo "<p>La table 'dossier_raisons' a été créée avec succès.</p>";
        
        // Migrer les données existantes
        $sql = "INSERT INTO dossier_raisons (dossier_id, raison_id, created_at)
                SELECT id, raison_retour_id, created_at FROM dossiers_retournes 
                WHERE raison_retour_id IS NOT NULL AND raison_retour_id > 0";
        $db->query($sql);
        echo "<p>Les données existantes ont été migrées avec succès.</p>";
    } else {
        echo "<p>La table 'dossier_raisons' existe déjà.</p>";
    }
    
    // Vérifier la structure de la table
    $sql = "DESCRIBE dossier_raisons";
    $columns = $db->all($sql);
    
    echo "<h3>Structure de la table dossier_raisons</h3>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage();
}
?>
