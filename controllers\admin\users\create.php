<?php
// Contrôleur pour la création d'un nouvel utilisateur

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Titre de la page
$page_title = 'Nouvel utilisateur';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=admin/users/create', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupération des données du formulaire
    $username = clean($_POST['username'] ?? '');
    $email = clean($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $role = clean($_POST['role'] ?? '');
    $active = isset($_POST['active']) ? 1 : 0;

    // Validation des données
    $errors = [];

    if (empty($username)) {
        $errors[] = 'Le nom d\'utilisateur est obligatoire.';
    } elseif (strlen($username) < 3) {
        $errors[] = 'Le nom d\'utilisateur doit contenir au moins 3 caractères.';
    }

    if (empty($email)) {
        $errors[] = 'L\'adresse e-mail est obligatoire.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'L\'adresse e-mail n\'est pas valide.';
    }

    if (empty($password)) {
        $errors[] = 'Le mot de passe est obligatoire.';
    } elseif (strlen($password) < 6) {
        $errors[] = 'Le mot de passe doit contenir au moins 6 caractères.';
    }

    if ($password !== $confirm_password) {
        $errors[] = 'Les mots de passe ne correspondent pas.';
    }

    if (empty($role) || !in_array($role, ['admin', 'user'])) {
        $errors[] = 'Le rôle sélectionné n\'est pas valide.';
    }

    // Vérifier si le nom d'utilisateur existe déjà
    $sql = "SELECT COUNT(*) as count FROM users WHERE username = ?";
    $result = $db->single($sql, [$username]);

    if ($result['count'] > 0) {
        $errors[] = 'Ce nom d\'utilisateur est déjà utilisé.';
    }

    // Vérifier si l'email existe déjà
    $sql = "SELECT COUNT(*) as count FROM users WHERE email = ?";
    $result = $db->single($sql, [$email]);

    if ($result['count'] > 0) {
        $errors[] = 'Cette adresse e-mail est déjà utilisée.';
    }

    // Si aucune erreur, créer l'utilisateur
    if (empty($errors)) {
        // Hacher le mot de passe
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Insérer l'utilisateur dans la base de données
        $sql = "INSERT INTO users (username, email, password, role, active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, NOW(), NOW())";
        $params = [$username, $email, $hashed_password, $role, $active];

        if ($db->query($sql, $params)) {
            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Création d\'un utilisateur', 'Utilisateur: ' . $username);

            redirect('index.php?page=admin/users', 'Utilisateur créé avec succès.', 'success');
        } else {
            $errors[] = 'Erreur lors de la création de l\'utilisateur.';
        }
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = [
    'username' => $_POST['username'] ?? '',
    'email' => $_POST['email'] ?? '',
    'role' => $_POST['role'] ?? 'user',
    'active' => isset($_POST['active']) ? 1 : 0
];

// Charger la vue
require_once VIEWS_PATH . 'admin/users/create.php';
?>
