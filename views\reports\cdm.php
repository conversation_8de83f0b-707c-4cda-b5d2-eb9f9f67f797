<?php
// Vue pour le rapport d'analyse des CDM
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-hospital"></i> Analyse par CDM</h1>
        <div>
            <button id="printPage" class="btn btn-outline-secondary">
                <i class="fas fa-print"></i> Imprimer
            </button>
            <a href="index.php?page=reports/export&type=cdm&periode=<?php echo $periode; ?>&cdm_id=<?php echo $cdm_id; ?>&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Exporter
            </a>
        </div>
    </div>
    
    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter"></i> Filtres</h5>
        </div>
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="reports/cdm">
                
                <div class="col-md-3">
                    <label for="periode" class="form-label">Période prédéfinie</label>
                    <select class="form-select" id="periode" name="periode">
                        <option value="7" <?php echo $periode === '7' ? 'selected' : ''; ?>>7 derniers jours</option>
                        <option value="30" <?php echo $periode === '30' ? 'selected' : ''; ?>>30 derniers jours</option>
                        <option value="90" <?php echo $periode === '90' ? 'selected' : ''; ?>>90 derniers jours</option>
                        <option value="365" <?php echo $periode === '365' ? 'selected' : ''; ?>>365 derniers jours</option>
                        <option value="custom" <?php echo (!empty($date_debut) && !empty($date_fin)) ? 'selected' : ''; ?>>Personnalisée</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="cdm_id" class="form-label">CDM</label>
                    <select class="form-select" id="cdm_id" name="cdm_id">
                        <option value="0">Tous les CDM</option>
                        <?php foreach ($cdm_list as $cdm): ?>
                            <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm_id == $cdm['id'] ? 'selected' : ''; ?>>
                                <?php echo $cdm['nom']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-3 date-range" style="<?php echo (!empty($date_debut) && !empty($date_fin)) ? '' : 'display: none;'; ?>">
                    <label for="date_debut" class="form-label">Date début</label>
                    <input type="text" class="form-control datepicker" id="date_debut" name="date_debut" value="<?php echo $date_debut; ?>" placeholder="jj/mm/aaaa">
                </div>
                
                <div class="col-md-3 date-range" style="<?php echo (!empty($date_debut) && !empty($date_fin)) ? '' : 'display: none;'; ?>">
                    <label for="date_fin" class="form-label">Date fin</label>
                    <input type="text" class="form-control datepicker" id="date_fin" name="date_fin" value="<?php echo $date_fin; ?>" placeholder="jj/mm/aaaa">
                </div>
                
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Appliquer les filtres
                    </button>
                    <a href="index.php?page=reports/cdm" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Période analysée -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle"></i> Période analysée: <strong>
            <?php 
                echo formatDate($date_debut_formatted) . ' au ' . formatDate($date_fin_formatted);
                if ($cdm_id > 0) {
                    foreach ($cdm_list as $cdm) {
                        if ($cdm['id'] == $cdm_id) {
                            echo ' - CDM: ' . $cdm['nom'];
                            break;
                        }
                    }
                }
            ?>
        </strong>
    </div>
    
    <!-- Activité par CDM -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Activité par CDM</h5>
        </div>
        <div class="card-body">
            <?php if (count($cdm_activity) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>CDM</th>
                                <th class="text-end">Lots</th>
                                <th class="text-end">Dossiers</th>
                                <th class="text-end">Reçus</th>
                                <th class="text-end">En attente</th>
                                <th>Taux de réception</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cdm_activity as $cdm): ?>
                                <tr>
                                    <td><?php echo $cdm['nom']; ?></td>
                                    <td class="text-end"><?php echo $cdm['nb_lots']; ?></td>
                                    <td class="text-end"><?php echo $cdm['nb_dossiers']; ?></td>
                                    <td class="text-end"><?php echo $cdm['nb_recus']; ?></td>
                                    <td class="text-end"><?php echo $cdm['nb_en_attente']; ?></td>
                                    <td>
                                        <?php 
                                            $taux_reception = ($cdm['nb_dossiers'] > 0) ? 
                                                            ($cdm['nb_recus'] / $cdm['nb_dossiers']) * 100 : 0;
                                        ?>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" role="progressbar" 
                                                 style="width: <?php echo $taux_reception; ?>%;" 
                                                 aria-valuenow="<?php echo $taux_reception; ?>" 
                                                 aria-valuemin="0" aria-valuemax="100">
                                                <?php echo round($taux_reception); ?>%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Graphique d'activité par CDM -->
                <div class="mt-4">
                    <h5>Graphique d'activité par CDM</h5>
                    <canvas id="cdmActivityChart" height="300"></canvas>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune donnée disponible pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Dossiers retournés par CDM -->
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-undo-alt"></i> Dossiers retournés par CDM</h5>
        </div>
        <div class="card-body">
            <?php if (count($cdm_retours) > 0 && array_sum(array_column($cdm_retours, 'nb_retours')) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>CDM</th>
                                <th class="text-end">Retours</th>
                                <th class="text-end">Corrigés</th>
                                <th class="text-end">Non corrigés</th>
                                <th>Taux de correction</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cdm_retours as $cdm): ?>
                                <?php if ($cdm['nb_retours'] > 0): ?>
                                    <tr>
                                        <td><?php echo $cdm['nom']; ?></td>
                                        <td class="text-end"><?php echo $cdm['nb_retours']; ?></td>
                                        <td class="text-end"><?php echo $cdm['nb_corriges']; ?></td>
                                        <td class="text-end"><?php echo $cdm['nb_non_corriges']; ?></td>
                                        <td>
                                            <?php 
                                                $taux_correction = ($cdm['nb_retours'] > 0) ? 
                                                                ($cdm['nb_corriges'] / $cdm['nb_retours']) * 100 : 0;
                                            ?>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-success" role="progressbar" 
                                                     style="width: <?php echo $taux_correction; ?>%;" 
                                                     aria-valuenow="<?php echo $taux_correction; ?>" 
                                                     aria-valuemin="0" aria-valuemax="100">
                                                    <?php echo round($taux_correction); ?>%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Graphique des retours par CDM -->
                <div class="mt-4">
                    <h5>Graphique des retours par CDM</h5>
                    <canvas id="cdmRetoursChart" height="300"></canvas>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun dossier retourné pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Raisons de retour par CDM -->
    <div class="card mb-4">
        <div class="card-header bg-warning">
            <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Raisons de retour par CDM</h5>
        </div>
        <div class="card-body">
            <?php if (count($raisons_by_cdm) > 0): ?>
                <div class="accordion" id="accordionRaisons">
                    <?php foreach ($raisons_by_cdm as $cdm_id => $cdm_data): ?>
                        <?php if (count($cdm_data['raisons']) > 0): ?>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading<?php echo $cdm_id; ?>">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $cdm_id; ?>" aria-expanded="false" aria-controls="collapse<?php echo $cdm_id; ?>">
                                        <?php echo $cdm_data['nom']; ?> - <?php echo count($cdm_data['raisons']); ?> raison(s) de retour
                                    </button>
                                </h2>
                                <div id="collapse<?php echo $cdm_id; ?>" class="accordion-collapse collapse" aria-labelledby="heading<?php echo $cdm_id; ?>" data-bs-parent="#accordionRaisons">
                                    <div class="accordion-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Raison</th>
                                                        <th class="text-end">Nombre</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($cdm_data['raisons'] as $raison): ?>
                                                        <tr>
                                                            <td><?php echo $raison['raison']; ?></td>
                                                            <td class="text-end"><?php echo $raison['nb_retours']; ?></td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune donnée disponible pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Évolution mensuelle des dossiers par CDM -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-chart-line"></i> Évolution mensuelle des dossiers par CDM</h5>
        </div>
        <div class="card-body">
            <?php if (count($evolution_by_cdm) > 0 && count($all_months) > 0): ?>
                <!-- Graphique d'évolution mensuelle -->
                <canvas id="evolutionChart" height="300"></canvas>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune donnée disponible pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Afficher/masquer les champs de date personnalisée
    document.getElementById('periode').addEventListener('change', function() {
        var dateRangeFields = document.querySelectorAll('.date-range');
        if (this.value === 'custom') {
            dateRangeFields.forEach(function(field) {
                field.style.display = '';
            });
        } else {
            dateRangeFields.forEach(function(field) {
                field.style.display = 'none';
            });
        }
    });
    
    // Graphique d'activité par CDM
    <?php if (count($cdm_activity) > 0): ?>
        var cdmActivityCtx = document.getElementById('cdmActivityChart').getContext('2d');
        var cdmActivityChart = new Chart(cdmActivityCtx, {
            type: 'bar',
            data: {
                labels: [<?php echo "'" . implode("', '", array_column($cdm_activity, 'nom')) . "'"; ?>],
                datasets: [{
                    label: 'Dossiers',
                    data: [<?php echo implode(', ', array_column($cdm_activity, 'nb_dossiers')); ?>],
                    backgroundColor: 'rgba(13, 110, 253, 0.7)',
                    borderColor: 'rgba(13, 110, 253, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    <?php endif; ?>
    
    // Graphique des retours par CDM
    <?php if (count($cdm_retours) > 0 && array_sum(array_column($cdm_retours, 'nb_retours')) > 0): ?>
        var cdmRetoursCtx = document.getElementById('cdmRetoursChart').getContext('2d');
        var cdmRetoursChart = new Chart(cdmRetoursCtx, {
            type: 'bar',
            data: {
                labels: [<?php 
                    $cdm_names = [];
                    foreach ($cdm_retours as $cdm) {
                        if ($cdm['nb_retours'] > 0) {
                            $cdm_names[] = $cdm['nom'];
                        }
                    }
                    echo "'" . implode("', '", $cdm_names) . "'"; 
                ?>],
                datasets: [{
                    label: 'Retours',
                    data: [<?php 
                        $retours_data = [];
                        foreach ($cdm_retours as $cdm) {
                            if ($cdm['nb_retours'] > 0) {
                                $retours_data[] = $cdm['nb_retours'];
                            }
                        }
                        echo implode(', ', $retours_data); 
                    ?>],
                    backgroundColor: 'rgba(220, 53, 69, 0.7)',
                    borderColor: 'rgba(220, 53, 69, 1)',
                    borderWidth: 1
                }, {
                    label: 'Corrigés',
                    data: [<?php 
                        $corriges_data = [];
                        foreach ($cdm_retours as $cdm) {
                            if ($cdm['nb_retours'] > 0) {
                                $corriges_data[] = $cdm['nb_corriges'];
                            }
                        }
                        echo implode(', ', $corriges_data); 
                    ?>],
                    backgroundColor: 'rgba(25, 135, 84, 0.7)',
                    borderColor: 'rgba(25, 135, 84, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    <?php endif; ?>
    
    // Graphique d'évolution mensuelle
    <?php if (count($evolution_by_cdm) > 0 && count($all_months) > 0): ?>
        var evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
        var evolutionChart = new Chart(evolutionCtx, {
            type: 'line',
            data: {
                labels: [<?php echo "'" . implode("', '", array_values($formatted_months)) . "'"; ?>],
                datasets: [
                    <?php 
                        $colors = [
                            'rgba(13, 110, 253, 1)', // blue
                            'rgba(25, 135, 84, 1)',  // green
                            'rgba(220, 53, 69, 1)',  // red
                            'rgba(255, 193, 7, 1)',  // yellow
                            'rgba(13, 202, 240, 1)', // cyan
                            'rgba(108, 117, 125, 1)', // gray
                            'rgba(111, 66, 193, 1)',  // purple
                            'rgba(214, 51, 132, 1)'   // pink
                        ];
                        
                        $datasets = [];
                        $color_index = 0;
                        
                        foreach ($evolution_by_cdm as $cdm_id => $cdm_data) {
                            $color = $colors[$color_index % count($colors)];
                            $data_values = [];
                            
                            foreach ($all_months as $month) {
                                $data_values[] = $cdm_data['data'][$month];
                            }
                            
                            $datasets[] = "{
                                label: '{$cdm_data['nom']}',
                                data: [" . implode(', ', $data_values) . "],
                                backgroundColor: '{$color}',
                                borderColor: '{$color}',
                                borderWidth: 2,
                                fill: false,
                                tension: 0.1
                            }";
                            
                            $color_index++;
                        }
                        
                        echo implode(', ', $datasets);
                    ?>
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    <?php endif; ?>
});
</script>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
