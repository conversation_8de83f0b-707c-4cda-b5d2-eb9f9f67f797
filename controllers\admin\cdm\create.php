<?php
// Contrôleur pour la création d'un CDM

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Titre de la page
$page_title = 'Ajouter un CDM';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'])) {
        redirect('index.php?page=admin/cdm', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }
    
    // Récupération des données du formulaire
    $nom = clean($_POST['nom']);
    $adresse = clean($_POST['adresse']);
    $contact_nom = clean($_POST['contact_nom']);
    $telephone = clean($_POST['telephone']);
    $email = clean($_POST['email']);
    $active = isset($_POST['active']) ? 1 : 0;
    
    // Validation des données
    $errors = [];
    
    if (empty($nom)) {
        $errors[] = 'Le nom du CDM est obligatoire.';
    }
    
    if (empty($adresse)) {
        $errors[] = 'L\'adresse du CDM est obligatoire.';
    }
    
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'L\'adresse email n\'est pas valide.';
    }
    
    // Vérifier si le CDM existe déjà
    $sql = "SELECT id FROM cdm WHERE nom = ?";
    $result = $db->single($sql, [$nom]);
    
    if ($result) {
        $errors[] = 'Un CDM avec ce nom existe déjà.';
    }
    
    // Si aucune erreur, insérer le CDM
    if (empty($errors)) {
        $sql = "INSERT INTO cdm (nom, adresse, contact_nom, telephone, email, active, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())";
        $params = [$nom, $adresse, $contact_nom, $telephone, $email, $active];
        
        if ($db->query($sql, $params)) {
            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Création d\'un CDM', 'Nom: ' . $nom);
            
            redirect('index.php?page=admin/cdm', 'CDM ajouté avec succès.', 'success');
        } else {
            $errors[] = 'Erreur lors de l\'ajout du CDM.';
        }
    }
}

// Charger la vue
require_once VIEWS_PATH . 'admin/cdm/create.php';
?>
