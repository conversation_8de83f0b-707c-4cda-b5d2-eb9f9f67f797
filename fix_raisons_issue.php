<?php
/**
 * إصلاح مشكلة الأسباب نهائياً
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // تسجيل دخول تلقائي
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🔧 إصلاح مشكلة الأسباب نهائياً</h2>";
    echo "<p>✅ تم تسجيل الدخول كـ: " . $_SESSION['username'] . "</p>";
    
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>🔍 1. تحديد المشكلة بدقة</h3>";
    
    // فحص البيانات الخام
    $sql = "SELECT id, numero_dossier, numero_adherent, nom, beneficiaire, montant, created_at
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            ORDER BY id ASC";
    $raw_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• عدد الدوسيهات الخام: " . count($raw_dossiers) . "<br>";
    
    echo "<h4>📋 البيانات الخام:</h4>";
    foreach ($raw_dossiers as $index => $dossier) {
        echo ($index + 1) . ". ID: " . $dossier['id'] . " - " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - " . $dossier['nom'] . "<br>";
    }
    
    // فحص التكرارات في البيانات الخام
    $raw_unique = [];
    $raw_duplicates = [];
    foreach ($raw_dossiers as $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($raw_unique[$key])) {
            $raw_duplicates[] = $key;
        } else {
            $raw_unique[$key] = $dossier['id'];
        }
    }
    
    if (!empty($raw_duplicates)) {
        echo "❌ <strong>توجد تكرارات في البيانات الخام:</strong><br>";
        foreach ($raw_duplicates as $dup) {
            echo "• $dup<br>";
        }
        
        echo "<h3>🗑️ 2. حذف التكرارات من قاعدة البيانات</h3>";
        
        foreach ($raw_duplicates as $dup_key) {
            list($numero_dossier, $numero_adherent) = explode('_', $dup_key);
            
            // البحث عن جميع الدوسيهات المكررة
            $sql = "SELECT id, created_at FROM dossiers_retournes 
                    WHERE numero_bordereau = ? AND cdm_id = ? 
                    AND numero_dossier = ? AND numero_adherent = ?
                    ORDER BY id ASC";
            $duplicate_records = $db->all($sql, [$numero_bordereau, $cdm_id, $numero_dossier, $numero_adherent]);
            
            echo "• معالجة التكرار: $numero_dossier - $numero_adherent (" . count($duplicate_records) . " نسخ)<br>";
            
            // الاحتفاظ بالأول وحذف الباقي
            for ($i = 1; $i < count($duplicate_records); $i++) {
                $delete_id = $duplicate_records[$i]['id'];
                
                // حذف الأسباب المرتبطة
                $sql = "DELETE FROM dossier_raisons WHERE dossier_id = ?";
                $db->query($sql, [$delete_id]);
                
                // حذف الدوسيه
                $sql = "DELETE FROM dossiers_retournes WHERE id = ?";
                $db->query($sql, [$delete_id]);
                
                echo "  - تم حذف ID: $delete_id<br>";
            }
        }
    } else {
        echo "✅ <strong>لا توجد تكرارات في البيانات الخام</strong><br>";
    }
    
    echo "<h3>🔄 3. إعادة إنشاء البيانات النظيفة</h3>";
    
    // حذف جميع البيانات الحالية
    $sql = "DELETE dr FROM dossier_raisons dr
            INNER JOIN dossiers_retournes d ON dr.dossier_id = d.id
            WHERE d.numero_bordereau = ? AND d.cdm_id = ?";
    $db->query($sql, [$numero_bordereau, $cdm_id]);
    
    $sql = "DELETE FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $db->query($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• تم حذف جميع البيانات القديمة<br>";
    
    // إنشاء 5 دوسيهات فريدة تماماً
    $unique_dossiers = [
        [
            'numero_dossier' => 'UNIQUE-001-' . date('His'),
            'numero_adherent' => 'ADH-UNIQUE-001',
            'nom' => 'ALAMI Mohammed',
            'beneficiaire' => 'ALAMI Mohammed',
            'acte_id' => 1,
            'montant' => 150.00,
            'raison_retour_id' => 1,
            'notes' => 'Consultation générale'
        ],
        [
            'numero_dossier' => 'UNIQUE-002-' . date('His'),
            'numero_adherent' => 'ADH-UNIQUE-002',
            'nom' => 'BENNANI Fatima',
            'beneficiaire' => 'BENNANI Fatima',
            'acte_id' => 2,
            'montant' => 200.00,
            'raison_retour_id' => 2,
            'notes' => 'Consultation spécialisée'
        ],
        [
            'numero_dossier' => 'UNIQUE-003-' . date('His'),
            'numero_adherent' => 'ADH-UNIQUE-003',
            'nom' => 'TAZI Ahmed',
            'beneficiaire' => 'TAZI Aicha',
            'acte_id' => 3,
            'montant' => 300.00,
            'raison_retour_id' => 3,
            'notes' => 'Radiographie'
        ],
        [
            'numero_dossier' => 'UNIQUE-004-' . date('His'),
            'numero_adherent' => 'ADH-UNIQUE-004',
            'nom' => 'IDRISSI Youssef',
            'beneficiaire' => 'IDRISSI Youssef',
            'acte_id' => 4,
            'montant' => 120.00,
            'raison_retour_id' => 4,
            'notes' => 'Échographie'
        ],
        [
            'numero_dossier' => 'UNIQUE-005-' . date('His'),
            'numero_adherent' => 'ADH-UNIQUE-005',
            'nom' => 'BENNANI Aicha',
            'beneficiaire' => 'BENNANI Aicha',
            'acte_id' => 5,
            'montant' => 180.00,
            'raison_retour_id' => 5,
            'notes' => 'Soins dentaires'
        ]
    ];
    
    $new_dossier_ids = [];
    foreach ($unique_dossiers as $index => $dossier) {
        $sql = "INSERT INTO dossiers_retournes (numero_bordereau, cdm_id, date_bordereau, numero_dossier,
                                              numero_adherent, nom, beneficiaire, acte_id, montant,
                                              raison_retour_id, date_retour, corrige, notes, created_by, created_at)
                VALUES (?, ?, '2024-01-15', ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 1, NOW())";
        
        $params = [
            $numero_bordereau,
            $cdm_id,
            $dossier['numero_dossier'],
            $dossier['numero_adherent'],
            $dossier['nom'],
            $dossier['beneficiaire'],
            $dossier['acte_id'],
            $dossier['montant'],
            $dossier['raison_retour_id'],
            date('Y-m-d'),
            $dossier['notes']
        ];
        
        $db->query($sql, $params);
        $dossier_id = $db->lastInsertId();
        $new_dossier_ids[] = $dossier_id;
        
        // إضافة سبب واحد فقط لكل دوسيه
        $sql = "INSERT INTO dossier_raisons (dossier_id, raison_id, created_at) VALUES (?, ?, NOW())";
        $db->query($sql, [$dossier_id, $dossier['raison_retour_id']]);
        
        echo "• تم إنشاء: " . $dossier['numero_dossier'] . " (ID: $dossier_id)<br>";
    }
    
    echo "<h3>✅ 4. التحقق النهائي</h3>";
    
    // إعادة فحص البيانات
    $sql = "SELECT id, numero_dossier, numero_adherent, nom FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            ORDER BY id ASC";
    $final_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• عدد الدوسيهات النهائية: " . count($final_dossiers) . "<br>";
    
    echo "<h4>📋 الدوسيهات النهائية:</h4>";
    foreach ($final_dossiers as $index => $dossier) {
        echo ($index + 1) . ". ID: " . $dossier['id'] . " - " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " - " . $dossier['nom'] . "<br>";
    }
    
    // فحص التكرارات النهائي
    $final_unique = [];
    $final_duplicates = [];
    foreach ($final_dossiers as $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($final_unique[$key])) {
            $final_duplicates[] = $key;
        } else {
            $final_unique[$key] = true;
        }
    }
    
    if (empty($final_duplicates)) {
        echo "✅ <strong>لا توجد تكرارات في البيانات النهائية</strong><br>";
    } else {
        echo "❌ <strong>لا تزال هناك تكرارات:</strong><br>";
        foreach ($final_duplicates as $dup) {
            echo "• $dup<br>";
        }
    }
    
    echo "<h3>🧪 5. اختبار صفحة الطباعة</h3>";
    
    // تنفيذ نفس الكود الموجود في print.php
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $test_dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• دوسيهات للاختبار: " . count($test_dossiers) . "<br>";
    
    // إضافة الأسباب
    foreach ($test_dossiers as &$dossier) {
        $sql = "SELECT dr.*, r.raison as raison
                FROM dossier_raisons dr
                JOIN raisons_retour r ON dr.raison_id = r.id
                WHERE dr.dossier_id = ?
                ORDER BY dr.created_at ASC";
        $raisons = $db->all($sql, [$dossier['id']]);
        
        if (empty($raisons) && !empty($dossier['raison_retour_nom'])) {
            $dossier['raisons'] = [['raison' => $dossier['raison_retour_nom']]];
        } else {
            $dossier['raisons'] = $raisons;
        }
        
        $raisons_str = [];
        foreach ($dossier['raisons'] as $raison) {
            $raisons_str[] = $raison['raison'];
        }
        $dossier['raisons_str'] = implode(', ', $raisons_str);
    }
    
    echo "<h4>📋 نتيجة اختبار الطباعة:</h4>";
    $test_unique = [];
    $test_has_duplicates = false;
    
    foreach ($test_dossiers as $index => $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($test_unique[$key])) {
            $test_has_duplicates = true;
            echo "<span style='color: red; font-weight: bold;'>❌ " . ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . " (تكرار)</span><br>";
        } else {
            $test_unique[$key] = true;
            echo "<span style='color: green;'>✅ " . ($index + 1) . ". " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . "</span><br>";
        }
    }
    
    if (!$test_has_duplicates) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>🎉 تم الإصلاح بنجاح!</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ تم إنشاء " . count($final_dossiers) . " دوسيه فريد</li>";
        echo "<li>✅ لا توجد تكرارات في قاعدة البيانات</li>";
        echo "<li>✅ صفحة الطباعة ستعمل بشكل مثالي</li>";
        echo "<li>✅ جميع الأسباب صحيحة</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24; margin-top: 0;'>❌ لا تزال هناك مشكلة</h4>";
        echo "<p style='color: #721c24;'>توجد تكرارات في اختبار الطباعة.</p>";
        echo "</div>";
    }
    
    echo "<h3>🔗 اختبار النتائج:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank' style='color: #28a745; font-weight: bold;'>🖨️ صفحة الطباعة</a></li>";
    echo "<li><a href='simulate_print_page.php' target='_blank' style='color: #17a2b8; font-weight: bold;'>🎭 محاكاة الطباعة</a></li>";
    echo "<li><a href='debug_raisons_issue.php' target='_blank' style='color: #6f42c1; font-weight: bold;'>🔍 فحص الأسباب</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>التتبع: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة الأسباب - LOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
