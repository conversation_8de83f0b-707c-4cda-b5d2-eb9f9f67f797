<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Impression des résultats de recherche</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }

        .header-title {
            font-size: 18pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
        }

        .subtitle {
            font-size: 14pt;
            text-align: center;
            margin-bottom: 5px;
        }

        .print-date {
            font-size: 10pt;
            text-align: center;
            margin-bottom: 20px;
            color: #666;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table, th, td {
            border: 1px solid #ddd;
        }

        th, td {
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }

        .badge {
            padding: 5px;
            border-radius: 4px;
            display: inline-block;
        }

        .bg-success {
            background-color: #28a745;
            color: white;
        }

        .bg-warning {
            background-color: #ffc107;
            color: black;
        }

        .bg-info {
            background-color: #17a2b8;
            color: white;
        }

        .bg-secondary {
            background-color: #6c757d;
            color: white;
        }

        .no-print {
            display: none;
        }

        .filters-info {
            margin-bottom: 20px;
            font-size: 11pt;
        }

        .filters-info strong {
            font-weight: bold;
        }

        @page {
            size: landscape;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Boutons d'action (non imprimables) -->
        <div class="d-flex justify-content-between align-items-center mb-4 no-print">
            <h1><i class="fas fa-print"></i> Impression des résultats de recherche</h1>
            <div>
                <button onclick="window.print();" class="btn btn-primary">
                    <i class="fas fa-print"></i> Imprimer
                </button>
                <a href="index.php?page=lots/search&search_type=<?php echo $search_type; ?>&search_term=<?php echo urlencode($search_term); ?><?php echo !empty($cdm_id) ? '&cdm_id=' . $cdm_id : ''; ?><?php echo !empty($date_debut) ? '&date_debut=' . $date_debut : ''; ?><?php echo !empty($date_fin) ? '&date_fin=' . $date_fin : ''; ?><?php echo !empty($statut) ? '&statut=' . $statut : ''; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour
                </a>
            </div>
        </div>

        <!-- En-tête du document -->
        <div class="text-center mb-2">
            <div class="header-title">Mutuelle Générale de l'Education National</div>
            <div class="subtitle">Gestion des Lots CDM</div>
            <div class="subtitle">Résultats de recherche</div>
            <div class="print-date">Imprimé le <?php echo date(DATETIME_FORMAT); ?></div>
        </div>

        <!-- Informations sur les filtres -->
        <div class="filters-info">
            <p>
                <strong>Type de recherche:</strong>
                <?php
                    echo $search_type === 'dossier' ? 'Dossier' :
                        ($search_type === 'lot' ? 'Lot' : 'Adhérent');
                ?>
                <?php if (!empty($search_term)): ?>&nbsp;&nbsp;|&nbsp;&nbsp;<?php endif; ?>
                <?php if (!empty($search_term)): ?>
                <strong>Terme de recherche:</strong> <?php echo $search_term; ?>
                <?php endif; ?>
                <?php if (!empty($cdm_id)): ?>
                    &nbsp;&nbsp;|&nbsp;&nbsp;
                    <strong>CDM:</strong> <?php echo $cdm_nom; ?>
                <?php endif; ?>
                <?php if (!empty($date_debut)): ?>
                    &nbsp;&nbsp;|&nbsp;&nbsp;
                    <strong>Date début:</strong> <?php echo $date_debut; ?>
                <?php endif; ?>
                <?php if (!empty($date_fin)): ?>
                    &nbsp;&nbsp;|&nbsp;&nbsp;
                    <strong>Date fin:</strong> <?php echo $date_fin; ?>
                <?php endif; ?>
                <?php if (!empty($statut)): ?>
                    &nbsp;&nbsp;|&nbsp;&nbsp;
                    <strong>Statut:</strong>
                    <?php
                        echo $statut === 'recu' ? 'Reçu' :
                            ($statut === 'non_recu' ? 'Non reçu' : $statut);
                    ?>
                <?php endif; ?>
            </p>
            <p><strong>Total des résultats:</strong> <?php echo $total_results; ?></p>
        </div>

        <!-- Résultats de recherche -->
        <?php if ($total_results > 0): ?>
            <?php if ($search_type === 'dossier' || $search_type === 'adherent'): ?>
                <!-- Résultats de recherche pour les dossiers -->
                <table>
                    <thead>
                        <tr>
                            <th>N° Lot</th>
                            <th>CDM</th>
                            <th>N° Dossier</th>
                            <th>N° Adhérent</th>
                            <th>Nom</th>
                            <th>Bénéficiaire</th>
                            <th>Acte</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($results as $dossier): ?>
                            <tr>
                                <td><?php echo $dossier['numero_lot']; ?></td>
                                <td><?php echo $dossier['cdm_nom']; ?></td>
                                <td><?php echo $dossier['numero_dossier']; ?></td>
                                <td><?php echo $dossier['numero_adherent']; ?></td>
                                <td><?php echo $dossier['nom']; ?></td>
                                <td><?php echo $dossier['beneficiaire']; ?></td>
                                <td><?php echo $dossier['acte_nom']; ?></td>
                                <td>
                                    <span class="badge <?php echo $dossier['recu'] ? 'bg-success' : 'bg-warning'; ?>">
                                        <?php echo $dossier['recu'] ? 'Reçu' : 'Non reçu'; ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php elseif ($search_type === 'lot'): ?>
                <!-- Résultats de recherche pour les lots -->
                <table>
                    <thead>
                        <tr>
                            <th>N° Lot</th>
                            <th>CDM</th>
                            <th>Date demande</th>
                            <th>Date réception</th>
                            <th>Dossiers</th>
                            <th>Reçus</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($results as $lot): ?>
                            <tr>
                                <td><?php echo $lot['numero_lot']; ?></td>
                                <td><?php echo $lot['cdm_nom']; ?></td>
                                <td><?php echo formatDate($lot['date_demande']); ?></td>
                                <td><?php echo formatDate($lot['date_reception']); ?></td>
                                <td><?php echo $lot['nb_dossiers']; ?></td>
                                <td><?php echo $lot['nb_recus']; ?></td>
                                <td>
                                    <span class="badge <?php
                                        echo $lot['statut'] === 'en_attente' ? 'bg-warning' :
                                            ($lot['statut'] === 'recu' ? 'bg-info' :
                                            ($lot['statut'] === 'traite' ? 'bg-success' : 'bg-secondary'));
                                    ?>">
                                        <?php
                                            echo $lot['statut'] === 'en_attente' ? 'En attente' :
                                                ($lot['statut'] === 'recu' ? 'Reçu' :
                                                ($lot['statut'] === 'traite' ? 'Traité' : 'Archivé'));
                                        ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>

            <?php if ($total_results >= 500): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> Les résultats sont limités aux 500 premiers enregistrements. Veuillez affiner votre recherche pour des résultats plus précis.
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Aucun résultat ne correspond à votre recherche. Veuillez essayer avec d'autres termes ou critères.
            </div>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Auto-print -->
    <script>
        // Imprimer automatiquement après 1 seconde
        setTimeout(function() {
            window.print();
        }, 1000);
    </script>
</body>
</html>
