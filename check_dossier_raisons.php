<?php
/**
 * فحص جدول dossier_raisons لتحديد مصدر التكرار
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // Créer la session admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🔍 فحص جدول dossier_raisons</h2>";
    echo "<p>✅ Session admin créée</p>";
    
    // Paramètres du test
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>📊 1. فحص الدوسيهات الأساسية</h3>";
    
    $sql = "SELECT id, numero_dossier, numero_adherent, nom FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            ORDER BY id ASC";
    $dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "<h4>📋 الدوسيهات الأساسية:</h4>";
    foreach ($dossiers as $index => $dossier) {
        echo ($index + 1) . ". ID: " . $dossier['id'] . " - " . $dossier['numero_dossier'] . " - " . $dossier['numero_adherent'] . "<br>";
    }
    
    echo "<h3>📊 2. فحص جدول dossier_raisons</h3>";
    
    $sql = "SELECT dr.*, r.raison 
            FROM dossier_raisons dr
            JOIN raisons_retour r ON dr.raison_id = r.id
            JOIN dossiers_retournes d ON dr.dossier_id = d.id
            WHERE d.numero_bordereau = ? AND d.cdm_id = ?
            ORDER BY dr.dossier_id ASC";
    $all_raisons = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "<h4>📋 جميع الأسباب في dossier_raisons:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='padding: 5px;'>ID</th>";
    echo "<th style='padding: 5px;'>Dossier ID</th>";
    echo "<th style='padding: 5px;'>Raison ID</th>";
    echo "<th style='padding: 5px;'>Raison</th>";
    echo "<th style='padding: 5px;'>Créé le</th>";
    echo "</tr>";
    
    $dossier_raisons_count = [];
    foreach ($all_raisons as $raison) {
        $dossier_id = $raison['dossier_id'];
        if (!isset($dossier_raisons_count[$dossier_id])) {
            $dossier_raisons_count[$dossier_id] = 0;
        }
        $dossier_raisons_count[$dossier_id]++;
        
        $bg_color = $dossier_raisons_count[$dossier_id] > 1 ? '#ffebee' : '#ffffff';
        
        echo "<tr style='background-color: $bg_color;'>";
        echo "<td style='padding: 5px;'>" . $raison['id'] . "</td>";
        echo "<td style='padding: 5px;'>" . $raison['dossier_id'] . "</td>";
        echo "<td style='padding: 5px;'>" . $raison['raison_id'] . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($raison['raison']) . "</td>";
        echo "<td style='padding: 5px;'>" . $raison['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>📊 3. تحليل التكرارات</h3>";
    
    foreach ($dossiers as $dossier) {
        $dossier_id = $dossier['id'];
        
        $sql = "SELECT COUNT(*) as count FROM dossier_raisons WHERE dossier_id = ?";
        $raison_count = $db->single($sql, [$dossier_id]);
        
        echo "• دوسيه " . $dossier['numero_dossier'] . " (ID: $dossier_id): " . $raison_count['count'] . " أسباب<br>";
        
        if ($raison_count['count'] > 1) {
            echo "  ⚠️ <strong>أسباب متعددة لنفس الدوسيه</strong><br>";
            
            $sql = "SELECT dr.*, r.raison 
                    FROM dossier_raisons dr
                    JOIN raisons_retour r ON dr.raison_id = r.id
                    WHERE dr.dossier_id = ?";
            $raisons_detail = $db->all($sql, [$dossier_id]);
            
            foreach ($raisons_detail as $rd) {
                echo "    - " . $rd['raison'] . " (ID: " . $rd['id'] . ")<br>";
            }
        }
    }
    
    echo "<h3>🔧 4. إصلاح المشكلة</h3>";
    
    // حذف الأسباب المكررة - الاحتفاظ بسبب واحد فقط لكل دوسيه
    foreach ($dossiers as $dossier) {
        $dossier_id = $dossier['id'];
        
        $sql = "SELECT id FROM dossier_raisons WHERE dossier_id = ? ORDER BY id ASC";
        $raisons_ids = $db->all($sql, [$dossier_id]);
        
        if (count($raisons_ids) > 1) {
            echo "• إصلاح دوسيه " . $dossier['numero_dossier'] . ":<br>";
            
            // الاحتفاظ بالسبب الأول وحذف الباقي
            for ($i = 1; $i < count($raisons_ids); $i++) {
                $raison_id_to_delete = $raisons_ids[$i]['id'];
                $sql = "DELETE FROM dossier_raisons WHERE id = ?";
                $db->query($sql, [$raison_id_to_delete]);
                echo "  ✅ تم حذف السبب ID: $raison_id_to_delete<br>";
            }
        }
    }
    
    echo "<h3>✅ 5. التحقق النهائي</h3>";
    
    // إعادة فحص الأسباب
    $sql = "SELECT dr.*, r.raison 
            FROM dossier_raisons dr
            JOIN raisons_retour r ON dr.raison_id = r.id
            JOIN dossiers_retournes d ON dr.dossier_id = d.id
            WHERE d.numero_bordereau = ? AND d.cdm_id = ?
            ORDER BY dr.dossier_id ASC";
    $final_raisons = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "<h4>📋 الأسباب النهائية:</h4>";
    $final_count_per_dossier = [];
    foreach ($final_raisons as $raison) {
        $dossier_id = $raison['dossier_id'];
        if (!isset($final_count_per_dossier[$dossier_id])) {
            $final_count_per_dossier[$dossier_id] = 0;
        }
        $final_count_per_dossier[$dossier_id]++;
    }
    
    $all_unique = true;
    foreach ($final_count_per_dossier as $dossier_id => $count) {
        if ($count > 1) {
            $all_unique = false;
            echo "⚠️ دوسيه ID $dossier_id لا يزال لديه $count أسباب<br>";
        }
    }
    
    if ($all_unique) {
        echo "✅ <strong>جميع الدوسيهات لديها سبب واحد فقط</strong><br>";
        echo "• <strong>العدد الإجمالي للأسباب:</strong> " . count($final_raisons) . "<br>";
        echo "• <strong>العدد الإجمالي للدوسيهات:</strong> " . count($dossiers) . "<br>";
    }
    
    echo "<h3>🔗 اختبار النتائج:</h3>";
    echo "<ul>";
    echo "<li><a href='final_verification.php' target='_blank'>🔍 التحقق النهائي</a></li>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank'>🖨️ صفحة الطباعة</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank'>📝 صفحة التحرير</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>التتبع: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص dossier_raisons - LOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        table {
            background: white;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            direction: ltr;
        }
    </style>
</head>
<body>
</body>
</html>
