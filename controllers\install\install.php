<?php
// Contrôleur pour l'installation de l'application

// Vérifier si l'application est déjà installée
try {
    $db = Database::getInstance();
    if ($db->databaseExists()) {
        redirect('index.php?page=login', 'L\'application est déjà installée.', 'info');
    }
} catch (Exception $e) {
    // Continuer avec l'installation
}

// Traitement du formulaire d'installation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=install', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }
    
    // Récupérer et nettoyer les données du formulaire
    $db_host = clean($_POST['db_host'] ?? 'localhost');
    $db_user = clean($_POST['db_user'] ?? 'root');
    $db_pass = $_POST['db_pass'] ?? '';
    $db_name = clean($_POST['db_name'] ?? 'lot2');
    
    $admin_username = clean($_POST['admin_username'] ?? DEFAULT_ADMIN_USERNAME);
    $admin_password = $_POST['admin_password'] ?? DEFAULT_ADMIN_PASSWORD;
    $admin_email = clean($_POST['admin_email'] ?? '');
    
    // Valider les données
    $errors = [];
    
    if (empty($db_host)) $errors[] = 'Le nom d\'hôte de la base de données est requis.';
    if (empty($db_user)) $errors[] = 'Le nom d\'utilisateur de la base de données est requis.';
    if (empty($db_name)) $errors[] = 'Le nom de la base de données est requis.';
    
    if (empty($admin_username)) $errors[] = 'Le nom d\'utilisateur administrateur est requis.';
    if (empty($admin_password)) $errors[] = 'Le mot de passe administrateur est requis.';
    if (empty($admin_email)) $errors[] = 'L\'email administrateur est requis.';
    if (!filter_var($admin_email, FILTER_VALIDATE_EMAIL)) $errors[] = 'L\'email administrateur n\'est pas valide.';
    
    // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
    if (!empty($errors)) {
        $_SESSION['install_errors'] = $errors;
        $_SESSION['install_data'] = [
            'db_host' => $db_host,
            'db_user' => $db_user,
            'db_name' => $db_name,
            'admin_username' => $admin_username,
            'admin_email' => $admin_email
        ];
        redirect('index.php?page=install', 'Veuillez corriger les erreurs.', 'danger');
    }
    
    // Tester la connexion à la base de données
    try {
        $conn = new PDO("mysql:host=$db_host", $db_user, $db_pass);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Créer la base de données si elle n'existe pas
        $conn->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $conn->exec("USE `$db_name`");
        
        // Créer les tables
        $sql_file = file_get_contents(BASE_PATH . 'sql/install.sql');
        $conn->exec($sql_file);
        
        // Créer l'utilisateur administrateur
        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO users (username, password, email, role, active, created_at) VALUES (?, ?, ?, 'admin', 1, NOW())");
        $stmt->execute([$admin_username, $hashed_password, $admin_email]);
        
        // Mettre à jour le fichier de configuration
        $config_file = BASE_PATH . 'config/config.php';
        $config_content = file_get_contents($config_file);
        
        $config_content = preg_replace("/define\('DB_HOST', '.*?'\);/", "define('DB_HOST', '$db_host');", $config_content);
        $config_content = preg_replace("/define\('DB_USER', '.*?'\);/", "define('DB_USER', '$db_user');", $config_content);
        $config_content = preg_replace("/define\('DB_PASS', '.*?'\);/", "define('DB_PASS', '$db_pass');", $config_content);
        $config_content = preg_replace("/define\('DB_NAME', '.*?'\);/", "define('DB_NAME', '$db_name');", $config_content);
        
        file_put_contents($config_file, $config_content);
        
        // Rediriger vers la page de connexion
        redirect('index.php?page=login', 'Installation réussie ! Vous pouvez maintenant vous connecter.', 'success');
        
    } catch (PDOException $e) {
        redirect('index.php?page=install', 'Erreur lors de l\'installation: ' . $e->getMessage(), 'danger');
    }
}

// Afficher le formulaire d'installation
$install_data = $_SESSION['install_data'] ?? [
    'db_host' => 'localhost',
    'db_user' => 'root',
    'db_name' => 'lot2',
    'admin_username' => DEFAULT_ADMIN_USERNAME,
    'admin_email' => ''
];

$install_errors = $_SESSION['install_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['install_data']);
unset($_SESSION['install_errors']);

// Charger la vue
require_once VIEWS_PATH . 'install/install.php';
?>
