<?php
// Vue pour la liste des bordereaux de dossiers retournés
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-undo-alt"></i> Liste des bordereaux de retour</h1>
        <div>
            <a href="index.php?page=retours/create" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouveau bordereau
            </a>
            <button id="printPage" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-print"></i> Imprimer
            </button>
            <div class="btn-group ms-2">
                <a href="index.php?page=retours/export&format=csv<?php echo !empty($cdm_id) ? '&cdm_id=' . $cdm_id : ''; ?><?php echo !empty($corrige) ? '&corrige=' . $corrige : ''; ?><?php echo !empty($date_debut) ? '&date_debut=' . $date_debut : ''; ?><?php echo !empty($date_fin) ? '&date_fin=' . $date_fin : ''; ?><?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-success">
                    <i class="fas fa-file-excel"></i> Exporter Excel
                </a>
                <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="visually-hidden">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="index.php?page=retours/export&format=csv<?php echo !empty($cdm_id) ? '&cdm_id=' . $cdm_id : ''; ?><?php echo !empty($corrige) ? '&corrige=' . $corrige : ''; ?><?php echo !empty($date_debut) ? '&date_debut=' . $date_debut : ''; ?><?php echo !empty($date_fin) ? '&date_fin=' . $date_fin : ''; ?><?php echo !empty($search) ? '&search=' . $search : ''; ?>">Format CSV</a></li>
                    <li><a class="dropdown-item" href="index.php?page=retours/export&format=xlsx<?php echo !empty($cdm_id) ? '&cdm_id=' . $cdm_id : ''; ?><?php echo !empty($corrige) ? '&corrige=' . $corrige : ''; ?><?php echo !empty($date_debut) ? '&date_debut=' . $date_debut : ''; ?><?php echo !empty($date_fin) ? '&date_fin=' . $date_fin : ''; ?><?php echo !empty($search) ? '&search=' . $search : ''; ?>">Format XLSX</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter"></i> Filtres</h5>
        </div>
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="retours">

                <div class="col-md-3">
                    <label for="cdm_id" class="form-label">CDM</label>
                    <select class="form-select" id="cdm_id" name="cdm_id">
                        <option value="0">Tous les CDM</option>
                        <?php foreach ($cdm_list as $cdm): ?>
                            <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm_id == $cdm['id'] ? 'selected' : ''; ?>>
                                <?php echo $cdm['nom']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="corrige" class="form-label">Statut</label>
                    <select class="form-select" id="corrige" name="corrige">
                        <option value="">Tous les statuts</option>
                        <option value="1" <?php echo $corrige === '1' ? 'selected' : ''; ?>>Corrigés</option>
                        <option value="0" <?php echo $corrige === '0' ? 'selected' : ''; ?>>Non corrigés</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="date_debut" class="form-label">Date début</label>
                    <input type="text" class="form-control datepicker" id="date_debut" name="date_debut" value="<?php echo $date_debut; ?>" placeholder="jj/mm/aaaa">
                </div>

                <div class="col-md-2">
                    <label for="date_fin" class="form-label">Date fin</label>
                    <input type="text" class="form-control datepicker" id="date_fin" name="date_fin" value="<?php echo $date_fin; ?>" placeholder="jj/mm/aaaa">
                </div>

                <div class="col-md-2">
                    <label for="search" class="form-label">Recherche</label>
                    <input type="text" class="form-control" id="search" name="search" value="<?php echo $search; ?>" placeholder="N° bordereau, CDM...">
                </div>

                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrer
                    </button>
                    <a href="index.php?page=retours" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des bordereaux -->
    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list"></i> Résultats</h5>
                <span class="badge bg-primary"><?php echo $total_items; ?> bordereau(x) trouvé(s)</span>
            </div>
        </div>
        <div class="card-body">
            <?php if (count($bordereaux) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th>N° Bordereau</th>
                                <th>CDM</th>
                                <th>Date bordereau</th>
                                <th>Dossiers</th>
                                <th>Corrigés</th>
                                <th>Progression</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($bordereaux as $bordereau): ?>
                                <tr>
                                    <td><?php echo $bordereau['numero_bordereau']; ?></td>
                                    <td><?php echo $bordereau['cdm_nom']; ?></td>
                                    <td><?php echo formatDate($bordereau['date_bordereau']); ?></td>
                                    <td><?php echo $bordereau['nb_dossiers']; ?></td>
                                    <td><?php echo $bordereau['nb_corriges']; ?></td>
                                    <td>
                                        <?php
                                            $progress = ($bordereau['nb_dossiers'] > 0) ?
                                                        ($bordereau['nb_corriges'] / $bordereau['nb_dossiers']) * 100 : 0;
                                        ?>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                 style="width: <?php echo $progress; ?>%;"
                                                 aria-valuenow="<?php echo $progress; ?>"
                                                 aria-valuemin="0" aria-valuemax="100">
                                                <?php echo round($progress); ?>%
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-end">
                                        <a href="index.php?page=retours/view&numero=<?php echo $bordereau['numero_bordereau']; ?>&cdm_id=<?php echo $bordereau['cdm_id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="index.php?page=retours/edit&numero=<?php echo $bordereau['numero_bordereau']; ?>&cdm_id=<?php echo $bordereau['cdm_id']; ?>" class="btn btn-sm btn-warning btn-action" data-bs-toggle="tooltip" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="index.php?page=retours/print&numero=<?php echo $bordereau['numero_bordereau']; ?>&cdm_id=<?php echo $bordereau['cdm_id']; ?>" class="btn btn-sm btn-info btn-action" data-bs-toggle="tooltip" title="Imprimer">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Pagination des bordereaux">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?php echo $page_num <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="index.php?page=retours&page_num=<?php echo $page_num - 1; ?>&cdm_id=<?php echo $cdm_id; ?>&corrige=<?php echo $corrige; ?>&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>&search=<?php echo $search; ?>">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>

                            <?php for ($i = max(1, $page_num - 2); $i <= min($total_pages, $page_num + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page_num ? 'active' : ''; ?>">
                                    <a class="page-link" href="index.php?page=retours&page_num=<?php echo $i; ?>&cdm_id=<?php echo $cdm_id; ?>&corrige=<?php echo $corrige; ?>&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>&search=<?php echo $search; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <li class="page-item <?php echo $page_num >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="index.php?page=retours&page_num=<?php echo $page_num + 1; ?>&cdm_id=<?php echo $cdm_id; ?>&corrige=<?php echo $corrige; ?>&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>&search=<?php echo $search; ?>">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>

            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun bordereau ne correspond aux critères de recherche.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
