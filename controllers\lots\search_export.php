<?php
// Contrôleur pour l'exportation des résultats de recherche vers Excel

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de recherche
$search_type = isset($_GET['search_type']) ? clean($_GET['search_type']) : 'dossier';
$search_term = isset($_GET['search_term']) ? clean($_GET['search_term']) : '';
$cdm_id = isset($_GET['cdm_id']) ? intval($_GET['cdm_id']) : 0;
$date_debut = isset($_GET['date_debut']) ? clean($_GET['date_debut']) : '';
$date_fin = isset($_GET['date_fin']) ? clean($_GET['date_fin']) : '';
$statut = isset($_GET['statut']) ? clean($_GET['statut']) : '';

// Résultats de recherche
$results = [];
$total_results = 0;

// Effectuer la recherche si au moins un critère est fourni
if (!empty($search_term) || $cdm_id > 0 || !empty($date_debut) || !empty($date_fin) || !empty($statut)) {
    // Recherche de dossiers
    if ($search_type === 'dossier' || $search_type === 'adherent') {
        $sql = "SELECT d.*, l.numero_lot, c.nom as cdm_nom, a.nom as acte_nom
                FROM dossiers d
                JOIN lots l ON d.lot_id = l.id
                JOIN cdm c ON l.cdm_id = c.id
                LEFT JOIN actes a ON d.acte_id = a.id
                WHERE 1=1";

        $params = [];

        // Ajouter le critère de recherche par terme si fourni
        if (!empty($search_term)) {
            if ($search_type === 'dossier') {
                $sql .= " AND (d.numero_dossier LIKE ? OR d.nom LIKE ? OR d.beneficiaire LIKE ?)";
                $params = ["%$search_term%", "%$search_term%", "%$search_term%"];
            } else { // adherent
                $sql .= " AND d.numero_adherent = ?";
                $params = [$search_term];
            }
        }

        // Filtres supplémentaires
        if ($cdm_id > 0) {
            $sql .= " AND l.cdm_id = ?";
            $params[] = $cdm_id;
        }

        if (!empty($date_debut)) {
            $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
            $sql .= " AND l.date_demande >= ?";
            $params[] = $date_debut_formatted;
        }

        if (!empty($date_fin)) {
            $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
            $sql .= " AND l.date_demande <= ?";
            $params[] = $date_fin_formatted;
        }

        if (!empty($statut)) {
            if ($statut === 'recu') {
                $sql .= " AND d.recu = 1";
            } elseif ($statut === 'non_recu') {
                $sql .= " AND d.recu = 0";
            }
        }

        $sql .= " ORDER BY d.created_at DESC";

        $results = $db->all($sql, $params);
        $total_results = count($results);

        // Préparer les données pour l'exportation
        $headers = [
            'N° Lot',
            'CDM',
            'N° Dossier',
            'N° Adhérent',
            'Nom',
            'Bénéficiaire',
            'Acte',
            'Montant (DH)',
            'Statut',
            'Date de création'
        ];

        $data = [];
        foreach ($results as $dossier) {
            $data[] = [
                $dossier['numero_lot'],
                $dossier['cdm_nom'],
                $dossier['numero_dossier'],
                $dossier['numero_adherent'],
                $dossier['nom'],
                $dossier['beneficiaire'],
                $dossier['acte_nom'],
                !empty($dossier['montant']) ? number_format($dossier['montant'], 2, ',', ' ') : '',
                $dossier['recu'] ? 'Reçu' : 'Non reçu',
                formatDate($dossier['created_at'], DATETIME_FORMAT)
            ];
        }
    }
    // Recherche de lots
    elseif ($search_type === 'lot') {
        $sql = "SELECT l.*, c.nom as cdm_nom,
                       (SELECT COUNT(*) FROM dossiers WHERE lot_id = l.id) as nb_dossiers,
                       (SELECT COUNT(*) FROM dossiers WHERE lot_id = l.id AND recu = 1) as nb_recus
                FROM lots l
                JOIN cdm c ON l.cdm_id = c.id
                WHERE 1=1";

        $params = [];

        // Ajouter le critère de recherche par terme si fourni
        if (!empty($search_term)) {
            $sql .= " AND l.numero_lot LIKE ?";
            $params[] = "%$search_term%";
        }

        // Filtres supplémentaires
        if ($cdm_id > 0) {
            $sql .= " AND l.cdm_id = ?";
            $params[] = $cdm_id;
        }

        if (!empty($date_debut)) {
            $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
            $sql .= " AND l.date_demande >= ?";
            $params[] = $date_debut_formatted;
        }

        if (!empty($date_fin)) {
            $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
            $sql .= " AND l.date_demande <= ?";
            $params[] = $date_fin_formatted;
        }

        if (!empty($statut)) {
            $sql .= " AND l.statut = ?";
            $params[] = $statut;
        }

        $sql .= " ORDER BY l.created_at DESC";

        $results = $db->all($sql, $params);
        $total_results = count($results);

        // Préparer les données pour l'exportation
        $headers = [
            'N° Lot',
            'CDM',
            'Date demande',
            'Date réception',
            'Nombre de dossiers',
            'Dossiers reçus',
            'Statut',
            'Date de création'
        ];

        $data = [];
        foreach ($results as $lot) {
            $statut_text = '';
            switch ($lot['statut']) {
                case 'en_attente':
                    $statut_text = 'En attente';
                    break;
                case 'recu':
                    $statut_text = 'Reçu';
                    break;
                case 'traite':
                    $statut_text = 'Traité';
                    break;
                case 'archive':
                    $statut_text = 'Archivé';
                    break;
            }

            $data[] = [
                $lot['numero_lot'],
                $lot['cdm_nom'],
                formatDate($lot['date_demande']),
                formatDate($lot['date_reception']),
                $lot['nb_dossiers'],
                $lot['nb_recus'],
                $statut_text,
                formatDate($lot['created_at'], DATETIME_FORMAT)
            ];
        }
    }
}

// Nom du fichier
$filename = 'Recherche_' . $search_type . '_' . date('Ymd');

// Format d'exportation (csv par défaut)
$format = isset($_GET['format']) && $_GET['format'] === 'xlsx' ? 'xlsx' : 'csv';

// Journaliser l'action
logActivity($_SESSION['user_id'], 'Exportation des résultats de recherche', "Type: $search_type, Format: $format");

// Exporter les données
ExcelExporter::export($data, $headers, $filename, $format);
?>
