<?php
// Contrôleur pour le tableau de bord d'analyse financière
// Accessible uniquement aux administrateurs

// Vérifier si l'utilisateur est connecté
if (!isAuthenticated()) {
    redirect('index.php?page=login', 'Veuillez vous connecter pour accéder à cette page.', 'warning');
}

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Titre de la page
$page_title = 'Tableau de bord - Analyse financière';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de filtrage
$date_debut = isset($_GET['date_debut']) ? clean($_GET['date_debut']) : date('Y-m-01');
$date_fin = isset($_GET['date_fin']) ? clean($_GET['date_fin']) : date('Y-m-d');
$cdm_id = isset($_GET['cdm_id']) ? intval($_GET['cdm_id']) : 0;
$acte_id = isset($_GET['acte_id']) ? intval($_GET['acte_id']) : 0;

// Formater les dates pour les requêtes SQL
$date_debut_formatted = date('Y-m-d', strtotime($date_debut));
$date_fin_formatted = date('Y-m-d', strtotime($date_fin));

// Obtenir la liste des CDM pour le filtre
$cdm_list = getCDMList();

// Obtenir la liste des actes pour le filtre
try {
    $check_column = "SHOW COLUMNS FROM actes LIKE 'active'";
    $column_exists = $db->query($check_column)->rowCount() > 0;
    
    if ($column_exists) {
        $actes_list = $db->all("SELECT id, nom, prix FROM actes WHERE active = 1 ORDER BY nom");
    } else {
        $actes_list = $db->all("SELECT id, nom, prix FROM actes ORDER BY nom");
    }
} catch (Exception $e) {
    // En cas d'erreur, récupérer tous les actes sans condition
    $actes_list = $db->all("SELECT id, nom, prix FROM actes ORDER BY nom");
}

// 1. Rapports d'analyse financière mensuels
// Montant total par mois
$sql_monthly = "
    SELECT 
        DATE_FORMAT(l.date_demande, '%Y-%m') AS mois,
        COUNT(DISTINCT d.id) AS nb_dossiers,
        SUM(d.montant) AS montant_total
    FROM 
        dossiers d
    JOIN 
        lots l ON d.lot_id = l.id
    WHERE 
        l.date_demande BETWEEN ? AND ?
";

$params_monthly = [$date_debut_formatted, $date_fin_formatted];

if ($cdm_id > 0) {
    $sql_monthly .= " AND l.cdm_id = ?";
    $params_monthly[] = $cdm_id;
}

if ($acte_id > 0) {
    $sql_monthly .= " AND d.acte_id = ?";
    $params_monthly[] = $acte_id;
}

$sql_monthly .= " GROUP BY mois ORDER BY mois";

$monthly_data = $db->all($sql_monthly, $params_monthly);

// 2. Résumé de l'analyse financière
// Par acte
$sql_by_acte = "
    SELECT 
        a.id AS acte_id,
        a.nom AS acte_nom,
        COUNT(d.id) AS nb_dossiers,
        SUM(d.montant) AS montant_total
    FROM 
        actes a
    LEFT JOIN 
        dossiers d ON a.id = d.acte_id
    LEFT JOIN 
        lots l ON d.lot_id = l.id
    WHERE 
        l.date_demande BETWEEN ? AND ?
";

$params_by_acte = [$date_debut_formatted, $date_fin_formatted];

if ($cdm_id > 0) {
    $sql_by_acte .= " AND l.cdm_id = ?";
    $params_by_acte[] = $cdm_id;
}

$sql_by_acte .= " GROUP BY a.id, a.nom ORDER BY montant_total DESC";

$actes_data = $db->all($sql_by_acte, $params_by_acte);

// Par CDM
$sql_by_cdm = "
    SELECT 
        c.id AS cdm_id,
        c.nom AS cdm_nom,
        COUNT(d.id) AS nb_dossiers,
        SUM(d.montant) AS montant_total
    FROM 
        cdm c
    LEFT JOIN 
        lots l ON c.id = l.cdm_id
    LEFT JOIN 
        dossiers d ON l.id = d.lot_id
    WHERE 
        l.date_demande BETWEEN ? AND ?
";

$params_by_cdm = [$date_debut_formatted, $date_fin_formatted];

if ($acte_id > 0) {
    $sql_by_cdm .= " AND d.acte_id = ?";
    $params_by_cdm[] = $acte_id;
}

$sql_by_cdm .= " GROUP BY c.id, c.nom ORDER BY montant_total DESC";

$cdm_data = $db->all($sql_by_cdm, $params_by_cdm);

// Montant total global
$sql_total = "
    SELECT 
        COUNT(d.id) AS nb_dossiers_total,
        SUM(d.montant) AS montant_total_global
    FROM 
        dossiers d
    JOIN 
        lots l ON d.lot_id = l.id
    WHERE 
        l.date_demande BETWEEN ? AND ?
";

$params_total = [$date_debut_formatted, $date_fin_formatted];

if ($cdm_id > 0) {
    $sql_total .= " AND l.cdm_id = ?";
    $params_total[] = $cdm_id;
}

if ($acte_id > 0) {
    $sql_total .= " AND d.acte_id = ?";
    $params_total[] = $acte_id;
}

$total_data = $db->single($sql_total, $params_total);

// Charger la vue
require_once VIEWS_PATH . 'dashboard/finance.php';
?>
