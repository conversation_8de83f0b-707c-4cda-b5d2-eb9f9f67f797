<?php
// Contrôleur pour la création d'un nouveau bordereau de dossiers retournés

// Titre de la page
$page_title = 'Nouveau bordereau de retour';
$page = 'retours/create';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=retours/create', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupérer et nettoyer les données du formulaire
    $cdm_id = isset($_POST['cdm_id']) ? intval($_POST['cdm_id']) : 0;
    $date_bordereau = isset($_POST['date_bordereau']) ? clean($_POST['date_bordereau']) : '';
    $notes = isset($_POST['notes']) ? clean($_POST['notes']) : '';

    // Valider les données
    $errors = [];

    if ($cdm_id <= 0) {
        $errors[] = 'Veuillez sélectionner un CDM.';
    }

    if (empty($date_bordereau)) {
        $errors[] = 'La date du bordereau est requise.';
    } else {
        // Convertir la date au format MySQL (YYYY-MM-DD)
        $date_bordereau = date('Y-m-d', strtotime(str_replace('/', '-', $date_bordereau)));
    }

    // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = $_POST;
        redirect('index.php?page=retours/create', 'Veuillez corriger les erreurs.', 'danger');
    }

    try {
        // Générer un numéro de bordereau unique
        $numero_bordereau = generateBordereauNumber();

        // Rediriger vers la page d'édition du bordereau
        redirect('index.php?page=retours/edit&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id . '&date_bordereau=' . $date_bordereau . '&notes=' . urlencode($notes) . '&new=1', 'Veuillez ajouter des dossiers au bordereau.', 'info');

    } catch (Exception $e) {
        redirect('index.php?page=retours/create', 'Erreur lors de la création du bordereau: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'cdm_id' => '',
    'date_bordereau' => date('d/m/Y'),
    'notes' => ''
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Obtenir la liste des CDM
$cdm_list = getCDMList();

// Charger la vue
require_once VIEWS_PATH . 'retours/create.php';
?>
