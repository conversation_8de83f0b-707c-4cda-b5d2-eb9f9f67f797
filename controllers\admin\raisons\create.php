<?php
// Contrôleur pour la création d'une nouvelle raison de retour

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Titre de la page
$page_title = 'Nouvelle raison de retour';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=admin/raisons/create', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupération des données du formulaire
    $raison = clean($_POST['raison'] ?? '');
    $description = clean($_POST['description'] ?? '');
    $active = isset($_POST['active']) ? 1 : 0;

    // Validation des données
    $errors = [];

    if (empty($raison)) {
        $errors[] = 'Le libellé de la raison est obligatoire.';
    }

    // Vérifier si la raison existe déjà
    $sql = "SELECT COUNT(*) as count FROM raisons_retour WHERE raison = ?";
    $result = $db->single($sql, [$raison]);

    if ($result['count'] > 0) {
        $errors[] = 'Une raison avec ce libellé existe déjà.';
    }

    // Vérifier si la colonne 'active' existe
    try {
        $check_column = "SHOW COLUMNS FROM raisons_retour LIKE 'active'";
        $column_exists = $db->query($check_column)->rowCount() > 0;
    } catch (Exception $e) {
        $column_exists = false;
    }

    // Si aucune erreur, créer la raison
    if (empty($errors)) {
        if ($column_exists) {
            $sql = "INSERT INTO raisons_retour (raison, description, active, created_at, updated_at)
                    VALUES (?, ?, ?, NOW(), NOW())";
            $params = [$raison, $description, $active];
        } else {
            $sql = "INSERT INTO raisons_retour (raison, description, created_at, updated_at)
                    VALUES (?, ?, NOW(), NOW())";
            $params = [$raison, $description];
        }

        if ($db->query($sql, $params)) {
            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Création d\'un motif de retour', 'Motif: ' . $raison);

            redirect('index.php?page=admin/raisons', 'Motif créé avec succès.', 'success');
        } else {
            $errors[] = 'Erreur lors de la création du motif.';
        }
    }
}

// Récupérer les données du formulaire en cas d'erreur
$default_data = [
    'raison' => '',
    'description' => '',
    'active' => 1
];

$form_data = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $form_data = [
        'raison' => $_POST['raison'] ?? '',
        'description' => $_POST['description'] ?? '',
        'active' => isset($_POST['active']) ? 1 : 0
    ];
} else {
    $form_data = $default_data;
}

// Charger la vue
require_once VIEWS_PATH . 'admin/raisons/create.php';
?>
