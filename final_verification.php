<?php
/**
 * التحقق النهائي من أن جميع المشاكل تم حلها
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // Créer la session admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h1>🎯 التحقق النهائي من النظام</h1>";
    echo "<p>✅ Session admin créée</p>";
    
    // Paramètres du test
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h2>📊 1. فحص قاعدة البيانات</h2>";
    
    // فحص الدوسيهات
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $count = $db->single($sql, [$numero_bordereau, $cdm_id]);
    echo "• عدد الدوسيهات: " . $count['count'] . "<br>";
    
    // فحص التكرارات
    $sql = "SELECT numero_dossier, numero_adherent, COUNT(*) as count
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            GROUP BY numero_dossier, numero_adherent
            HAVING COUNT(*) > 1";
    $duplicates = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    if (empty($duplicates)) {
        echo "✅ <strong>لا توجد تكرارات</strong><br>";
    } else {
        echo "❌ <strong>توجد تكرارات:</strong> " . count($duplicates) . "<br>";
    }
    
    echo "<h2>🖨️ 2. اختبار صفحة الطباعة</h2>";
    
    // تنفيذ نفس الكود الموجود في print.php
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• دوسيهات مُسترجعة من استعلام الطباعة: " . count($dossiers) . "<br>";
    
    // إضافة الأسباب المتعددة
    foreach ($dossiers as &$dossier) {
        $sql = "SELECT dr.*, r.raison as raison
                FROM dossier_raisons dr
                JOIN raisons_retour r ON dr.raison_id = r.id
                WHERE dr.dossier_id = ?
                ORDER BY dr.created_at ASC";
        $raisons = $db->all($sql, [$dossier['id']]);
        
        if (empty($raisons) && !empty($dossier['raison_retour_nom'])) {
            $dossier['raisons'] = [['raison' => $dossier['raison_retour_nom']]];
        } else {
            $dossier['raisons'] = $raisons;
        }
        
        $raisons_str = [];
        foreach ($dossier['raisons'] as $raison) {
            $raisons_str[] = $raison['raison'];
        }
        $dossier['raisons_str'] = implode(', ', $raisons_str);
    }
    
    echo "<h3>📋 الدوسيهات النهائية للطباعة:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>#</th>";
    echo "<th style='padding: 8px;'>N° Dossier</th>";
    echo "<th style='padding: 8px;'>N° Adhérent</th>";
    echo "<th style='padding: 8px;'>Nom</th>";
    echo "<th style='padding: 8px;'>Bénéficiaire</th>";
    echo "<th style='padding: 8px;'>Acte</th>";
    echo "<th style='padding: 8px;'>Montant</th>";
    echo "<th style='padding: 8px;'>Raisons</th>";
    echo "</tr>";
    
    $unique_check = [];
    $has_duplicates_in_display = false;
    
    foreach ($dossiers as $index => $dossier) {
        $key = $dossier['numero_dossier'] . '_' . $dossier['numero_adherent'];
        if (isset($unique_check[$key])) {
            $has_duplicates_in_display = true;
            $bg_color = '#ffebee';
        } else {
            $unique_check[$key] = true;
            $bg_color = '#ffffff';
        }
        
        echo "<tr style='background-color: $bg_color;'>";
        echo "<td style='padding: 8px; text-align: center;'>" . ($index + 1) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($dossier['numero_dossier']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($dossier['numero_adherent']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($dossier['nom']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($dossier['beneficiaire']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($dossier['acte_nom'] ?? 'N/A') . " (" . htmlspecialchars($dossier['acte_code'] ?? 'N/A') . ")</td>";
        echo "<td style='padding: 8px; text-align: right;'>" . number_format($dossier['montant'], 2, ',', ' ') . " DH</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($dossier['raisons_str']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (!$has_duplicates_in_display) {
        echo "✅ <strong>لا توجد تكرارات في العرض</strong><br>";
    } else {
        echo "❌ <strong>توجد تكرارات في العرض</strong><br>";
    }
    
    echo "<h2>📊 3. اختبار التصدير</h2>";
    
    // اختبار استعلام التصدير
    $sql = "SELECT dr.numero_dossier, dr.numero_adherent, dr.nom, dr.beneficiaire, 
                   a.nom as acte_nom, a.code as acte_code, dr.montant, 
                   r.raison as raison_retour, dr.date_retour, dr.notes,
                   CASE WHEN dr.corrige = 1 THEN 'Corrigé' ELSE 'En attente' END as statut
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $export_data = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "• دوسيهات للتصدير: " . count($export_data) . "<br>";
    
    $total_montant = 0;
    foreach ($export_data as $row) {
        $total_montant += $row['montant'];
    }
    echo "• المبلغ الإجمالي: " . number_format($total_montant, 2, ',', ' ') . " DH<br>";
    
    echo "<h2>📝 4. اختبار صفحة التحرير</h2>";
    
    // فحص إمكانية إضافة دوسيه جديد
    $test_dossier = [
        'numero_dossier' => 'TEST-NEW-' . date('His'),
        'numero_adherent' => 'ADH-TEST-NEW'
    ];
    
    // فحص التكرار
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ? 
            AND numero_dossier = ? AND numero_adherent = ?";
    $duplicate_check = $db->single($sql, [
        $numero_bordereau, 
        $cdm_id, 
        $test_dossier['numero_dossier'], 
        $test_dossier['numero_adherent']
    ]);
    
    if ($duplicate_check['count'] == 0) {
        echo "✅ <strong>فحص التكرار يعمل بشكل صحيح</strong><br>";
    } else {
        echo "❌ <strong>مشكلة في فحص التكرار</strong><br>";
    }
    
    echo "<h2>🎉 النتيجة النهائية</h2>";
    
    $all_tests_passed = true;
    $issues = [];
    
    if ($count['count'] != 5) {
        $all_tests_passed = false;
        $issues[] = "عدد الدوسيهات غير صحيح";
    }
    
    if (!empty($duplicates)) {
        $all_tests_passed = false;
        $issues[] = "توجد تكرارات في قاعدة البيانات";
    }
    
    if ($has_duplicates_in_display) {
        $all_tests_passed = false;
        $issues[] = "توجد تكرارات في العرض";
    }
    
    if (count($dossiers) != count($export_data)) {
        $all_tests_passed = false;
        $issues[] = "عدم تطابق بيانات الطباعة والتصدير";
    }
    
    if ($all_tests_passed) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ جميع الاختبارات نجحت!</h3>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ قاعدة البيانات نظيفة</li>";
        echo "<li>✅ لا توجد تكرارات</li>";
        echo "<li>✅ صفحة الطباعة تعمل</li>";
        echo "<li>✅ التصدير يعمل</li>";
        echo "<li>✅ صفحة التحرير محمية</li>";
        echo "</ul>";
        echo "<h4>🎯 النظام جاهز للاستخدام بالكامل!</h4>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ توجد مشاكل:</h3>";
        echo "<ul style='color: #721c24;'>";
        foreach ($issues as $issue) {
            echo "<li>❌ $issue</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<h3>🔗 الروابط النهائية:</h3>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
    echo "<ul>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank' style='color: #28a745; font-weight: bold;'>🖨️ صفحة الطباعة</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank' style='color: #17a2b8; font-weight: bold;'>📝 صفحة التحرير</a></li>";
    echo "<li><a href='index.php?page=retours/export&numero_bordereau=10001&cdm_id=1' target='_blank' style='color: #ffc107; font-weight: bold;'>📊 تصدير Excel</a></li>";
    echo "<li><a href='index.php?page=dashboard' target='_blank' style='color: #6f42c1; font-weight: bold;'>📈 لوحة التحكم</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "<p>التتبع: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحقق النهائي - LOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        h1, h2, h3 {
            color: #333;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        table {
            background: white;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            direction: ltr;
        }
    </style>
</head>
<body>
</body>
</html>
