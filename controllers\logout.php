<?php
// Contrôleur pour la déconnexion

// Vérifier si l'utilisateur est connecté
if (isAuthenticated()) {
    // Journaliser la déconnexion
    logActivity($_SESSION['user_id'], 'Déconnexion', 'Déconnexion réussie');
    
    // Supprimer le cookie "Se souvenir de moi" s'il existe
    if (isset($_COOKIE['remember_token'])) {
        $token = $_COOKIE['remember_token'];
        
        try {
            $db = Database::getInstance();
            $sql = "DELETE FROM user_tokens WHERE token = ?";
            $db->query($sql, [$token]);
        } catch (Exception $e) {
            // Ignorer les erreurs
        }
        
        setcookie('remember_token', '', time() - 3600, '/', '', false, true);
    }
    
    // Détruire la session
    session_unset();
    session_destroy();
}

// Rediriger vers la page de connexion
redirect('index.php?page=login', 'Vous avez été déconnecté avec succès.', 'success');
?>
