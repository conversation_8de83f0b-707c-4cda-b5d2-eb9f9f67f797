<?php
// Contrôleur pour la liste des lots

// Titre de la page
$page_title = 'Liste des lots';
$page = 'lots';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de pagination
$page_num = isset($_GET['page_num']) ? intval($_GET['page_num']) : 1;
$items_per_page = ITEMS_PER_PAGE;
$offset = ($page_num - 1) * $items_per_page;

// Filtres
$cdm_id = isset($_GET['cdm_id']) ? intval($_GET['cdm_id']) : 0;
$statut = isset($_GET['statut']) ? clean($_GET['statut']) : '';
$date_debut = isset($_GET['date_debut']) ? clean($_GET['date_debut']) : '';
$date_fin = isset($_GET['date_fin']) ? clean($_GET['date_fin']) : '';
$search = isset($_GET['search']) ? clean($_GET['search']) : '';

// Construction de la requête SQL
$sql_count = "SELECT COUNT(*) as total FROM lots l JOIN cdm c ON l.cdm_id = c.id WHERE 1=1";
$sql = "SELECT l.*, c.nom as cdm_nom, 
               (SELECT COUNT(*) FROM dossiers WHERE lot_id = l.id) as nb_dossiers,
               (SELECT COUNT(*) FROM dossiers WHERE lot_id = l.id AND recu = 1) as nb_recus
        FROM lots l
        JOIN cdm c ON l.cdm_id = c.id
        WHERE 1=1";

$params = [];

// Appliquer les filtres
if ($cdm_id > 0) {
    $sql .= " AND l.cdm_id = ?";
    $sql_count .= " AND l.cdm_id = ?";
    $params[] = $cdm_id;
}

if (!empty($statut)) {
    $sql .= " AND l.statut = ?";
    $sql_count .= " AND l.statut = ?";
    $params[] = $statut;
}

if (!empty($date_debut)) {
    $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
    $sql .= " AND l.date_demande >= ?";
    $sql_count .= " AND l.date_demande >= ?";
    $params[] = $date_debut_formatted;
}

if (!empty($date_fin)) {
    $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
    $sql .= " AND l.date_demande <= ?";
    $sql_count .= " AND l.date_demande <= ?";
    $params[] = $date_fin_formatted;
}

if (!empty($search)) {
    $sql .= " AND (l.numero_lot LIKE ? OR c.nom LIKE ?)";
    $sql_count .= " AND (l.numero_lot LIKE ? OR c.nom LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
}

// Obtenir le nombre total de lots
$result = $db->single($sql_count, $params);
$total_items = $result['total'];
$total_pages = ceil($total_items / $items_per_page);

// Ajouter l'ordre et la limite à la requête principale
$sql .= " ORDER BY l.created_at DESC LIMIT ?, ?";
$params[] = $offset;
$params[] = $items_per_page;

// Exécuter la requête
$lots = $db->all($sql, $params);

// Obtenir la liste des CDM pour le filtre
$cdm_list = getCDMList();

// Charger la vue
require_once VIEWS_PATH . 'lots/index.php';
?>
