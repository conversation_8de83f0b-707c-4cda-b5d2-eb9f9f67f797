<?php
// Vue pour la recherche de lots et dossiers
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-search"></i> Recherche</h1>
        <div>
            <button id="printPage" class="btn btn-outline-secondary">
                <i class="fas fa-print"></i> Imprimer les résultats
            </button>
        </div>
    </div>
    
    <!-- Formulaire de recherche -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-filter"></i> Critères de recherche</h5>
        </div>
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="lots/search">
                
                <div class="col-md-4">
                    <label for="search_type" class="form-label">Type de recherche</label>
                    <select class="form-select" id="search_type" name="search_type">
                        <option value="dossier" <?php echo $search_type === 'dossier' ? 'selected' : ''; ?>>Dossier (N° dossier, nom, bénéficiaire)</option>
                        <option value="lot" <?php echo $search_type === 'lot' ? 'selected' : ''; ?>>Lot (N° lot)</option>
                        <option value="adherent" <?php echo $search_type === 'adherent' ? 'selected' : ''; ?>>Adhérent (N° adhérent exact)</option>
                    </select>
                </div>
                
                <div class="col-md-8">
                    <label for="search_term" class="form-label">Terme de recherche</label>
                    <input type="text" class="form-control" id="search_term" name="search_term" value="<?php echo $search_term; ?>" placeholder="Entrez votre terme de recherche..." required>
                </div>
                
                <div class="col-12">
                    <button type="button" class="btn btn-outline-primary" id="toggleAdvancedSearch">
                        <i class="fas fa-plus"></i> Afficher les filtres avancés
                    </button>
                </div>
                
                <div class="col-12 d-none" id="advancedFilters">
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="cdm_id" class="form-label">CDM</label>
                                    <select class="form-select" id="cdm_id" name="cdm_id">
                                        <option value="0">Tous les CDM</option>
                                        <?php foreach ($cdm_list as $cdm): ?>
                                            <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm_id == $cdm['id'] ? 'selected' : ''; ?>>
                                                <?php echo $cdm['nom']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="statut" class="form-label">Statut</label>
                                    <select class="form-select" id="statut" name="statut">
                                        <option value="">Tous les statuts</option>
                                        <?php if ($search_type === 'lot'): ?>
                                            <option value="en_attente" <?php echo $statut === 'en_attente' ? 'selected' : ''; ?>>En attente</option>
                                            <option value="recu" <?php echo $statut === 'recu' ? 'selected' : ''; ?>>Reçu</option>
                                            <option value="traite" <?php echo $statut === 'traite' ? 'selected' : ''; ?>>Traité</option>
                                            <option value="archive" <?php echo $statut === 'archive' ? 'selected' : ''; ?>>Archivé</option>
                                        <?php else: ?>
                                            <option value="recu" <?php echo $statut === 'recu' ? 'selected' : ''; ?>>Reçu</option>
                                            <option value="non_recu" <?php echo $statut === 'non_recu' ? 'selected' : ''; ?>>Non reçu</option>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="date_debut" class="form-label">Date début</label>
                                    <input type="text" class="form-control datepicker" id="date_debut" name="date_debut" value="<?php echo $date_debut; ?>" placeholder="jj/mm/aaaa">
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="date_fin" class="form-label">Date fin</label>
                                    <input type="text" class="form-control datepicker" id="date_fin" name="date_fin" value="<?php echo $date_fin; ?>" placeholder="jj/mm/aaaa">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 text-end">
                    <button type="reset" class="btn btn-secondary" id="resetFilters">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Rechercher
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Résultats de recherche -->
    <?php if (!empty($search_term)): ?>
        <div class="card">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Résultats de recherche</h5>
                    <span class="badge bg-primary"><?php echo $total_results; ?> résultat(s) trouvé(s)</span>
                </div>
            </div>
            <div class="card-body">
                <?php if ($total_results > 0): ?>
                    <?php if ($search_type === 'dossier' || $search_type === 'adherent'): ?>
                        <!-- Résultats de recherche pour les dossiers -->
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>N° Lot</th>
                                        <th>CDM</th>
                                        <th>N° Dossier</th>
                                        <th>N° Adhérent</th>
                                        <th>Nom</th>
                                        <th>Bénéficiaire</th>
                                        <th>Acte</th>
                                        <th>Statut</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results as $dossier): ?>
                                        <tr>
                                            <td>
                                                <a href="index.php?page=lots/view&id=<?php echo $dossier['lot_id']; ?>" class="text-decoration-none">
                                                    <?php echo $dossier['numero_lot']; ?>
                                                </a>
                                            </td>
                                            <td><?php echo $dossier['cdm_nom']; ?></td>
                                            <td><?php echo $dossier['numero_dossier']; ?></td>
                                            <td><?php echo $dossier['numero_adherent']; ?></td>
                                            <td><?php echo $dossier['nom']; ?></td>
                                            <td><?php echo $dossier['beneficiaire']; ?></td>
                                            <td><?php echo $dossier['acte_nom']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $dossier['recu'] ? 'success' : 'warning'; ?>">
                                                    <?php echo $dossier['recu'] ? 'Reçu' : 'Non reçu'; ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <a href="index.php?page=dossiers/edit&id=<?php echo $dossier['id']; ?>" class="btn btn-sm btn-warning btn-action" data-bs-toggle="tooltip" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="index.php?page=lots/view&id=<?php echo $dossier['lot_id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir le lot">
                                                    <i class="fas fa-folder-open"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php elseif ($search_type === 'lot'): ?>
                        <!-- Résultats de recherche pour les lots -->
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>N° Lot</th>
                                        <th>CDM</th>
                                        <th>Date demande</th>
                                        <th>Date réception</th>
                                        <th>Dossiers</th>
                                        <th>Reçus</th>
                                        <th>Statut</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results as $lot): ?>
                                        <tr>
                                            <td><?php echo $lot['numero_lot']; ?></td>
                                            <td><?php echo $lot['cdm_nom']; ?></td>
                                            <td><?php echo formatDate($lot['date_demande']); ?></td>
                                            <td><?php echo formatDate($lot['date_reception']); ?></td>
                                            <td><?php echo $lot['nb_dossiers']; ?></td>
                                            <td><?php echo $lot['nb_recus']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $lot['statut'] === 'en_attente' ? 'warning' : 
                                                        ($lot['statut'] === 'recu' ? 'info' : 
                                                        ($lot['statut'] === 'traite' ? 'success' : 'secondary')); 
                                                ?>">
                                                    <?php 
                                                        echo $lot['statut'] === 'en_attente' ? 'En attente' : 
                                                            ($lot['statut'] === 'recu' ? 'Reçu' : 
                                                            ($lot['statut'] === 'traite' ? 'Traité' : 'Archivé')); 
                                                    ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <a href="index.php?page=lots/view&id=<?php echo $lot['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="index.php?page=lots/edit&id=<?php echo $lot['id']; ?>" class="btn btn-sm btn-warning btn-action" data-bs-toggle="tooltip" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="index.php?page=lots/print&id=<?php echo $lot['id']; ?>" class="btn btn-sm btn-info btn-action" data-bs-toggle="tooltip" title="Imprimer">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($total_results >= 100): ?>
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle"></i> Les résultats sont limités aux 100 premiers enregistrements. Veuillez affiner votre recherche pour des résultats plus précis.
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Aucun résultat ne correspond à votre recherche. Veuillez essayer avec d'autres termes ou critères.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
