<?php
// Contrôleur pour l'exportation d'un lot spécifique vers Excel

// Vérifier si l'ID du lot est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=lots', 'ID du lot non spécifié.', 'danger');
}

$lot_id = intval($_GET['id']);

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du lot
$sql = "SELECT l.*, c.nom as cdm_nom, u.username as created_by_username
        FROM lots l
        JOIN cdm c ON l.cdm_id = c.id
        JOIN users u ON l.created_by = u.id
        WHERE l.id = ?";
$lot = $db->single($sql, [$lot_id]);

if (!$lot) {
    redirect('index.php?page=lots', 'Lot non trouvé.', 'danger');
}

// Récupérer les dossiers associés à ce lot
$sql = "SELECT d.*, a.nom as acte_nom
        FROM dossiers d
        LEFT JOIN actes a ON d.acte_id = a.id
        WHERE d.lot_id = ?
        ORDER BY d.created_at ASC";
$dossiers = $db->all($sql, [$lot_id]);

// Préparer les données pour l'exportation
$headers = [
    'N°',
    'N° Dossier',
    'N° Adhérent',
    'Nom',
    'Bénéficiaire',
    'Acte',
    'Montant (DH)',
    'N° Bon',
    'Statut',
    'Notes',
    'Date de création'
];

$data = [];
foreach ($dossiers as $index => $dossier) {
    $data[] = [
        $index + 1,
        $dossier['numero_dossier'],
        $dossier['numero_adherent'],
        $dossier['nom'],
        $dossier['beneficiaire'],
        $dossier['acte_nom'],
        !empty($dossier['montant']) ? number_format($dossier['montant'], 2, ',', ' ') : '',
        $dossier['numero_bon'],
        $dossier['recu'] ? 'Reçu' : 'Non reçu',
        $dossier['notes'],
        formatDate($dossier['created_at'], DATETIME_FORMAT)
    ];
}

// Nom du fichier
$filename = 'Lot_' . $lot['numero'] . '_' . $lot['cdm_nom'] . '_' . date('Ymd');

// Format d'exportation (csv par défaut)
$format = isset($_GET['format']) && $_GET['format'] === 'xlsx' ? 'xlsx' : 'csv';

// Journaliser l'action
logActivity($_SESSION['user_id'], 'Exportation d\'un lot', "Lot #" . $lot['numero'] . " au format $format");

// Exporter les données
ExcelExporter::export($data, $headers, $filename, $format);
?>
