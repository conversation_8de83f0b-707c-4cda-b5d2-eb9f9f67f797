<?php
// Script pour vérifier si la table bordereaux_retour existe
require_once 'config/config.php';
require_once 'config/database.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

try {
    // Vérifier si la table bordereaux_retour existe
    $sql = "SHOW TABLES LIKE 'bordereaux_retour'";
    $result = $db->query($sql);
    $table_exists = $result->rowCount() > 0;
    
    if ($table_exists) {
        echo "La table 'bordereaux_retour' existe.";
        
        // Vérifier la structure de la table
        $sql = "DESCRIBE bordereaux_retour";
        $structure = $db->all($sql);
        
        echo "<h2>Structure de la table bordereaux_retour</h2>";
        echo "<pre>";
        print_r($structure);
        echo "</pre>";
        
        // Vérifier les données existantes
        $sql = "SELECT * FROM bordereaux_retour LIMIT 5";
        $data = $db->all($sql);
        
        echo "<h2>Exemples de données dans bordereaux_retour</h2>";
        echo "<pre>";
        print_r($data);
        echo "</pre>";
    } else {
        echo "La table 'bordereaux_retour' n'existe pas. Création de la table...";
        
        // Créer la table bordereaux_retour
        $sql = "CREATE TABLE bordereaux_retour (
            id INT AUTO_INCREMENT PRIMARY KEY,
            numero INT NOT NULL,
            cdm_id INT NOT NULL,
            date_bordereau DATE NOT NULL,
            created_by INT NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NULL,
            UNIQUE KEY unique_bordereau (numero, cdm_id),
            FOREIGN KEY (cdm_id) REFERENCES cdm(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )";
        
        $db->query($sql);
        echo "<p>Table 'bordereaux_retour' créée avec succès.</p>";
        
        // Migrer les données existantes
        echo "<p>Migration des données existantes...</p>";
        
        $sql = "INSERT IGNORE INTO bordereaux_retour (numero, cdm_id, date_bordereau, created_by, created_at)
                SELECT DISTINCT numero_bordereau, cdm_id, date_bordereau, created_by, created_at
                FROM dossiers_retournes";
        
        $result = $db->query($sql);
        $count = $result->rowCount();
        
        echo "<p>$count bordereaux migrés avec succès.</p>";
    }
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage();
}
?>
