<?php
// Contrôleur pour la modification d'un dossier retourné

// Vérifier si l'ID du dossier est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=retours', 'ID du dossier non spécifié.', 'danger');
}

$dossier_id = intval($_GET['id']);

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du dossier
$sql = "SELECT dr.*, l.numero as numero_bordereau, l.cdm_id, c.nom as cdm_nom
        FROM dossiers_retournes dr
        LEFT JOIN bordereaux_retour l ON dr.numero_bordereau = l.numero AND dr.cdm_id = l.cdm_id
        JOIN cdm c ON dr.cdm_id = c.id
        WHERE dr.id = ?";
$dossier = $db->single($sql, [$dossier_id]);

// Vérifier si le dossier existe
if (!$dossier) {
    redirect('index.php?page=retours', 'Dossier non trouvé.', 'danger');
}

$numero_bordereau = $dossier['numero_bordereau'];
$cdm_id = $dossier['cdm_id'];

// Titre de la page
$page_title = 'Modifier le dossier retourné #' . $dossier['numero_dossier'];
$page = 'dossiers/edit_retour';

// Récupérer les raisons de retour actuelles du dossier
$sql = "SELECT dr.*, r.raison, r.id as raison_id
        FROM dossier_raisons dr
        JOIN raisons_retour r ON dr.raison_id = r.id
        WHERE dr.dossier_id = ?
        ORDER BY dr.created_at ASC";
$raisons_dossier = $db->all($sql, [$dossier_id]);

$raisons_ids = [];
foreach ($raisons_dossier as $raison) {
    $raisons_ids[] = $raison['raison_id'];
}

// Si aucune raison n'est trouvée dans la nouvelle table, utiliser la raison principale
if (empty($raisons_ids) && !empty($dossier['raison_retour_id'])) {
    $raisons_ids[] = $dossier['raison_retour_id'];
}

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=dossiers/edit_retour&id=' . $dossier_id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupérer et nettoyer les données du formulaire
    $numero_dossier = clean($_POST['numero_dossier'] ?? '');
    $numero_adherent = clean($_POST['numero_adherent'] ?? '');
    $nom = clean($_POST['nom'] ?? '');
    $beneficiaire = clean($_POST['beneficiaire'] ?? '');

    // Gestion des actes multiples
    $acte_ids = isset($_POST['acte_ids']) ? clean($_POST['acte_ids']) : '';
    // Utiliser le premier acte comme acte principal pour la rétrocompatibilité
    $acte_id = !empty($acte_ids) ? intval(explode(',', $acte_ids)[0]) : 0;

    $montant = isset($_POST['montant']) ? floatval(str_replace(',', '.', $_POST['montant'])) : 0;
    $raisons_retour = isset($_POST['raisons_retour']) && is_array($_POST['raisons_retour']) ? $_POST['raisons_retour'] : [];
    // Utiliser la première raison comme raison principale pour la rétrocompatibilité
    $raison_retour_id = !empty($raisons_retour) ? intval($raisons_retour[0]) : 0;
    $date_retour = isset($_POST['date_retour']) ? clean($_POST['date_retour']) : '';
    $date_correction = isset($_POST['date_correction']) ? clean($_POST['date_correction']) : '';
    $corrige = isset($_POST['corrige']) && $_POST['corrige'] === '1' ? 1 : 0;
    $notes = clean($_POST['notes'] ?? '');

    // Valider les données
    $errors = [];

    if (empty($numero_dossier)) {
        $errors[] = 'Le numéro de dossier est requis.';
    }

    if (empty($numero_adherent)) {
        $errors[] = 'Le numéro d\'adhérent est requis.';
    }

    if (empty($nom)) {
        $errors[] = 'Le nom est requis.';
    }

    if (empty($raisons_retour)) {
        $errors[] = 'Au moins une raison du retour est requise.';
    }

    if (empty($date_retour)) {
        $errors[] = 'La date de retour est requise.';
    } else {
        // Convertir la date au format MySQL (YYYY-MM-DD)
        $date_retour = date('Y-m-d', strtotime(str_replace('/', '-', $date_retour)));
    }

    if (!empty($date_correction)) {
        // Convertir la date au format MySQL (YYYY-MM-DD)
        $date_correction = date('Y-m-d', strtotime(str_replace('/', '-', $date_correction)));
    } else {
        $date_correction = null;
    }

    // S'il y a des erreurs, rediriger vers la page avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = $_POST;
        redirect('index.php?page=dossiers/edit_retour&id=' . $dossier_id, 'Veuillez corriger les erreurs.', 'danger');
    }

    try {
        // Mettre à jour le dossier dans la base de données
        $sql = "UPDATE dossiers_retournes
                SET numero_dossier = ?, numero_adherent = ?, nom = ?, beneficiaire = ?,
                    acte_id = ?, montant = ?, raison_retour_id = ?, date_retour = ?,
                    date_correction = ?, corrige = ?, notes = ?, updated_at = NOW()
                WHERE id = ?";
        $params = [
            $numero_dossier, $numero_adherent, $nom, $beneficiaire,
            $acte_id ?: null, $montant, $raison_retour_id, $date_retour,
            $date_correction, $corrige, $notes, $dossier_id
        ];
        $db->query($sql, $params);

        // Supprimer les anciennes raisons
        $sql = "DELETE FROM dossier_raisons WHERE dossier_id = ?";
        $db->query($sql, [$dossier_id]);

        // Insérer les nouvelles raisons
        foreach ($raisons_retour as $raison_id) {
            $sql = "INSERT INTO dossier_raisons (dossier_id, raison_id) VALUES (?, ?)";
            $db->query($sql, [$dossier_id, intval($raison_id)]);
        }

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Modification d\'un dossier retourné', "Dossier #$numero_dossier modifié");

        // Rediriger vers la page d'édition du bordereau
        redirect('index.php?page=retours/edit&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Le dossier a été modifié avec succès.', 'success');

    } catch (Exception $e) {
        redirect('index.php?page=dossiers/edit_retour&id=' . $dossier_id, 'Erreur lors de la modification du dossier: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'numero_dossier' => $dossier['numero_dossier'],
    'numero_adherent' => $dossier['numero_adherent'],
    'nom' => $dossier['nom'],
    'beneficiaire' => $dossier['beneficiaire'],
    'acte_id' => $dossier['acte_id'],
    'acte_ids' => $dossier['acte_id'],
    'montant' => $dossier['montant'],
    'raisons_retour' => $raisons_ids,
    'date_retour' => formatDate($dossier['date_retour']),
    'date_correction' => formatDate($dossier['date_correction']),
    'corrige' => $dossier['corrige'],
    'notes' => $dossier['notes']
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Obtenir la liste des actes
$actes_list = getActesList();

// Obtenir la liste des raisons de retour
$raisons_list = getRaisonsRetourList();

// Charger la vue
require_once VIEWS_PATH . 'dossiers/edit_retour.php';
?>
