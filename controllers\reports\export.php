<?php
/**
 * Contrôleur pour l'exportation des rapports
 */

// Inclure la classe ExcelExporter
require_once __DIR__ . '/../../includes/ExcelExporter.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres d'exportation
$report_type = isset($_GET['type']) ? clean($_GET['type']) : 'retours';
$periode = isset($_GET['periode']) ? intval($_GET['periode']) : 30;
$cdm_id = isset($_GET['cdm_id']) ? intval($_GET['cdm_id']) : 0;
$format = isset($_GET['format']) && $_GET['format'] === 'xlsx' ? 'xlsx' : 'csv';

// Calculer les dates
$date_fin = date('Y-m-d');
$date_debut = date('Y-m-d', strtotime("-$periode days"));

$data = [];
$headers = [];
$filename = '';

switch ($report_type) {
    case 'retours':
        // Rapport des retours
        $sql = "SELECT dr.*, c.nom as cdm_nom, a.nom as acte_nom, a.code as acte_code, r.raison as raison_retour
                FROM dossiers_retournes dr
                LEFT JOIN cdm c ON dr.cdm_id = c.id
                LEFT JOIN actes a ON dr.acte_id = a.id
                LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
                WHERE dr.date_retour BETWEEN ? AND ?";
        
        $params = [$date_debut, $date_fin];
        
        if ($cdm_id > 0) {
            $sql .= " AND dr.cdm_id = ?";
            $params[] = $cdm_id;
        }
        
        $sql .= " ORDER BY dr.date_retour DESC, dr.created_at DESC";
        
        $results = $db->all($sql, $params);
        
        $headers = [
            'Date retour',
            'N° Bordereau',
            'CDM',
            'N° Dossier',
            'N° Adhérent',
            'Nom',
            'Bénéficiaire',
            'Code Acte',
            'Nom Acte',
            'Montant (DH)',
            'Raison retour',
            'Statut',
            'Date correction',
            'Notes'
        ];
        
        foreach ($results as $row) {
            $data[] = [
                formatDate($row['date_retour']),
                $row['numero_bordereau'],
                $row['cdm_nom'],
                $row['numero_dossier'],
                $row['numero_adherent'],
                $row['nom'],
                $row['beneficiaire'],
                $row['acte_code'],
                $row['acte_nom'],
                !empty($row['montant']) ? number_format($row['montant'], 2, ',', ' ') : '0,00',
                $row['raison_retour'],
                $row['corrige'] ? 'Corrigé' : 'Non corrigé',
                formatDate($row['date_correction']),
                $row['notes']
            ];
        }
        
        $filename = 'Rapport_Retours_' . $periode . 'j_' . date('Ymd');
        break;
        
    case 'lots':
        // Rapport des lots
        $sql = "SELECT l.*, c.nom as cdm_nom, u.username as created_by_username,
                       (SELECT COUNT(*) FROM dossiers WHERE lot_id = l.id) as nb_dossiers,
                       (SELECT COUNT(*) FROM dossiers WHERE lot_id = l.id AND recu = 1) as nb_recus
                FROM lots l
                LEFT JOIN cdm c ON l.cdm_id = c.id
                LEFT JOIN users u ON l.created_by = u.id
                WHERE l.date_reception BETWEEN ? AND ?";
        
        $params = [$date_debut, $date_fin];
        
        if ($cdm_id > 0) {
            $sql .= " AND l.cdm_id = ?";
            $params[] = $cdm_id;
        }
        
        $sql .= " ORDER BY l.date_reception DESC, l.created_at DESC";
        
        $results = $db->all($sql, $params);
        
        $headers = [
            'N° Lot',
            'CDM',
            'Date demande',
            'Date réception',
            'Nombre dossiers',
            'Dossiers reçus',
            'Statut',
            'Date traitement',
            'Créé par',
            'Date création'
        ];
        
        foreach ($results as $row) {
            $statut_text = '';
            switch ($row['statut']) {
                case 'en_attente':
                    $statut_text = 'En attente';
                    break;
                case 'recu':
                    $statut_text = 'Reçu';
                    break;
                case 'traite':
                    $statut_text = 'Traité';
                    break;
                case 'archive':
                    $statut_text = 'Archivé';
                    break;
            }
            
            $data[] = [
                $row['numero_lot'],
                $row['cdm_nom'],
                formatDate($row['date_demande']),
                formatDate($row['date_reception']),
                $row['nb_dossiers'],
                $row['nb_recus'],
                $statut_text,
                formatDate($row['date_traitement']),
                $row['created_by_username'],
                formatDate($row['created_at'], DATETIME_FORMAT)
            ];
        }
        
        $filename = 'Rapport_Lots_' . $periode . 'j_' . date('Ymd');
        break;
        
    case 'activite':
        // Rapport d'activité
        $sql = "SELECT al.*, u.username
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.created_at BETWEEN ? AND ?";
        
        $params = [$date_debut . ' 00:00:00', $date_fin . ' 23:59:59'];
        
        $sql .= " ORDER BY al.created_at DESC";
        
        $results = $db->all($sql, $params);
        
        $headers = [
            'Date/Heure',
            'Utilisateur',
            'Action',
            'Détails',
            'Adresse IP'
        ];
        
        foreach ($results as $row) {
            $data[] = [
                formatDate($row['created_at'], DATETIME_FORMAT),
                $row['username'],
                $row['action'],
                $row['details'],
                $row['ip_address']
            ];
        }
        
        $filename = 'Rapport_Activite_' . $periode . 'j_' . date('Ymd');
        break;
        
    case 'statistiques':
        // Rapport de statistiques
        $sql = "SELECT 
                    DATE(dr.date_retour) as date_retour,
                    COUNT(*) as total_retours,
                    SUM(CASE WHEN dr.corrige = 1 THEN 1 ELSE 0 END) as retours_corriges,
                    SUM(dr.montant) as montant_total,
                    c.nom as cdm_nom
                FROM dossiers_retournes dr
                LEFT JOIN cdm c ON dr.cdm_id = c.id
                WHERE dr.date_retour BETWEEN ? AND ?";
        
        $params = [$date_debut, $date_fin];
        
        if ($cdm_id > 0) {
            $sql .= " AND dr.cdm_id = ?";
            $params[] = $cdm_id;
        }
        
        $sql .= " GROUP BY DATE(dr.date_retour), dr.cdm_id
                  ORDER BY dr.date_retour DESC";
        
        $results = $db->all($sql, $params);
        
        $headers = [
            'Date',
            'CDM',
            'Total retours',
            'Retours corrigés',
            'Retours non corrigés',
            'Taux correction (%)',
            'Montant total (DH)'
        ];
        
        foreach ($results as $row) {
            $non_corriges = $row['total_retours'] - $row['retours_corriges'];
            $taux_correction = $row['total_retours'] > 0 ? 
                round(($row['retours_corriges'] / $row['total_retours']) * 100, 1) : 0;
            
            $data[] = [
                formatDate($row['date_retour']),
                $row['cdm_nom'],
                $row['total_retours'],
                $row['retours_corriges'],
                $non_corriges,
                $taux_correction . '%',
                !empty($row['montant_total']) ? number_format($row['montant_total'], 2, ',', ' ') : '0,00'
            ];
        }
        
        $filename = 'Rapport_Statistiques_' . $periode . 'j_' . date('Ymd');
        break;
        
    default:
        // Type de rapport non reconnu
        redirect('index.php?page=reports/retours', 'Type de rapport non reconnu.', 'danger');
        break;
}

// Vérifier s'il y a des données à exporter
if (empty($data)) {
    redirect('index.php?page=reports/retours', 'Aucune donnée à exporter pour la période sélectionnée.', 'warning');
}

// Journaliser l'action
$cdm_filter = $cdm_id > 0 ? " (CDM: $cdm_id)" : '';
logActivity($_SESSION['user_id'], 'Exportation de rapport', "Type: $report_type, Période: $periode jours, Format: $format$cdm_filter");

// Exporter les données
ExcelExporter::export($data, $headers, $filename, $format);
?>
