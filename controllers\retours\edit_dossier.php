<?php
// Contrôleur pour la modification d'un dossier retourné

// Vérifier si les paramètres nécessaires sont fournis
if (!isset($_GET['id']) || empty($_GET['id']) || !isset($_GET['numero']) || empty($_GET['numero']) || !isset($_GET['cdm_id']) || empty($_GET['cdm_id'])) {
    redirect('index.php?page=retours', 'Paramètres manquants.', 'danger');
}

$dossier_id = intval($_GET['id']);
$numero_bordereau = intval($_GET['numero']);
$cdm_id = intval($_GET['cdm_id']);

// Titre de la page
$page_title = 'Modifier un dossier retourné';
$page = 'retours/edit_dossier';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du dossier
$sql = "SELECT dr.*, c.nom as cdm_nom
        FROM dossiers_retournes dr
        JOIN cdm c ON dr.cdm_id = c.id
        WHERE dr.id = ? AND dr.numero_bordereau = ? AND dr.cdm_id = ?";
$dossier = $db->single($sql, [$dossier_id, $numero_bordereau, $cdm_id]);

// Vérifier si le dossier existe
if (!$dossier) {
    redirect('index.php?page=retours/view&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Dossier non trouvé.', 'danger');
}

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=retours/edit_dossier&id=' . $dossier_id . '&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupérer et nettoyer les données du formulaire
    $numero_dossier = isset($_POST['numero_dossier']) ? clean($_POST['numero_dossier']) : '';
    $numero_adherent = isset($_POST['numero_adherent']) ? clean($_POST['numero_adherent']) : '';
    $nom = isset($_POST['nom']) ? clean($_POST['nom']) : '';
    $beneficiaire = isset($_POST['beneficiaire']) ? clean($_POST['beneficiaire']) : '';
    $acte_id = isset($_POST['acte_id']) ? intval($_POST['acte_id']) : 0;
    $montant = isset($_POST['montant']) ? floatval(str_replace(',', '.', $_POST['montant'])) : 0;
    $raison_retour_id = isset($_POST['raison_retour_id']) ? intval($_POST['raison_retour_id']) : 0;
    $date_retour = isset($_POST['date_retour']) ? clean($_POST['date_retour']) : '';
    $corrige = isset($_POST['corrige']) ? 1 : 0;
    $date_correction = isset($_POST['date_correction']) ? clean($_POST['date_correction']) : '';
    $commentaire = isset($_POST['commentaire']) ? clean($_POST['commentaire']) : '';

    // Valider les données
    $errors = [];

    if (empty($numero_dossier)) {
        $errors[] = 'Le numéro de dossier est obligatoire.';
    }

    if (empty($numero_adherent)) {
        $errors[] = 'Le numéro d\'adhérent est obligatoire.';
    }

    if (empty($nom)) {
        $errors[] = 'Le nom est obligatoire.';
    }

    if (empty($date_retour)) {
        $errors[] = 'La date de retour est obligatoire.';
    } else {
        // Convertir la date au format MySQL (YYYY-MM-DD)
        $date_retour = date('Y-m-d', strtotime(str_replace('/', '-', $date_retour)));
    }

    if ($raison_retour_id <= 0) {
        $errors[] = 'La raison du retour est obligatoire.';
    }

    // Convertir la date de correction si elle est fournie
    if (!empty($date_correction)) {
        $date_correction = date('Y-m-d', strtotime(str_replace('/', '-', $date_correction)));
    } else {
        $date_correction = null;
    }

    // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = $_POST;
        redirect('index.php?page=retours/edit_dossier&id=' . $dossier_id . '&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Veuillez corriger les erreurs.', 'danger');
    }

    try {
        // Mettre à jour le dossier dans la base de données
        $sql = "UPDATE dossiers_retournes 
                SET numero_dossier = ?, numero_adherent = ?, nom = ?, beneficiaire = ?, 
                    acte_id = ?, montant = ?, raison_retour_id = ?, date_retour = ?, 
                    corrige = ?, date_correction = ?, commentaire = ?, updated_at = NOW()
                WHERE id = ?";
        $params = [
            $numero_dossier, $numero_adherent, $nom, $beneficiaire, 
            $acte_id, $montant, $raison_retour_id, $date_retour, 
            $corrige, $date_correction, $commentaire, $dossier_id
        ];
        $db->query($sql, $params);

        // Journaliser l'action
        logActivity($_SESSION['user_id'], 'Modification d\'un dossier retourné', "Dossier #$numero_dossier modifié");

        // Rediriger vers la page de détails du bordereau
        redirect('index.php?page=retours/view&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Le dossier a été modifié avec succès.', 'success');

    } catch (Exception $e) {
        redirect('index.php?page=retours/edit_dossier&id=' . $dossier_id . '&numero=' . $numero_bordereau . '&cdm_id=' . $cdm_id, 'Erreur lors de la modification du dossier: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'numero_dossier' => $dossier['numero_dossier'],
    'numero_adherent' => $dossier['numero_adherent'],
    'nom' => $dossier['nom'],
    'beneficiaire' => $dossier['beneficiaire'],
    'acte_id' => $dossier['acte_id'],
    'montant' => $dossier['montant'],
    'raison_retour_id' => $dossier['raison_retour_id'],
    'date_retour' => formatDate($dossier['date_retour']),
    'corrige' => $dossier['corrige'],
    'date_correction' => formatDate($dossier['date_correction']),
    'commentaire' => $dossier['commentaire']
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Obtenir la liste des actes
$actes_list = getActesList();

// Obtenir la liste des raisons de retour
$raisons_list = getRaisonsRetourList();

// Charger la vue
require_once VIEWS_PATH . 'retours/edit_dossier.php';
?>
