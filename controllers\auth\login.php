<?php
// Contrôleur pour la page de connexion

// Rediriger si l'utilisateur est déjà connecté
if (isset($_SESSION['user_id'])) {
    redirect('index.php?page=dashboard');
}

// Vérifier si l'installation est nécessaire
if (needsInstallation()) {
    redirect('index.php?page=install');
}

// Traitement du formulaire de connexion
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=login', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }
    
    // Récupérer et nettoyer les données du formulaire
    $username = clean($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']) && $_POST['remember'] === 'on';
    
    // Valider les données
    if (empty($username) || empty($password)) {
        redirect('index.php?page=login', 'Veuillez saisir votre nom d\'utilisateur et votre mot de passe.', 'danger');
    }
    
    // Vérifier les identifiants
    $db = Database::getInstance();
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = ?";
    $user = $db->single($sql, [$username]);
    
    if (!$user || !password_verify($password, $user['password'])) {
        redirect('index.php?page=login', 'Identifiants incorrects.', 'danger');
    }
    
    if (!$user['active']) {
        redirect('index.php?page=login', 'Votre compte est désactivé. Veuillez contacter l\'administrateur.', 'danger');
    }
    
    // Mettre à jour la date de dernière connexion
    $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
    $db->query($sql, [$user['id']]);
    
    // Enregistrer l'activité
    logActivity($user['id'], 'Connexion', 'Connexion réussie');
    
    // Créer la session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['user_role'] = $user['role'];
    
    // Si "Se souvenir de moi" est coché, créer un cookie
    if ($remember) {
        $token = bin2hex(random_bytes(32));
        $expires = time() + (30 * 24 * 60 * 60); // 30 jours
        
        // Stocker le token en base de données
        $sql = "INSERT INTO user_tokens (user_id, token, expires_at) VALUES (?, ?, FROM_UNIXTIME(?))";
        $db->query($sql, [$user['id'], password_hash($token, PASSWORD_DEFAULT), $expires]);
        
        // Créer le cookie
        setcookie('remember_token', $token, $expires, '/', '', false, true);
    }
    
    // Rediriger vers le tableau de bord
    redirect('index.php?page=dashboard', 'Bienvenue, ' . $user['username'] . ' !', 'success');
}

// Afficher le formulaire de connexion
require_once VIEWS_PATH . 'auth/login.php';
?>
