/**
 * Script moderne pour les graphiques Chart.js
 * Optimisé pour un affichage élégant et compact
 */

// Configuration globale pour Chart.js
Chart.defaults.font.family = "'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
Chart.defaults.font.size = 12;
Chart.defaults.color = '#6c757d';

// Couleurs modernes et élégantes
const modernColors = {
    primary: ['#667eea', '#764ba2', '#4facfe', '#00f2fe'],
    success: ['#11998e', '#38ef7d', '#06ffa5', '#00d4aa'],
    warning: ['#fa709a', '#fee140', '#f093fb', '#f5576c'],
    danger: ['#fc4a1a', '#f7b733', '#ff6b6b', '#ee5a24'],
    info: ['#4facfe', '#00f2fe', '#43e97b', '#38f9d7'],
    gradients: {
        blue: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        green: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
        orange: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        red: 'linear-gradient(135deg, #fc4a1a 0%, #f7b733 100%)',
        cyan: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
    }
};

// Configuration par défaut pour tous les graphiques
const defaultChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom',
            labels: {
                padding: 20,
                usePointStyle: true,
                font: {
                    size: 11,
                    weight: '500'
                }
            }
        },
        tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#fff',
            bodyColor: '#fff',
            borderColor: 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: true,
            titleFont: {
                size: 13,
                weight: 'bold'
            },
            bodyFont: {
                size: 12
            }
        }
    },
    animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
    }
};

// Fonction pour créer un graphique en barres moderne
function createModernBarChart(canvasId, labels, data, title = '') {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;

    return new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: title || 'Données',
                data: data,
                backgroundColor: modernColors.primary.map(color => color + '80'),
                borderColor: modernColors.primary,
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            ...defaultChartOptions,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        drawBorder: false
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 11
                        },
                        maxRotation: 45
                    }
                }
            }
        }
    });
}

// Fonction pour créer un graphique en secteurs moderne
function createModernPieChart(canvasId, labels, data, title = '') {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;

    return new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                label: title || 'Données',
                data: data,
                backgroundColor: [
                    '#667eea',
                    '#11998e',
                    '#fa709a',
                    '#fc4a1a',
                    '#4facfe',
                    '#38ef7d',
                    '#fee140',
                    '#f7b733'
                ],
                borderColor: '#fff',
                borderWidth: 3,
                hoverBorderWidth: 4,
                hoverOffset: 8
            }]
        },
        options: {
            ...defaultChartOptions,
            cutout: '60%',
            plugins: {
                ...defaultChartOptions.plugins,
                legend: {
                    ...defaultChartOptions.plugins.legend,
                    position: 'right'
                }
            }
        }
    });
}

// Fonction pour créer un graphique en aires moderne
function createModernAreaChart(canvasId, labels, data, title = '') {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;

    return new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: title || 'Données',
                data: data,
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderColor: '#667eea',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            ...defaultChartOptions,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// Initialisation automatique des graphiques
document.addEventListener('DOMContentLoaded', function() {
    // Graphique d'activité CDM
    const cdmChart = document.getElementById('cdmActivityChart');
    if (cdmChart) {
        const labels = JSON.parse(cdmChart.dataset.labels || '[]');
        const values = JSON.parse(cdmChart.dataset.values || '[]');
        createModernBarChart('cdmActivityChart', labels, values, 'Nombre de dossiers');
    }

    // Graphique de distribution des actes
    const actesChart = document.getElementById('actesDistributionChart');
    if (actesChart) {
        const labels = JSON.parse(actesChart.dataset.labels || '[]');
        const values = JSON.parse(actesChart.dataset.values || '[]');
        createModernPieChart('actesDistributionChart', labels, values, 'Nombre d\'actes');
    }

    // Graphique des raisons de retour
    const raisonsChart = document.getElementById('raisonsRetourChart');
    if (raisonsChart) {
        const labels = JSON.parse(raisonsChart.dataset.labels || '[]');
        const values = JSON.parse(raisonsChart.dataset.values || '[]');
        createModernPieChart('raisonsRetourChart', labels, values, 'Nombre de retours');
    }

    // Graphiques pour les rapports
    initializeReportCharts();
});

// Fonction pour initialiser les graphiques des rapports
function initializeReportCharts() {
    // Graphique évolution mensuelle
    const evolutionChart = document.getElementById('evolutionChart');
    if (evolutionChart) {
        const labels = JSON.parse(evolutionChart.dataset.labels || '[]');
        const values = JSON.parse(evolutionChart.dataset.values || '[]');
        createModernAreaChart('evolutionChart', labels, values, 'Évolution');
    }

    // Graphique comparaison CDM
    const comparaisonChart = document.getElementById('comparaisonChart');
    if (comparaisonChart) {
        const labels = JSON.parse(comparaisonChart.dataset.labels || '[]');
        const values = JSON.parse(comparaisonChart.dataset.values || '[]');
        createModernBarChart('comparaisonChart', labels, values, 'Comparaison');
    }

    // Graphique répartition
    const repartitionChart = document.getElementById('repartitionChart');
    if (repartitionChart) {
        const labels = JSON.parse(repartitionChart.dataset.labels || '[]');
        const values = JSON.parse(repartitionChart.dataset.values || '[]');
        createModernPieChart('repartitionChart', labels, values, 'Répartition');
    }
}

// Fonction utilitaire pour redimensionner les graphiques
function resizeCharts() {
    Chart.helpers.each(Chart.instances, function(instance) {
        instance.resize();
    });
}

// Redimensionner les graphiques lors du redimensionnement de la fenêtre
window.addEventListener('resize', resizeCharts);

// Export des fonctions pour utilisation externe
window.ChartUtils = {
    createModernBarChart,
    createModernPieChart,
    createModernAreaChart,
    modernColors,
    resizeCharts
};
