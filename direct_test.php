<?php
/**
 * Test direct de la page problématique
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';
require_once 'config/routes.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // C<PERSON>er ou récupérer l'utilisateur admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if (!$user) {
        // Créer l'utilisateur admin
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, email, nom, prenom, role, active, created_at) VALUES
                ('admin', ?, '<EMAIL>', 'Administrateur', 'Système', 'admin', 1, NOW())";
        $db->query($sql, [$password_hash]);
        
        // Récupérer l'utilisateur créé
        $user = $db->single("SELECT id, username, password, role, active FROM users WHERE username = 'admin'");
        echo "✅ Utilisateur admin créé<br>";
    }
    
    // Créer la session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['role'] = $user['role'];
    
    echo "<h2>🧪 Test Direct du Système</h2>";
    echo "<p>✅ Session créée pour: " . $user['username'] . " (ID: " . $user['id'] . ", Rôle: " . $user['role'] . ")</p>";
    
    // Vérifier les données de base
    echo "<h3>📊 Vérification des données:</h3>";
    
    // CDM
    $sql = "SELECT COUNT(*) as count FROM cdm";
    $result = $db->single($sql);
    echo "• CDM: " . $result['count'] . "<br>";
    
    // Actes
    $sql = "SELECT COUNT(*) as count FROM actes";
    $result = $db->single($sql);
    echo "• Actes: " . $result['count'] . "<br>";
    
    // Raisons de retour
    $sql = "SELECT COUNT(*) as count FROM raisons_retour";
    $result = $db->single($sql);
    echo "• Raisons de retour: " . $result['count'] . "<br>";
    
    // Dossiers retournés
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes";
    $result = $db->single($sql);
    echo "• Dossiers retournés: " . $result['count'] . "<br>";
    
    // Vérifier si le bordereau 10001 existe
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = 10001 AND cdm_id = 1";
    $result = $db->single($sql);
    echo "• Dossiers dans bordereau 10001: " . $result['count'] . "<br>";
    
    echo "<h3>🔧 Tests de fonctionnalités:</h3>";
    
    // Test 1: Charger la page retours/edit
    echo "<h4>Test 1: Chargement de la page retours/edit</h4>";
    
    $page = 'retours/edit';
    if (isset($routes[$page])) {
        echo "✅ Route trouvée: " . $routes[$page] . "<br>";
        
        if (file_exists($routes[$page])) {
            echo "✅ Fichier contrôleur existe<br>";
            
            // Simuler les paramètres GET
            $_GET['numero'] = 10001;
            $_GET['cdm_id'] = 1;
            $_GET['page'] = $page;
            
            echo "✅ Paramètres définis: numero=10001, cdm_id=1<br>";
            
            // Capturer la sortie
            ob_start();
            try {
                include $routes[$page];
                $output = ob_get_contents();
                ob_end_clean();
                
                if (strlen($output) > 100) {
                    echo "✅ Page chargée avec succès (" . strlen($output) . " caractères)<br>";
                    
                    // Vérifier si c'est une redirection
                    if (strpos($output, 'Location:') !== false) {
                        echo "⚠️ Redirection détectée<br>";
                    } else {
                        echo "✅ Contenu HTML généré<br>";
                        
                        // Afficher un aperçu
                        echo "<details><summary>Aperçu du contenu (premiers 500 caractères)</summary>";
                        echo "<pre>" . htmlspecialchars(substr($output, 0, 500)) . "...</pre>";
                        echo "</details>";
                    }
                } else {
                    echo "⚠️ Sortie courte ou vide: " . htmlspecialchars($output) . "<br>";
                }
                
            } catch (Exception $e) {
                ob_end_clean();
                echo "❌ Erreur lors du chargement: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ Fichier contrôleur introuvable: " . $routes[$page] . "<br>";
        }
    } else {
        echo "❌ Route non trouvée pour: $page<br>";
    }
    
    echo "<h3>🔗 Liens de test:</h3>";
    echo "<ul>";
    echo "<li><a href='index.php?page=dashboard' target='_blank'>📊 Dashboard</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank'>📝 Page problématique</a></li>";
    echo "<li><a href='index.php?page=retours' target='_blank'>📋 Liste des retours</a></li>";
    echo "<li><a href='maintenance.php?key=LOT2024MAINTENANCE' target='_blank'>🛠️ Maintenance</a></li>";
    echo "</ul>";
    
    echo "<h3>📋 Informations de session:</h3>";
    echo "<pre>";
    print_r($_SESSION);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<h2>❌ Erreur</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
    echo "<p>Trace: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Direct - LOT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        pre {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            max-height: 300px;
        }
        
        details {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        
        summary {
            cursor: pointer;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
</body>
</html>
