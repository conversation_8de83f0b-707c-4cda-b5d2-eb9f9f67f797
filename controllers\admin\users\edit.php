<?php
// Contrôleur pour la modification d'un utilisateur

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Vérifier si l'ID est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=admin/users', 'ID de l\'utilisateur non spécifié.', 'danger');
}

$id = intval($_GET['id']);

// Titre de la page
$page_title = 'Modifier un utilisateur';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations de l'utilisateur
$sql = "SELECT * FROM users WHERE id = ?";
$user = $db->single($sql, [$id]);

if (!$user) {
    redirect('index.php?page=admin/users', 'Utilisateur non trouvé.', 'danger');
}

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCSRFToken(isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '')) {
        redirect('index.php?page=admin/users/edit&id=' . $id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }

    // Récupération des données du formulaire
    $username = isset($_POST['username']) ? clean($_POST['username']) : '';
    $email = isset($_POST['email']) ? clean($_POST['email']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
    $role = isset($_POST['role']) ? clean($_POST['role']) : '';
    $active = isset($_POST['active']) ? 1 : 0;

    // Validation des données
    $errors = [];

    if (empty($username)) {
        $errors[] = 'Le nom d\'utilisateur est obligatoire.';
    } elseif (strlen($username) < 3) {
        $errors[] = 'Le nom d\'utilisateur doit contenir au moins 3 caractères.';
    }

    if (empty($email)) {
        $errors[] = 'L\'adresse e-mail est obligatoire.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'L\'adresse e-mail n\'est pas valide.';
    }

    // Vérification du mot de passe uniquement s'il est fourni
    if (!empty($password)) {
        if (strlen($password) < 6) {
            $errors[] = 'Le mot de passe doit contenir au moins 6 caractères.';
        }

        if ($password !== $confirm_password) {
            $errors[] = 'Les mots de passe ne correspondent pas.';
        }
    }

    if (empty($role) || !in_array($role, ['admin', 'user'])) {
        $errors[] = 'Le rôle sélectionné n\'est pas valide.';
    }

    // Vérifier si le nom d'utilisateur existe déjà (sauf pour cet utilisateur)
    $sql = "SELECT COUNT(*) as count FROM users WHERE username = ? AND id != ?";
    $result = $db->single($sql, [$username, $id]);

    if ($result['count'] > 0) {
        $errors[] = 'Ce nom d\'utilisateur est déjà utilisé.';
    }

    // Vérifier si l'email existe déjà (sauf pour cet utilisateur)
    $sql = "SELECT COUNT(*) as count FROM users WHERE email = ? AND id != ?";
    $result = $db->single($sql, [$email, $id]);

    if ($result['count'] > 0) {
        $errors[] = 'Cette adresse e-mail est déjà utilisée.';
    }

    // Si aucune erreur, mettre à jour l'utilisateur
    if (empty($errors)) {
        // Préparer la requête SQL
        if (!empty($password)) {
            // Hacher le nouveau mot de passe
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            $sql = "UPDATE users
                    SET username = ?, email = ?, password = ?, role = ?, active = ?, updated_at = NOW()
                    WHERE id = ?";
            $params = [$username, $email, $hashed_password, $role, $active, $id];
        } else {
            // Ne pas modifier le mot de passe
            $sql = "UPDATE users
                    SET username = ?, email = ?, role = ?, active = ?, updated_at = NOW()
                    WHERE id = ?";
            $params = [$username, $email, $role, $active, $id];
        }

        if ($db->query($sql, $params)) {
            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Modification d\'un utilisateur', 'ID: ' . $id . ', Utilisateur: ' . $username);

            redirect('index.php?page=admin/users', 'Utilisateur modifié avec succès.', 'success');
        } else {
            $errors[] = 'Erreur lors de la modification de l\'utilisateur.';
        }
    }
}

// Récupérer les données du formulaire en cas d'erreur
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $form_data = [
        'username' => $_POST['username'] ?? '',
        'email' => $_POST['email'] ?? '',
        'role' => $_POST['role'] ?? '',
        'active' => isset($_POST['active']) ? 1 : 0
    ];
} else {
    $form_data = [
        'username' => $user['username'],
        'email' => $user['email'],
        'role' => $user['role'],
        'active' => $user['active']
    ];
}

// Charger la vue
require_once VIEWS_PATH . 'admin/users/edit.php';
?>
