<?php
// Contrôleur pour l'impression d'un lot

// Vérifier si l'ID du lot est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=lots', 'ID du lot non spécifié.', 'danger');
}

$lot_id = intval($_GET['id']);

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations du lot
$sql = "SELECT l.*, c.nom as cdm_nom, c.adresse as cdm_adresse, u.username as created_by_username
        FROM lots l
        JOIN cdm c ON l.cdm_id = c.id
        JOIN users u ON l.created_by = u.id
        WHERE l.id = ?";
$lot = $db->single($sql, [$lot_id]);

// Vérifier si le lot existe
if (!$lot) {
    redirect('index.php?page=lots', 'Lot non trouvé.', 'danger');
}

// Titre de la page
$page_title = 'Impression - Lot #' . $lot['numero_lot'];
$page = 'lots/print';

// Récupérer les dossiers associés à ce lot
$sql = "SELECT d.*, a.nom as acte_nom
        FROM dossiers d
        LEFT JOIN actes a ON d.acte_id = a.id
        WHERE d.lot_id = ?
        ORDER BY d.created_at ASC";
$dossiers = $db->all($sql, [$lot_id]);

// Statistiques du lot
$total_dossiers = count($dossiers);
$dossiers_recus = 0;
$dossiers_en_attente = 0;
$montant_total = 0;

foreach ($dossiers as $dossier) {
    if ($dossier['recu']) {
        $dossiers_recus++;
    } else {
        $dossiers_en_attente++;
    }
    $montant_total += $dossier['montant'];
}

// Charger la vue
require_once VIEWS_PATH . 'lots/print.php';
?>
