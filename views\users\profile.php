<?php
// Vue pour la page de profil utilisateur
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Mon profil</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($form_errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($form_errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <form method="post" action="index.php?page=profile">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Nom d'utilisateur</label>
                            <input type="text" class="form-control" id="username" value="<?php echo $user['username']; ?>" readonly>
                            <div class="form-text text-muted">Le nom d'utilisateur ne peut pas être modifié.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Adresse e-mail</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo $form_data['email']; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="role" class="form-label">Rôle</label>
                            <input type="text" class="form-control" id="role" value="<?php echo $user['role'] === 'admin' ? 'Administrateur' : 'Utilisateur'; ?>" readonly>
                            <div class="form-text text-muted">Le rôle ne peut pas être modifié.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="last_login" class="form-label">Dernière connexion</label>
                            <input type="text" class="form-control" id="last_login" value="<?php echo !empty($user['last_login']) ? formatDate($user['last_login'], DATETIME_FORMAT) : 'Jamais'; ?>" readonly>
                        </div>
                        
                        <hr>
                        
                        <h5 class="mb-3">Changer le mot de passe</h5>
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Mot de passe actuel</label>
                            <input type="password" class="form-control" id="current_password" name="current_password">
                            <div class="form-text text-muted">Laissez vide si vous ne souhaitez pas changer de mot de passe.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">Nouveau mot de passe</label>
                            <input type="password" class="form-control" id="new_password" name="new_password">
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirmer le nouveau mot de passe</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
