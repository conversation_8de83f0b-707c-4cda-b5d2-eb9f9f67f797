<?php
// Vue pour la modification d'un CDM
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-hospital"></i> Modifier un CDM</h1>
        <div>
            <a href="index.php?page=admin/cdm" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>
    
    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header bg-warning text-dark">
            <h5 class="mb-0"><i class="fas fa-edit"></i> Informations du CDM</h5>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=admin/cdm/edit&id=<?php echo $cdm['id']; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="nom" class="form-label">Nom du CDM <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nom" name="nom" value="<?php echo isset($_POST['nom']) ? $_POST['nom'] : $cdm['nom']; ?>" required>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="active" class="form-label">Statut</label>
                        <div class="form-check form-switch mt-2">
                            <input class="form-check-input" type="checkbox" id="active" name="active" <?php echo (isset($_POST['active']) ? $_POST['active'] : $cdm['active']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="active">Actif</label>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="adresse" class="form-label">Adresse <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="adresse" name="adresse" rows="3" required><?php echo isset($_POST['adresse']) ? $_POST['adresse'] : $cdm['adresse']; ?></textarea>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="contact_nom" class="form-label">Nom du contact</label>
                        <input type="text" class="form-control" id="contact_nom" name="contact_nom" value="<?php echo isset($_POST['contact_nom']) ? $_POST['contact_nom'] : $cdm['contact_nom']; ?>">
                    </div>
                    
                    <div class="col-md-4">
                        <label for="telephone" class="form-label">Téléphone</label>
                        <input type="text" class="form-control" id="telephone" name="telephone" value="<?php echo isset($_POST['telephone']) ? $_POST['telephone'] : $cdm['telephone']; ?>">
                    </div>
                    
                    <div class="col-md-4">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($_POST['email']) ? $_POST['email'] : $cdm['email']; ?>">
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="index.php?page=admin/cdm" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Mettre à jour
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
