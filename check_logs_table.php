<?php
// Script pour vérifier la structure de la table logs
require_once 'config/config.php';
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    
    // Vérifier la structure de la table logs
    $sql = "DESCRIBE logs";
    $columns = $db->all($sql);
    
    echo "<h2>Structure de la table logs</h2>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Vérifier les contraintes de clé étrangère
    $sql = "SELECT * FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND REFERENCED_TABLE_NAME IS NOT NULL";
    $constraints = $db->all($sql, [DB_NAME, 'logs']);
    
    echo "<h2>Contraintes de clé étrangère</h2>";
    echo "<pre>";
    print_r($constraints);
    echo "</pre>";
    
    // Vérifier les utilisateurs existants
    $sql = "SELECT id, username FROM users LIMIT 10";
    $users = $db->all($sql);
    
    echo "<h2>Utilisateurs existants</h2>";
    echo "<pre>";
    print_r($users);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage();
}
?>
