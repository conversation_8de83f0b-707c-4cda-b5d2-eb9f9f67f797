<?php
// Vue pour la recherche de dossiers retournés
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-search"></i> Recherche de dossiers retournés</h1>
        <div>
            <button id="printPage" class="btn btn-outline-secondary">
                <i class="fas fa-print"></i> Imprimer les résultats
            </button>
        </div>
    </div>
    
    <!-- Formulaire de recherche -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-filter"></i> Critères de recherche</h5>
        </div>
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="retours/search">
                
                <div class="col-md-4">
                    <label for="search_type" class="form-label">Type de recherche</label>
                    <select class="form-select" id="search_type" name="search_type">
                        <option value="dossier" <?php echo $search_type === 'dossier' ? 'selected' : ''; ?>>Dossier (N° dossier, nom, bénéficiaire)</option>
                        <option value="bordereau" <?php echo $search_type === 'bordereau' ? 'selected' : ''; ?>>Bordereau (N° bordereau)</option>
                        <option value="adherent" <?php echo $search_type === 'adherent' ? 'selected' : ''; ?>>Adhérent (N° adhérent exact)</option>
                    </select>
                </div>
                
                <div class="col-md-8">
                    <label for="search_term" class="form-label">Terme de recherche</label>
                    <input type="text" class="form-control" id="search_term" name="search_term" value="<?php echo $search_term; ?>" placeholder="Entrez votre terme de recherche...">
                </div>
                
                <div class="col-12">
                    <button type="button" class="btn btn-outline-primary" id="toggleAdvancedSearch">
                        <i class="fas fa-plus"></i> Afficher les filtres avancés
                    </button>
                </div>
                
                <div class="col-12 d-none" id="advancedFilters">
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="cdm_id" class="form-label">CDM</label>
                                    <select class="form-select" id="cdm_id" name="cdm_id">
                                        <option value="0">Tous les CDM</option>
                                        <?php foreach ($cdm_list as $cdm): ?>
                                            <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm_id == $cdm['id'] ? 'selected' : ''; ?>>
                                                <?php echo $cdm['nom']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="raison_id" class="form-label">Motif de retour</label>
                                    <select class="form-select" id="raison_id" name="raison_id">
                                        <option value="0">Tous les motifs</option>
                                        <?php foreach ($raisons_list as $raison): ?>
                                            <option value="<?php echo $raison['id']; ?>" <?php echo $raison_id == $raison['id'] ? 'selected' : ''; ?>>
                                                <?php echo $raison['raison']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="corrige" class="form-label">Statut</label>
                                    <select class="form-select" id="corrige" name="corrige">
                                        <option value="">Tous les statuts</option>
                                        <option value="1" <?php echo $corrige === '1' ? 'selected' : ''; ?>>Corrigés</option>
                                        <option value="0" <?php echo $corrige === '0' ? 'selected' : ''; ?>>Non corrigés</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="date_debut" class="form-label">Date début</label>
                                    <input type="text" class="form-control datepicker" id="date_debut" name="date_debut" value="<?php echo $date_debut; ?>" placeholder="jj/mm/aaaa">
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="date_fin" class="form-label">Date fin</label>
                                    <input type="text" class="form-control datepicker" id="date_fin" name="date_fin" value="<?php echo $date_fin; ?>" placeholder="jj/mm/aaaa">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 text-end">
                    <button type="reset" class="btn btn-secondary" id="resetFilters">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Rechercher
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Résultats de recherche -->
    <?php if (!empty($results)): ?>
        <div class="card">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Résultats de recherche</h5>
                    <span class="badge bg-primary"><?php echo $total_results; ?> résultat(s) trouvé(s)</span>
                </div>
            </div>
            <div class="card-body">
                <?php if ($total_results > 0): ?>
                    <?php if ($search_type === 'dossier' || $search_type === 'adherent'): ?>
                        <!-- Résultats de recherche pour les dossiers -->
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>N° Bordereau</th>
                                        <th>CDM</th>
                                        <th>N° Dossier</th>
                                        <th>N° Adhérent</th>
                                        <th>Nom</th>
                                        <th>Motif de retour</th>
                                        <th>Date retour</th>
                                        <th>Statut</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results as $dossier): ?>
                                        <tr>
                                            <td>
                                                <a href="index.php?page=retours/view&numero=<?php echo $dossier['numero_bordereau']; ?>&cdm_id=<?php echo $dossier['cdm_id']; ?>" class="text-decoration-none">
                                                    <?php echo $dossier['numero_bordereau']; ?>
                                                </a>
                                            </td>
                                            <td><?php echo $dossier['cdm_nom']; ?></td>
                                            <td><?php echo $dossier['numero_dossier']; ?></td>
                                            <td><?php echo $dossier['numero_adherent']; ?></td>
                                            <td><?php echo $dossier['nom']; ?></td>
                                            <td><?php echo $dossier['raison_retour_nom']; ?></td>
                                            <td><?php echo formatDate($dossier['date_retour']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $dossier['corrige'] ? 'success' : 'danger'; ?>">
                                                    <?php echo $dossier['corrige'] ? 'Corrigé' : 'Non corrigé'; ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <a href="index.php?page=dossiers/edit_retour&id=<?php echo $dossier['id']; ?>" class="btn btn-sm btn-warning btn-action" data-bs-toggle="tooltip" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="index.php?page=retours/view&numero=<?php echo $dossier['numero_bordereau']; ?>&cdm_id=<?php echo $dossier['cdm_id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir le bordereau">
                                                    <i class="fas fa-folder-open"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php elseif ($search_type === 'bordereau'): ?>
                        <!-- Résultats de recherche pour les bordereaux -->
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>N° Bordereau</th>
                                        <th>CDM</th>
                                        <th>Date bordereau</th>
                                        <th>Dossiers</th>
                                        <th>Corrigés</th>
                                        <th>Progression</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results as $bordereau): ?>
                                        <tr>
                                            <td><?php echo $bordereau['numero_bordereau']; ?></td>
                                            <td><?php echo $bordereau['cdm_nom']; ?></td>
                                            <td><?php echo formatDate($bordereau['date_bordereau']); ?></td>
                                            <td><?php echo $bordereau['nb_dossiers']; ?></td>
                                            <td><?php echo $bordereau['nb_corriges']; ?></td>
                                            <td>
                                                <?php 
                                                    $progress = ($bordereau['nb_dossiers'] > 0) ? 
                                                                ($bordereau['nb_corriges'] / $bordereau['nb_dossiers']) * 100 : 0;
                                                ?>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-success" role="progressbar" 
                                                         style="width: <?php echo $progress; ?>%;" 
                                                         aria-valuenow="<?php echo $progress; ?>" 
                                                         aria-valuemin="0" aria-valuemax="100">
                                                        <?php echo round($progress); ?>%
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="text-end">
                                                <a href="index.php?page=retours/view&numero=<?php echo $bordereau['numero_bordereau']; ?>&cdm_id=<?php echo $bordereau['cdm_id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="index.php?page=retours/edit&numero=<?php echo $bordereau['numero_bordereau']; ?>&cdm_id=<?php echo $bordereau['cdm_id']; ?>" class="btn btn-sm btn-warning btn-action" data-bs-toggle="tooltip" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="index.php?page=retours/print&numero=<?php echo $bordereau['numero_bordereau']; ?>&cdm_id=<?php echo $bordereau['cdm_id']; ?>" class="btn btn-sm btn-info btn-action" data-bs-toggle="tooltip" title="Imprimer">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($total_results >= 100): ?>
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle"></i> Les résultats sont limités aux 100 premiers enregistrements. Veuillez affiner votre recherche pour des résultats plus précis.
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Aucun résultat ne correspond à votre recherche. Veuillez essayer avec d'autres termes ou critères.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php elseif (isset($_GET['search_term']) || $cdm_id > 0 || $raison_id > 0 || $corrige !== '' || !empty($date_debut) || !empty($date_fin)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> Aucun résultat ne correspond à votre recherche. Veuillez essayer avec d'autres termes ou critères.
        </div>
    <?php endif; ?>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
