<?php
/**
 * Script pour réinitialiser la base de données
 * Supprime toutes les données sauf les utilisateurs
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Vérifier si l'utilisateur est connecté et est admin
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    die('Accès refusé. Seuls les administrateurs peuvent réinitialiser la base de données.');
}

try {
    // Obtenir la connexion à la base de données
    $db = Database::getInstance();
    
    echo "<h2>Réinitialisation de la base de données en cours...</h2>\n";
    echo "<pre>\n";
    
    // Lire et exécuter le script SQL
    $sql_file = 'sql/reset_database.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("Le fichier SQL n'existe pas: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    if ($sql_content === false) {
        throw new Exception("Impossible de lire le fichier SQL: $sql_file");
    }
    
    // Diviser le contenu en requêtes individuelles
    $queries = explode(';', $sql_content);
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($queries as $query) {
        $query = trim($query);
        
        // Ignorer les requêtes vides et les commentaires
        if (empty($query) || strpos($query, '--') === 0) {
            continue;
        }
        
        try {
            $result = $db->query($query);
            
            // Si c'est une requête SELECT, afficher le résultat
            if (stripos($query, 'SELECT') === 0) {
                $data = $result->fetchAll(PDO::FETCH_ASSOC);
                if (!empty($data)) {
                    foreach ($data as $row) {
                        foreach ($row as $value) {
                            echo "✅ $value\n";
                        }
                    }
                }
            } else {
                echo "✅ Requête exécutée avec succès: " . substr($query, 0, 50) . "...\n";
            }
            
            $success_count++;
            
        } catch (Exception $e) {
            echo "❌ Erreur dans la requête: " . substr($query, 0, 50) . "...\n";
            echo "   Erreur: " . $e->getMessage() . "\n";
            $error_count++;
        }
    }
    
    echo "\n";
    echo "=== RÉSUMÉ ===\n";
    echo "Requêtes réussies: $success_count\n";
    echo "Requêtes échouées: $error_count\n";
    
    if ($error_count === 0) {
        echo "\n🎉 Base de données réinitialisée avec succès!\n";
        echo "\nLes données suivantes ont été conservées:\n";
        echo "- Utilisateurs et leurs mots de passe\n";
        echo "- Rôles et permissions\n";
        
        echo "\nLes données suivantes ont été réinitialisées:\n";
        echo "- CDM (5 centres d'exemple)\n";
        echo "- Actes médicaux (25 actes d'exemple)\n";
        echo "- Raisons de retour (12 raisons d'exemple)\n";
        echo "- Lots (3 lots d'exemple)\n";
        echo "- Dossiers (5 dossiers d'exemple)\n";
        echo "- Bordereaux de retour (2 bordereaux d'exemple)\n";
        echo "- Dossiers retournés (3 dossiers retournés d'exemple)\n";
        
        echo "\n<a href='index.php'>🏠 Retour à l'accueil</a>\n";
        
    } else {
        echo "\n⚠️ La réinitialisation s'est terminée avec des erreurs.\n";
        echo "Veuillez vérifier les erreurs ci-dessus et corriger le script SQL si nécessaire.\n";
    }
    
    echo "</pre>\n";
    
} catch (Exception $e) {
    echo "<h2>❌ Erreur lors de la réinitialisation</h2>\n";
    echo "<pre>\n";
    echo "Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
    echo "</pre>\n";
    echo "\n<a href='index.php'>🏠 Retour à l'accueil</a>\n";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Réinitialisation de la base de données</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        pre {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            line-height: 1.4;
        }
        
        a {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        a:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
</body>
</html>
