<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        body {
            font-family: 'Arial', sans-serif;
            font-size: 9pt;
            line-height: 1.1;
        }

        .container {
            padding: 10px;
        }

        .header-title {
            font-size: 14pt;
            font-weight: 500;
            margin-bottom: 3px;
            text-align: center;
        }

        .subtitle {
            font-size: 12pt;
            font-weight: 400;
            margin-bottom: 3px;
            text-align: center;
        }

        .print-date {
            font-size: 8pt;
            text-align: right;
            margin-bottom: 10px;
        }

        .section-title {
            font-size: 10pt;
            font-weight: 500;
            margin-bottom: 3px;
            padding: 2px 3px;
            background-color: #f0f0f0;
            border-bottom: 1px solid #ddd;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }

        .info-table th, .info-table td {
            padding: 2px 3px;
            border: 1px solid #ddd;
            font-size: 8pt;
            line-height: 1;
        }

        .info-table th {
            background-color: #f8f9fa;
            width: 30%;
            font-weight: 500;
        }

        .dossiers-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            table-layout: fixed;
            page-break-inside: auto;
        }

        /* Assurer que les en-têtes se répètent sur chaque page */
        .dossiers-table thead {
            display: table-header-group;
        }

        .dossiers-table tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        .dossiers-table th, .dossiers-table td {
            padding: 1px 2px; /* Réduit davantage l'espacement */
            border: 1px solid #ddd;
            font-size: 8pt; /* Réduit la taille de la police */
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1; /* Réduit davantage la hauteur des lignes */
            white-space: nowrap; /* Empêche le retour à la ligne */
            max-height: 12px; /* Limite la hauteur des cellules */
        }

        .dossiers-table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }

        .dossiers-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        /* Largeurs des colonnes */
        .dossiers-table th:nth-child(1), .dossiers-table td:nth-child(1) { /* # */
            width: 3%;
        }
        .dossiers-table th:nth-child(2), .dossiers-table td:nth-child(2) { /* N° Dossier */
            width: 10%;
        }
        .dossiers-table th:nth-child(3), .dossiers-table td:nth-child(3) { /* N° Adhérent */
            width: 10%;
        }
        .dossiers-table th:nth-child(4), .dossiers-table td:nth-child(4) { /* Nom */
            width: 20%;
        }
        .dossiers-table th:nth-child(5), .dossiers-table td:nth-child(5) { /* Bénéficiaire */
            width: 12%;
        }
        .dossiers-table th:nth-child(6), .dossiers-table td:nth-child(6) { /* Montant */
            width: 10%;
        }
        .dossiers-table th:nth-child(7), .dossiers-table td:nth-child(7) { /* Raisons du retour */
            width: 25%;
        }
        .dossiers-table th:nth-child(8), .dossiers-table td:nth-child(8) { /* Statut */
            width: 10%;
        }

        .footer {
            font-size: 8pt;
            text-align: center;
            margin-top: 10px;
            border-top: 1px solid #ddd;
            padding-top: 5px;
        }

        .signatures {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
        }

        .signature-box {
            width: 45%;
            border-top: 1px solid #ddd;
            padding-top: 3px;
            text-align: center;
            font-size: 8pt;
        }

        @media print {
            body {
                font-size: 8pt;
                line-height: 1;
            }

            .no-print {
                display: none !important;
            }

            .container {
                width: 100%;
                max-width: 100%;
                padding: 5px;
                margin: 0;
            }

            .page-break {
                page-break-after: always;
            }

            /* Réduire les marges d'impression */
            @page {
                margin: 0.3cm;
                size: A4 portrait;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Boutons d'action (non imprimables) -->
        <div class="d-flex justify-content-between align-items-center mb-4 no-print">
            <h1><i class="fas fa-print"></i> Impression - Bordereau #<?php echo $numero_bordereau; ?></h1>
            <div>
                <button onclick="window.print();" class="btn btn-primary">
                    <i class="fas fa-print"></i> Imprimer
                </button>
                <div class="btn-group">
                    <a href="index.php?page=retours/export&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>&format=csv" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Exporter Excel
                    </a>
                    <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="visually-hidden">Toggle Dropdown</span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php?page=retours/export&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>&format=csv">Format CSV</a></li>
                        <li><a class="dropdown-item" href="index.php?page=retours/export&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>&format=xlsx">Format XLSX</a></li>
                    </ul>
                </div>
                <a href="index.php?page=retours/view&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour
                </a>
            </div>
        </div>

        <!-- En-tête du document -->
        <div class="mb-2 position-relative">
            <!-- Logo MGEN à gauche -->
            <div style="position: absolute; top: 0; left: 0; width: 50px; height: 50px;">
                <img src="MGEN.jpg" alt="MGEN Logo" style="width: 50px; height: 50px;">
            </div>

            <div class="text-center">
                <div class="header-title">Mutuelle Générale de l'Education National</div>
                <div class="subtitle" style="text-align: left; margin-left: 60px;">Gestion des Lots CDM</div>
                <div class="subtitle" style="text-align: left; margin-left: 60px;">Bordereau de Retour #<?php echo $numero_bordereau; ?></div>
                <div class="print-date">Imprimé le <?php echo date(DATETIME_FORMAT); ?></div>
            </div>
        </div>

        <!-- Informations du bordereau -->
        <div class="mb-3">
            <div class="section-title"><i class="fas fa-info-circle"></i> Informations du bordereau</div>
            <table class="info-table">
                <tbody>
                    <tr>
                        <th>Numéro de bordereau</th>
                        <td><?php echo $numero_bordereau; ?></td>
                        <th>Date du bordereau</th>
                        <td><?php echo formatDate($bordereau_info['date_bordereau']); ?></td>
                    </tr>
                    <tr>
                        <th>CDM</th>
                        <td><?php echo $cdm['nom']; ?></td>
                        <th>Total dossiers</th>
                        <td><?php echo $total_dossiers; ?></td>
                    </tr>
                    <tr>
                        <th>Dossiers corrigés</th>
                        <td><?php echo $dossiers_corriges; ?></td>
                        <th></th>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Dossiers non corrigés</th>
                        <td><?php echo $dossiers_non_corriges; ?></td>
                        <th>Montant total</th>
                        <td><?php echo number_format($montant_total, 2, ',', ' '); ?> DH</td>
                    </tr>
                    <?php if (!empty($bordereau_info['notes'])): ?>
                    <tr>
                        <th>Notes</th>
                        <td colspan="3"><?php echo nl2br(htmlspecialchars($bordereau_info['notes'])); ?></td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Liste des dossiers retournés -->
        <div>
            <div class="section-title"><i class="fas fa-folder"></i> Liste des dossiers retournés (<?php echo count($dossiers); ?>)</div>
            <?php if (count($dossiers) > 0): ?>
                <table class="dossiers-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>N° Dossier</th>
                            <th>N° Adhérent</th>
                            <th>Nom</th>
                            <th>Bénéficiaire</th>
                            <th>Montant</th>
                            <th>Raisons du retour</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($dossiers as $index => $dossier): ?>
                            <tr>
                                <td><?php echo $index + 1; ?></td>
                                <td><?php echo $dossier['numero_dossier']; ?></td>
                                <td><?php echo $dossier['numero_adherent']; ?></td>
                                <td><?php echo $dossier['nom']; ?></td>
                                <td><?php echo $dossier['beneficiaire']; ?></td>
                                <td><?php echo number_format($dossier['montant'], 2, ',', ' '); ?> DH</td>
                                <td>
                                    <?php echo $dossier['raisons_str']; ?>
                                    <?php if (!empty($dossier['notes'])): ?>
                                        <br><small><strong>Notes:</strong> <?php echo nl2br(htmlspecialchars($dossier['notes'])); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo $dossier['corrige'] ? 'Corrigé' : 'Non corrigé'; ?>
                                    <?php if ($dossier['corrige'] && !empty($dossier['date_correction'])): ?>
                                        (<?php echo formatDate($dossier['date_correction']); ?>)
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <!-- Total des montants -->
                <div style="margin-top: 10px; text-align: right; font-weight: bold; font-size: 11pt;">
                    Montant total: <?php echo number_format($montant_total, 2, ',', ' '); ?> DH
                </div>
            <?php else: ?>
                <div style="border: 1px solid #ddd; padding: 5px; margin-top: 5px; font-size: 9pt;">
                    <i class="fas fa-info-circle"></i> Aucun dossier n'a été ajouté à ce bordereau.
                </div>
            <?php endif; ?>
        </div>

        <!-- Signatures -->
        <div class="signatures">
            <div class="signature-box">
                <p>Signature du responsable CDM</p>
            </div>
            <div class="signature-box">
                <p>Signature du responsable traitement</p>
            </div>
        </div>

        <!-- Pied de page -->
        <div class="footer">
            <p>Page 1/1</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Auto-print -->
    <script>
        // Imprimer automatiquement après 1 seconde
        setTimeout(function() {
            window.print();
        }, 1000);
    </script>
</body>
</html>
