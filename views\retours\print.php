<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 10pt;
            line-height: 1.3;
            color: #333;
            background: #fff;
        }

        .container {
            padding: 15px;
            max-width: 100%;
        }

        .header-section {
            border-bottom: 2px solid #0066cc;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .header-title {
            font-size: 18pt;
            font-weight: 600;
            margin-bottom: 5px;
            color: #0066cc;
            text-align: center;
        }

        .subtitle {
            font-size: 14pt;
            font-weight: 400;
            margin-bottom: 5px;
            color: #666;
        }

        .print-date {
            font-size: 9pt;
            text-align: right;
            margin-bottom: 15px;
            color: #888;
        }

        .section-title {
            font-size: 12pt;
            font-weight: 600;
            margin: 15px 0 8px 0;
            padding: 8px 12px;
            background: linear-gradient(135deg, #0066cc, #004499);
            color: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 6px;
            overflow: hidden;
        }

        .info-table th, .info-table td {
            padding: 8px 12px;
            border: 1px solid #e0e0e0;
            font-size: 9pt;
            line-height: 1.4;
        }

        .info-table th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: 600;
            color: #495057;
            width: 25%;
        }

        .info-table td {
            background: #fff;
        }

        .dossiers-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            table-layout: auto;
            page-break-inside: auto;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 6px;
            overflow: hidden;
        }

        .dossiers-table thead {
            display: table-header-group;
        }

        .dossiers-table tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        .dossiers-table th, .dossiers-table td {
            padding: 6px 8px;
            border: 1px solid #e0e0e0;
            font-size: 8.5pt;
            line-height: 1.3;
            vertical-align: top;
            word-wrap: break-word;
        }

        .dossiers-table th {
            background: linear-gradient(135deg, #0066cc, #004499);
            color: white;
            font-weight: 600;
            text-align: center;
        }

        .dossiers-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .dossiers-table tr:hover {
            background-color: #e3f2fd;
        }

        /* Largeurs optimisées des colonnes */
        .dossiers-table th:nth-child(1), .dossiers-table td:nth-child(1) { /* # */
            width: 4%;
            text-align: center;
        }
        .dossiers-table th:nth-child(2), .dossiers-table td:nth-child(2) { /* N° Dossier */
            width: 12%;
        }
        .dossiers-table th:nth-child(3), .dossiers-table td:nth-child(3) { /* N° Adhérent */
            width: 12%;
        }
        .dossiers-table th:nth-child(4), .dossiers-table td:nth-child(4) { /* Nom */
            width: 18%;
        }
        .dossiers-table th:nth-child(5), .dossiers-table td:nth-child(5) { /* Bénéficiaire */
            width: 15%;
        }
        .dossiers-table th:nth-child(6), .dossiers-table td:nth-child(6) { /* Montant */
            width: 10%;
            text-align: right;
        }
        .dossiers-table th:nth-child(7), .dossiers-table td:nth-child(7) { /* Motifs de retour */
            width: 20%;
            white-space: normal;
            word-wrap: break-word;
        }
        .dossiers-table th:nth-child(8), .dossiers-table td:nth-child(8) { /* Statut */
            width: 9%;
            text-align: center;
        }

        .motif-cell {
            font-size: 8pt;
            line-height: 1.2;
            max-height: none;
            white-space: normal;
            word-wrap: break-word;
        }

        .status-cell {
            font-weight: 600;
            text-align: center;
        }

        .status-corrected {
            color: #28a745;
        }

        .status-pending {
            color: #dc3545;
        }

        .total-section {
            margin-top: 15px;
            padding: 10px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 6px;
            text-align: right;
            font-weight: 600;
            font-size: 12pt;
            color: #0066cc;
        }

        .signatures {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }

        .signature-box {
            flex: 1;
            border: 2px solid #0066cc;
            border-radius: 6px;
            padding: 20px 10px;
            text-align: center;
            font-size: 10pt;
            font-weight: 600;
            color: #0066cc;
            min-height: 60px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
        }

        .footer {
            font-size: 9pt;
            text-align: center;
            margin-top: 20px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            color: #666;
        }

        @media print {
            body {
                font-size: 9pt;
                line-height: 1.2;
            }

            .no-print {
                display: none !important;
            }

            .container {
                width: 100%;
                max-width: 100%;
                padding: 10px;
                margin: 0;
            }

            .page-break {
                page-break-after: always;
            }

            .dossiers-table th, .dossiers-table td {
                font-size: 8pt;
                padding: 4px 6px;
            }

            .motif-cell {
                font-size: 7.5pt;
            }

            @page {
                margin: 0.5cm;
                size: A4 portrait;
            }

            .signatures {
                margin-top: 20px;
            }

            .signature-box {
                min-height: 40px;
                padding: 15px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Boutons d'action (non imprimables) -->
        <div class="d-flex justify-content-between align-items-center mb-4 no-print">
            <h1><i class="fas fa-print"></i> Impression - Bordereau #<?php echo $numero_bordereau; ?></h1>
            <div>
                <button onclick="window.print();" class="btn btn-primary">
                    <i class="fas fa-print"></i> Imprimer
                </button>
                <div class="btn-group">
                    <a href="index.php?page=retours/export&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>&format=csv" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Exporter Excel
                    </a>
                    <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="visually-hidden">Toggle Dropdown</span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php?page=retours/export&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>&format=csv">Format CSV</a></li>
                        <li><a class="dropdown-item" href="index.php?page=retours/export&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>&format=xlsx">Format XLSX</a></li>
                    </ul>
                </div>
                <a href="index.php?page=retours/view&numero=<?php echo $numero_bordereau; ?>&cdm_id=<?php echo $cdm_id; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour
                </a>
            </div>
        </div>

        <!-- En-tête du document -->
        <div class="header-section">
            <div class="d-flex align-items-center">
                <!-- Logo MGEN à gauche -->
                <div style="flex-shrink: 0; margin-right: 20px;">
                    <img src="MGEN.jpg" alt="MGEN Logo" style="width: 60px; height: 60px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                </div>

                <div style="flex-grow: 1;">
                    <div class="header-title">Mutuelle Générale de l'Education Nationale</div>
                    <div class="subtitle" style="color: #0066cc; font-weight: 500;">Système de Gestion des Lots CDM</div>
                    <div class="subtitle" style="color: #666; font-size: 12pt;">Bordereau de Retour N° <?php echo $numero_bordereau; ?></div>
                </div>
            </div>
            <div class="print-date">Document généré le <?php echo date('d/m/Y à H:i'); ?></div>
        </div>

        <!-- Informations du bordereau -->
        <div class="mb-3">
            <div class="section-title"><i class="fas fa-info-circle"></i> Informations du bordereau</div>
            <table class="info-table">
                <tbody>
                    <tr>
                        <th>Numéro de bordereau</th>
                        <td><?php echo $numero_bordereau; ?></td>
                        <th>Date du bordereau</th>
                        <td><?php echo formatDate($bordereau_info['date_bordereau']); ?></td>
                    </tr>
                    <tr>
                        <th>CDM</th>
                        <td><?php echo $cdm['nom']; ?></td>
                        <th>Total dossiers</th>
                        <td><?php echo $total_dossiers; ?></td>
                    </tr>
                    <tr>
                        <th>Dossiers corrigés</th>
                        <td><?php echo $dossiers_corriges; ?></td>
                        <th></th>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Dossiers non corrigés</th>
                        <td><?php echo $dossiers_non_corriges; ?></td>
                        <th>Montant total</th>
                        <td><?php echo number_format($montant_total, 2, ',', ' '); ?> DH</td>
                    </tr>
                    <?php if (!empty($bordereau_info['notes'])): ?>
                    <tr>
                        <th>Notes</th>
                        <td colspan="3"><?php echo nl2br(htmlspecialchars($bordereau_info['notes'])); ?></td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Liste des dossiers retournés -->
        <div>
            <div class="section-title"><i class="fas fa-folder"></i> Liste des dossiers retournés (<?php echo count($dossiers); ?>)</div>
            <?php if (count($dossiers) > 0): ?>
                <table class="dossiers-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>N° Dossier</th>
                            <th>N° Adhérent</th>
                            <th>Nom</th>
                            <th>Bénéficiaire</th>
                            <th>Montant</th>
                            <th>Motifs de retour</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($dossiers as $index => $dossier): ?>
                            <tr>
                                <td style="text-align: center; font-weight: 600;"><?php echo $index + 1; ?></td>
                                <td style="font-weight: 500;"><?php echo htmlspecialchars($dossier['numero_dossier']); ?></td>
                                <td><?php echo htmlspecialchars($dossier['numero_adherent']); ?></td>
                                <td><?php echo htmlspecialchars($dossier['nom']); ?></td>
                                <td><?php echo htmlspecialchars($dossier['beneficiaire']); ?></td>
                                <td style="text-align: right; font-weight: 500;"><?php echo number_format($dossier['montant'], 2, ',', ' '); ?> DH</td>
                                <td class="motif-cell">
                                    <div style="line-height: 1.3;">
                                        <?php echo htmlspecialchars($dossier['raisons_str']); ?>
                                    </div>
                                    <?php if (!empty($dossier['notes'])): ?>
                                        <div style="margin-top: 4px; padding-top: 4px; border-top: 1px solid #eee; font-size: 7.5pt; color: #666;">
                                            <strong>Notes:</strong> <?php echo nl2br(htmlspecialchars($dossier['notes'])); ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="status-cell <?php echo $dossier['corrige'] ? 'status-corrected' : 'status-pending'; ?>">
                                    <div style="font-weight: 600;">
                                        <?php echo $dossier['corrige'] ? '✓ Corrigé' : '✗ En attente'; ?>
                                    </div>
                                    <?php if ($dossier['corrige'] && !empty($dossier['date_correction'])): ?>
                                        <div style="font-size: 7pt; color: #666; margin-top: 2px;">
                                            <?php echo formatDate($dossier['date_correction']); ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <!-- Résumé et totaux -->
                <div class="total-section">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Résumé:</strong><br>
                            <span style="color: #28a745;">✓ Dossiers corrigés: <?php echo $dossiers_corriges; ?></span><br>
                            <span style="color: #dc3545;">✗ Dossiers en attente: <?php echo $dossiers_non_corriges; ?></span>
                        </div>
                        <div class="col-md-6 text-end">
                            <strong>Montant total: <?php echo number_format($montant_total, 2, ',', ' '); ?> DH</strong>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div style="border: 2px dashed #ddd; padding: 20px; margin-top: 10px; text-align: center; font-size: 11pt; color: #666; border-radius: 8px;">
                    <i class="fas fa-info-circle" style="font-size: 24pt; color: #ccc; margin-bottom: 10px;"></i><br>
                    <strong>Aucun dossier trouvé</strong><br>
                    Ce bordereau ne contient aucun dossier retourné.
                </div>
            <?php endif; ?>
        </div>

        <!-- Signatures -->
        <div class="signatures">
            <div class="signature-box">
                <div>
                    <div style="margin-bottom: 10px; font-weight: 600;">Responsable CDM</div>
                    <div style="font-size: 9pt; color: #666;">Nom et signature</div>
                </div>
            </div>
            <div class="signature-box">
                <div>
                    <div style="margin-bottom: 10px; font-weight: 600;">Responsable Traitement</div>
                    <div style="font-size: 9pt; color: #666;">Nom et signature</div>
                </div>
            </div>
        </div>

        <!-- Pied de page -->
        <div class="footer">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <strong><?php echo APP_NAME; ?></strong> - Système de Gestion des Lots CDM
                </div>
                <div>
                    Page 1/1 - <?php echo count($dossiers); ?> dossier(s)
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Auto-print -->
    <script>
        // Imprimer automatiquement après 1 seconde
        setTimeout(function() {
            window.print();
        }, 1000);
    </script>
</body>
</html>
