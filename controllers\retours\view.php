<?php
// Contrôleur pour afficher les détails d'un bordereau de dossiers retournés

// Vérifier si les paramètres du bordereau sont fournis
if (!isset($_GET['numero']) || empty($_GET['numero']) || !isset($_GET['cdm_id']) || empty($_GET['cdm_id'])) {
    redirect('index.php?page=retours', 'Paramètres du bordereau non spécifiés.', 'danger');
}

$numero_bordereau = intval($_GET['numero']);
$cdm_id = intval($_GET['cdm_id']);

// Titre de la page
$page_title = 'Bordereau de retour #' . $numero_bordereau;
$page = 'retours/view';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Vérifier si le bordereau existe
$sql = "SELECT COUNT(*) as count FROM bordereaux_retour WHERE numero = ? AND cdm_id = ?";
$result = $db->single($sql, [$numero_bordereau, $cdm_id]);

if ($result['count'] === 0) {
    // Si le bordereau n'existe pas dans la table des bordereaux, vérifions s'il existe des dossiers
    // avec ce numéro de bordereau (pour la rétrocompatibilité)
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $result = $db->single($sql, [$numero_bordereau, $cdm_id]);

    if ($result['count'] === 0) {
        redirect('index.php?page=retours', 'Bordereau non trouvé.', 'danger');
    }
}

// Récupérer la date du bordereau et les informations de l'utilisateur qui l'a créé
$sql = "SELECT dr.date_bordereau, u.username as created_by_username
        FROM dossiers_retournes dr
        JOIN users u ON dr.created_by = u.id
        WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
        LIMIT 1";
$bordereau_info = $db->single($sql, [$numero_bordereau, $cdm_id]);

// Récupérer les informations du CDM
$sql = "SELECT * FROM cdm WHERE id = ?";
$cdm = $db->single($sql, [$cdm_id]);

if (!$cdm) {
    redirect('index.php?page=retours', 'CDM non trouvé.', 'danger');
}

// Récupérer les dossiers associés à ce bordereau
$sql = "SELECT dr.*, a.nom as acte_nom, r.raison as raison_retour_nom
        FROM dossiers_retournes dr
        LEFT JOIN actes a ON dr.acte_id = a.id
        LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
        WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
        ORDER BY dr.created_at ASC";
$dossiers = $db->all($sql, [$numero_bordereau, $cdm_id]);

// Statistiques du bordereau
$total_dossiers = count($dossiers);
$dossiers_corriges = 0;
$dossiers_non_corriges = 0;
$montant_total = 0;

// Regrouper les raisons de retour pour les statistiques
$raisons_count = [];

foreach ($dossiers as $dossier) {
    if ($dossier['corrige']) {
        $dossiers_corriges++;
    } else {
        $dossiers_non_corriges++;
    }

    $montant_total += $dossier['montant'];

    // Compter les raisons de retour
    if (!empty($dossier['raison_retour_nom'])) {
        if (!isset($raisons_count[$dossier['raison_retour_nom']])) {
            $raisons_count[$dossier['raison_retour_nom']] = 0;
        }
        $raisons_count[$dossier['raison_retour_nom']]++;
    }
}

// Trier les motifs de retour par fréquence
arsort($raisons_count);

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'numero_dossier' => '',
    'numero_adherent' => '',
    'nom' => '',
    'beneficiaire' => '',
    'acte_id' => '',
    'montant' => '',
    'raison_retour_id' => '',
    'date_retour' => date('d/m/Y'),
    'date_correction' => '',
    'corrige' => '0',
    'notes' => ''
];

// Supprimer les données du formulaire de la session
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}

// Charger la vue
require_once VIEWS_PATH . 'retours/view.php';
?>
