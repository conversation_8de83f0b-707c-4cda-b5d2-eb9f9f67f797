<?php
// Vue pour la création d'un nouveau bordereau de dossiers retournés
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-plus-circle"></i> Nouveau bordereau de retour</h1>
        <div>
            <a href="index.php?page=retours" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>

    <?php if (!empty($form_errors)): ?>
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle"></i> Erreurs:</h5>
            <ul class="mb-0">
                <?php foreach ($form_errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-undo-alt"></i> Informations du bordereau</h5>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=retours/create">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="cdm_id" class="form-label required">CDM</label>
                        <select class="form-select" id="cdm_id" name="cdm_id" required>
                            <option value="">Sélectionnez un CDM</option>
                            <?php foreach ($cdm_list as $cdm): ?>
                                <option value="<?php echo $cdm['id']; ?>" <?php echo $form_data['cdm_id'] == $cdm['id'] ? 'selected' : ''; ?>>
                                    <?php echo $cdm['nom']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Centre médical concerné par ce bordereau</div>
                    </div>
                    <div class="col-md-6">
                        <label for="date_bordereau" class="form-label required">Date du bordereau</label>
                        <input type="text" class="form-control datepicker" id="date_bordereau" name="date_bordereau" value="<?php echo $form_data['date_bordereau']; ?>" required placeholder="jj/mm/aaaa">
                        <div class="form-text">Date à laquelle le bordereau a été créé</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $form_data['notes'] ?? ''; ?></textarea>
                        <div class="form-text">Ajoutez des notes ou des observations concernant ce bordereau</div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Un numéro de bordereau sera automatiquement généré lors de la création. Vous pourrez ensuite ajouter des dossiers retournés.
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Créer le bordereau
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
