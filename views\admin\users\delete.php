<?php
// Vue pour la suppression d'un utilisateur
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-user-times"></i> Supprimer un utilisateur</h1>
        <div>
            <a href="index.php?page=admin/users" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Confirmation de suppression</h5>
        </div>
        <div class="card-body">
            <p>Êtes-vous sûr de vouloir supprimer l'utilisateur suivant ?</p>
            
            <div class="alert alert-info">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Nom d'utilisateur:</strong> <?php echo $user['username']; ?></p>
                        <p><strong>Email:</strong> <?php echo $user['email']; ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Rôle:</strong> <?php echo $user['role'] === 'admin' ? 'Administrateur' : 'Utilisateur'; ?></p>
                        <p><strong>Statut:</strong> <?php echo $user['active'] ? 'Actif' : 'Inactif'; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> Attention: Cette action est irréversible. Toutes les données associées à cet utilisateur seront supprimées.
            </div>
            
            <form method="post" action="index.php?page=admin/users/delete&id=<?php echo $user['id']; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="index.php?page=admin/users" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Supprimer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
