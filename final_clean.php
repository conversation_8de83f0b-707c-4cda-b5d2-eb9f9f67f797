<?php
/**
 * التنظيف النهائي للبيانات - إزالة جميع التكرارات نهائياً
 */

// Inclure les fichiers de configuration
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Démarrer la session
session_start();

try {
    $db = Database::getInstance();
    
    // Créer la session admin
    $sql = "SELECT id, username, password, role, active FROM users WHERE username = 'admin'";
    $user = $db->single($sql);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    }
    
    echo "<h2>🔥 Nettoyage Final et Définitif</h2>";
    echo "<p>✅ Session admin créée</p>";
    
    // Paramètres du test
    $numero_bordereau = 10001;
    $cdm_id = 1;
    
    echo "<h3>🗑️ Étape 1: Suppression complète</h3>";
    
    // Supprimer TOUTES les raisons associées aux dossiers du bordereau
    $sql = "DELETE dr FROM dossier_raisons dr
            INNER JOIN dossiers_retournes d ON dr.dossier_id = d.id
            WHERE d.numero_bordereau = ? AND d.cdm_id = ?";
    $result = $db->query($sql, [$numero_bordereau, $cdm_id]);
    echo "• Raisons supprimées<br>";
    
    // Supprimer TOUS les dossiers du bordereau
    $sql = "DELETE FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $result = $db->query($sql, [$numero_bordereau, $cdm_id]);
    echo "• Dossiers supprimés<br>";
    
    // Vérifier que tout est supprimé
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $count = $db->single($sql, [$numero_bordereau, $cdm_id]);
    echo "• Dossiers restants: " . $count['count'] . "<br>";
    
    echo "<h3>📦 Étape 2: Création de données parfaitement uniques</h3>";
    
    // Récupérer les IDs des actes et raisons disponibles
    $sql = "SELECT id, nom, code FROM actes ORDER BY id LIMIT 5";
    $actes = $db->all($sql);
    
    $sql = "SELECT id, raison FROM raisons_retour ORDER BY id LIMIT 5";
    $raisons = $db->all($sql);
    
    echo "• Actes disponibles: " . count($actes) . "<br>";
    echo "• Raisons disponibles: " . count($raisons) . "<br>";
    
    // Créer exactement 5 dossiers complètement uniques
    $dossiers_uniques = [
        [
            'numero_dossier' => 'FINAL-001-' . date('His'),
            'numero_adherent' => 'ADH-FINAL-001',
            'nom' => 'ALAMI Mohammed',
            'beneficiaire' => 'ALAMI Mohammed',
            'acte_id' => $actes[0]['id'] ?? 1,
            'montant' => 150.00,
            'raison_retour_id' => $raisons[0]['id'] ?? 1,
            'notes' => 'Consultation générale - Document manquant'
        ],
        [
            'numero_dossier' => 'FINAL-002-' . date('His'),
            'numero_adherent' => 'ADH-FINAL-002',
            'nom' => 'BENNANI Fatima',
            'beneficiaire' => 'BENNANI Fatima',
            'acte_id' => $actes[1]['id'] ?? 2,
            'montant' => 200.00,
            'raison_retour_id' => $raisons[1]['id'] ?? 2,
            'notes' => 'Consultation spécialisée - Signature manquante'
        ],
        [
            'numero_dossier' => 'FINAL-003-' . date('His'),
            'numero_adherent' => 'ADH-FINAL-003',
            'nom' => 'TAZI Ahmed',
            'beneficiaire' => 'TAZI Aicha',
            'acte_id' => $actes[2]['id'] ?? 3,
            'montant' => 300.00,
            'raison_retour_id' => $raisons[2]['id'] ?? 3,
            'notes' => 'Radiographie - Informations incomplètes'
        ],
        [
            'numero_dossier' => 'FINAL-004-' . date('His'),
            'numero_adherent' => 'ADH-FINAL-004',
            'nom' => 'IDRISSI Youssef',
            'beneficiaire' => 'IDRISSI Youssef',
            'acte_id' => $actes[3]['id'] ?? 4,
            'montant' => 120.00,
            'raison_retour_id' => $raisons[3]['id'] ?? 4,
            'notes' => 'Échographie - Erreur identification'
        ],
        [
            'numero_dossier' => 'FINAL-005-' . date('His'),
            'numero_adherent' => 'ADH-FINAL-005',
            'nom' => 'BENNANI Aicha',
            'beneficiaire' => 'BENNANI Aicha',
            'acte_id' => $actes[4]['id'] ?? 5,
            'montant' => 180.00,
            'raison_retour_id' => $raisons[4]['id'] ?? 5,
            'notes' => 'Analyses biologiques - Document illisible'
        ]
    ];
    
    $dossier_ids = [];
    
    foreach ($dossiers_uniques as $index => $dossier) {
        echo "<h4>Création du dossier " . ($index + 1) . ":</h4>";
        
        // Insérer le dossier
        $sql = "INSERT INTO dossiers_retournes (numero_bordereau, cdm_id, date_bordereau, numero_dossier,
                                              numero_adherent, nom, beneficiaire, acte_id, montant,
                                              raison_retour_id, date_retour, corrige, notes, created_by, created_at)
                VALUES (?, ?, '2024-01-15', ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 1, NOW())";
        
        $params = [
            $numero_bordereau,
            $cdm_id,
            $dossier['numero_dossier'],
            $dossier['numero_adherent'],
            $dossier['nom'],
            $dossier['beneficiaire'],
            $dossier['acte_id'],
            $dossier['montant'],
            $dossier['raison_retour_id'],
            date('Y-m-d'),
            $dossier['notes']
        ];
        
        $db->query($sql, $params);
        $dossier_id = $db->lastInsertId();
        $dossier_ids[] = $dossier_id;
        
        echo "• ✅ " . $dossier['numero_dossier'] . " - " . $dossier['nom'] . " (ID: $dossier_id)<br>";
        echo "• Acte: " . ($actes[$index]['nom'] ?? 'N/A') . " (" . ($actes[$index]['code'] ?? 'N/A') . ")<br>";
        echo "• Raison: " . ($raisons[$index]['raison'] ?? 'N/A') . "<br>";
        
        // Ajouter une raison dans la table dossier_raisons
        $sql = "INSERT INTO dossier_raisons (dossier_id, raison_id, created_at) VALUES (?, ?, NOW())";
        $db->query($sql, [$dossier_id, $dossier['raison_retour_id']]);
        echo "• Raison ajoutée dans dossier_raisons<br>";
        
        echo "<br>";
    }
    
    echo "<h3>🔍 Étape 3: Vérification finale complète</h3>";
    
    // Vérifier le nombre total
    $sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE numero_bordereau = ? AND cdm_id = ?";
    $total = $db->single($sql, [$numero_bordereau, $cdm_id]);
    echo "• <strong>Total dossiers créés:</strong> " . $total['count'] . "<br>";
    
    // Vérifier l'unicité
    $sql = "SELECT numero_dossier, numero_adherent, COUNT(*) as count
            FROM dossiers_retournes 
            WHERE numero_bordereau = ? AND cdm_id = ?
            GROUP BY numero_dossier, numero_adherent
            HAVING COUNT(*) > 1";
    $duplicates = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    if (empty($duplicates)) {
        echo "• ✅ <strong>AUCUNE DUPLICATION</strong> - Tous les dossiers sont uniques<br>";
    } else {
        echo "• ❌ <strong>Duplications trouvées:</strong> " . count($duplicates) . "<br>";
        foreach ($duplicates as $dup) {
            echo "  - " . $dup['numero_dossier'] . " / " . $dup['numero_adherent'] . " (x" . $dup['count'] . ")<br>";
        }
    }
    
    // Vérifier les données complètes
    $sql = "SELECT dr.*, a.nom as acte_nom, a.code as acte_code, r.raison as raison_nom
            FROM dossiers_retournes dr
            LEFT JOIN actes a ON dr.acte_id = a.id
            LEFT JOIN raisons_retour r ON dr.raison_retour_id = r.id
            WHERE dr.numero_bordereau = ? AND dr.cdm_id = ?
            ORDER BY dr.created_at ASC";
    $verification = $db->all($sql, [$numero_bordereau, $cdm_id]);
    
    echo "<h4>📋 Données finales:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='padding: 5px;'>#</th>";
    echo "<th style='padding: 5px;'>N° Dossier</th>";
    echo "<th style='padding: 5px;'>N° Adhérent</th>";
    echo "<th style='padding: 5px;'>Nom</th>";
    echo "<th style='padding: 5px;'>Acte</th>";
    echo "<th style='padding: 5px;'>Montant</th>";
    echo "<th style='padding: 5px;'>Raison</th>";
    echo "</tr>";
    
    foreach ($verification as $index => $dossier) {
        echo "<tr>";
        echo "<td style='padding: 5px; text-align: center;'>" . ($index + 1) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_dossier']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['numero_adherent']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['nom']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['acte_nom'] ?? 'N/A') . "</td>";
        echo "<td style='padding: 5px; text-align: right;'>" . number_format($dossier['montant'], 2) . " DH</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($dossier['raison_nom'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🎉 Nettoyage Final Terminé!</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0;'>";
    echo "<h4 style='color: #155724; margin-top: 0;'>✅ Résultats:</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li><strong>Dossiers créés:</strong> " . $total['count'] . "</li>";
    echo "<li><strong>Duplications:</strong> 0</li>";
    echo "<li><strong>Données complètes:</strong> Oui</li>";
    echo "<li><strong>Prêt pour impression:</strong> Oui</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🔗 Tests finaux:</h3>";
    echo "<ul>";
    echo "<li><a href='test_print.php' target='_blank' style='color: #007bff; font-weight: bold;'>🖨️ Test d'impression final</a></li>";
    echo "<li><a href='index.php?page=retours/print&numero=10001&cdm_id=1' target='_blank' style='color: #28a745; font-weight: bold;'>🖨️ Page d'impression directe</a></li>";
    echo "<li><a href='index.php?page=retours/edit&numero=10001&cdm_id=1' target='_blank' style='color: #17a2b8; font-weight: bold;'>📝 Page d'édition</a></li>";
    echo "<li><a href='test_export.php' target='_blank' style='color: #ffc107; font-weight: bold;'>📊 Test d'exportation</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Erreur</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
    echo "<p>Trace: " . $e->getTraceAsString() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nettoyage Final - LOT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h2, h3, h4 {
            color: #333;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 10px 0;
        }
        
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        table {
            background: white;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
</body>
</html>
