<?php
// Vue pour la suppression d'un acte médical
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-procedures"></i> Supprimer un acte médical</h1>
        <div>
            <a href="index.php?page=admin/actes" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-trash"></i> Confirmation de suppression</h5>
        </div>
        <div class="card-body">
            <p>Êtes-vous sûr de vouloir <?php echo $is_used ? 'désactiver' : 'supprimer'; ?> l'acte médical suivant ?</p>
            
            <div class="alert alert-info">
                <h5><?php echo $acte['nom']; ?></h5>
                <p><?php echo $acte['description']; ?></p>
            </div>
            
            <?php if ($is_used): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> Cet acte est utilisé dans des dossiers existants. Il sera désactivé au lieu d'être supprimé.
                </div>
            <?php endif; ?>
            
            <form method="post" action="index.php?page=admin/actes/delete&id=<?php echo $acte['id']; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="index.php?page=admin/actes" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> <?php echo $is_used ? 'Désactiver' : 'Supprimer'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
