<?php
// Contrôleur pour la suppression d'une raison de retour

// Vérifier si l'utilisateur est administrateur
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Accès non autorisé.', 'danger');
}

// Vérifier si l'ID est fourni
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php?page=admin/raisons', 'ID de la raison non spécifié.', 'danger');
}

$id = intval($_GET['id']);

// Titre de la page
$page_title = 'Supprimer un motif de retour';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations de la raison
$sql = "SELECT * FROM raisons_retour WHERE id = ?";
$raison = $db->single($sql, [$id]);

if (!$raison) {
    redirect('index.php?page=admin/raisons', 'Motif non trouvé.', 'danger');
}

// Vérifier si la raison est utilisée dans des dossiers
$sql = "SELECT COUNT(*) as count FROM dossiers_retournes WHERE raison_retour_id = ?";
$result = $db->single($sql, [$id]);
$is_used = $result['count'] > 0;

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérification du jeton CSRF
    if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=admin/raisons/delete&id=' . $id, 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }
    
    try {
        // Vérifier si la colonne 'active' existe
        try {
            $check_column = "SHOW COLUMNS FROM raisons_retour LIKE 'active'";
            $column_exists = $db->query($check_column)->rowCount() > 0;
        } catch (Exception $e) {
            $column_exists = false;
        }
        
        if ($is_used) {
            if ($column_exists) {
                // Si la raison est utilisée et que la colonne 'active' existe, on la désactive
                $sql = "UPDATE raisons_retour SET active = 0, updated_at = NOW() WHERE id = ?";
                $db->query($sql, [$id]);
                
                // Journaliser l'action
                logActivity($_SESSION['user_id'], 'Désactivation d\'un motif de retour', 'ID: ' . $id . ', Motif: ' . $raison['raison']);
                
                redirect('index.php?page=admin/raisons', 'Le motif a été désactivé car il est utilisé dans des dossiers.', 'warning');
            } else {
                // Si la colonne 'active' n'existe pas, on ne peut pas désactiver le motif
                redirect('index.php?page=admin/raisons', 'Impossible de désactiver le motif car la colonne \'active\' n\'existe pas. Veuillez exécuter le script d\'ajout de la colonne.', 'danger');
            }
        } else {
            // Si le motif n'est pas utilisé, on peut le supprimer
            $sql = "DELETE FROM raisons_retour WHERE id = ?";
            $db->query($sql, [$id]);
            
            // Journaliser l'action
            logActivity($_SESSION['user_id'], 'Suppression d\'un motif de retour', 'ID: ' . $id . ', Motif: ' . $raison['raison']);
            
            redirect('index.php?page=admin/raisons', 'Le motif a été supprimé avec succès.', 'success');
        }
    } catch (Exception $e) {
        redirect('index.php?page=admin/raisons/delete&id=' . $id, 'Erreur lors de la suppression: ' . $e->getMessage(), 'danger');
    }
}

// Charger la vue
require_once VIEWS_PATH . 'admin/raisons/delete.php';
?>
