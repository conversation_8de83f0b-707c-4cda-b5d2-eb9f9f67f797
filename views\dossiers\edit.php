<?php
// Vue pour la modification d'un dossier
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-edit"></i> Modifier le dossier #<?php echo $dossier['numero_dossier']; ?></h1>
        <div>
            <a href="index.php?page=lots/view&id=<?php echo $lot_id; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour au lot
            </a>
        </div>
    </div>

    <?php if (!empty($form_errors)): ?>
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle"></i> Erreurs:</h5>
            <ul class="mb-0">
                <?php foreach ($form_errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-folder"></i> Informations du dossier</h5>
        </div>
        <div class="card-body">
            <form method="post" action="index.php?page=dossiers/edit&id=<?php echo $dossier_id; ?>&lot_id=<?php echo $lot_id; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="numero_dossier" class="form-label required">Numéro de dossier</label>
                        <input type="text" class="form-control" id="numero_dossier" name="numero_dossier" value="<?php echo $form_data['numero_dossier']; ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="numero_adherent" class="form-label">Numéro d'adhérent</label>
                        <input type="text" class="form-control" id="numero_adherent" name="numero_adherent" value="<?php echo $form_data['numero_adherent']; ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="numero_bon" class="form-label">Numéro de bon</label>
                        <input type="text" class="form-control" id="numero_bon" name="numero_bon" value="<?php echo $form_data['numero_bon']; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="nom" class="form-label required">Nom</label>
                        <input type="text" class="form-control" id="nom" name="nom" value="<?php echo $form_data['nom']; ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="beneficiaire" class="form-label">Bénéficiaire</label>
                        <input type="text" class="form-control" id="beneficiaire" name="beneficiaire" value="<?php echo $form_data['beneficiaire']; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="acte_id" class="form-label">Acte</label>
                        <select class="form-select" id="acte_id" name="acte_id">
                            <option value="">Sélectionnez un acte</option>
                            <?php foreach ($actes_list as $acte): ?>
                                <option value="<?php echo $acte['id']; ?>" <?php echo $form_data['acte_id'] == $acte['id'] ? 'selected' : ''; ?>>
                                    <?php echo $acte['nom']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="montant" class="form-label">Montant</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="montant" name="montant" value="<?php echo $form_data['montant']; ?>">
                            <span class="input-group-text">DH</span>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Statut de réception</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="recu" id="recu_non" value="0" <?php echo $form_data['recu'] == 0 ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="recu_non">
                                <span class="badge bg-warning">Non reçu</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="recu" id="recu_oui" value="1" <?php echo $form_data['recu'] == 1 ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="recu_oui">
                                <span class="badge bg-success">Reçu</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $form_data['notes']; ?></textarea>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="index.php?page=lots/view&id=<?php echo $lot_id; ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
