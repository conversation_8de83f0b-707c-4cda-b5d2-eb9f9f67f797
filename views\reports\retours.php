<?php
// Vue pour le rapport d'analyse des dossiers retournés
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-undo-alt"></i> Analyse des retours</h1>
        <div>
            <button id="printPage" class="btn btn-outline-secondary">
                <i class="fas fa-print"></i> Imprimer
            </button>
            <a href="index.php?page=reports/export&type=retours&periode=<?php echo $periode; ?>&cdm_id=<?php echo $cdm_id; ?>&raison_id=<?php echo $raison_id; ?>&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Exporter
            </a>
        </div>
    </div>
    
    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter"></i> Filtres</h5>
        </div>
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="reports/retours">
                
                <div class="col-md-3">
                    <label for="periode" class="form-label">Période prédéfinie</label>
                    <select class="form-select" id="periode" name="periode">
                        <option value="30" <?php echo $periode === '30' ? 'selected' : ''; ?>>30 derniers jours</option>
                        <option value="60" <?php echo $periode === '60' ? 'selected' : ''; ?>>60 derniers jours</option>
                        <option value="90" <?php echo $periode === '90' ? 'selected' : ''; ?>>90 derniers jours</option>
                        <option value="365" <?php echo $periode === '365' ? 'selected' : ''; ?>>365 derniers jours</option>
                        <option value="custom" <?php echo (!empty($date_debut) && !empty($date_fin)) ? 'selected' : ''; ?>>Personnalisée</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="cdm_id" class="form-label">CDM</label>
                    <select class="form-select" id="cdm_id" name="cdm_id">
                        <option value="0">Tous les CDM</option>
                        <?php foreach ($cdm_list as $cdm): ?>
                            <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm_id == $cdm['id'] ? 'selected' : ''; ?>>
                                <?php echo $cdm['nom']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="raison_id" class="form-label">Raison de retour</label>
                    <select class="form-select" id="raison_id" name="raison_id">
                        <option value="0">Toutes les raisons</option>
                        <?php foreach ($raisons_list as $raison): ?>
                            <option value="<?php echo $raison['id']; ?>" <?php echo $raison_id == $raison['id'] ? 'selected' : ''; ?>>
                                <?php echo $raison['raison']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2 date-range" style="<?php echo (!empty($date_debut) && !empty($date_fin)) ? '' : 'display: none;'; ?>">
                    <label for="date_debut" class="form-label">Date début</label>
                    <input type="text" class="form-control datepicker" id="date_debut" name="date_debut" value="<?php echo $date_debut; ?>" placeholder="jj/mm/aaaa">
                </div>
                
                <div class="col-md-2 date-range" style="<?php echo (!empty($date_debut) && !empty($date_fin)) ? '' : 'display: none;'; ?>">
                    <label for="date_fin" class="form-label">Date fin</label>
                    <input type="text" class="form-control datepicker" id="date_fin" name="date_fin" value="<?php echo $date_fin; ?>" placeholder="jj/mm/aaaa">
                </div>
                
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Appliquer les filtres
                    </button>
                    <a href="index.php?page=reports/retours" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Période analysée -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle"></i> Période analysée: <strong>
            <?php 
                echo formatDate($date_debut_formatted) . ' au ' . formatDate($date_fin_formatted);
                if ($cdm_id > 0) {
                    foreach ($cdm_list as $cdm) {
                        if ($cdm['id'] == $cdm_id) {
                            echo ' - CDM: ' . $cdm['nom'];
                            break;
                        }
                    }
                }
                if ($raison_id > 0) {
                    foreach ($raisons_list as $raison) {
                        if ($raison['id'] == $raison_id) {
                            echo ' - Raison: ' . $raison['raison'];
                            break;
                        }
                    }
                }
            ?>
        </strong>
    </div>
    
    <!-- Résumé des retours -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Résumé des retours</h5>
        </div>
        <div class="card-body">
            <?php 
                $total_retours = array_sum(array_column($cdm_retours, 'nb_retours'));
                $total_corriges = array_sum(array_column($cdm_retours, 'nb_corriges'));
                $total_non_corriges = array_sum(array_column($cdm_retours, 'nb_non_corriges'));
                $taux_correction = ($total_retours > 0) ? ($total_corriges / $total_retours) * 100 : 0;
            ?>
            
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">Total des retours</h6>
                            <p class="card-text fs-4 fw-bold"><?php echo $total_retours; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h6 class="card-title">Dossiers corrigés</h6>
                            <p class="card-text fs-4 fw-bold"><?php echo $total_corriges; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h6 class="card-title">Dossiers non corrigés</h6>
                            <p class="card-text fs-4 fw-bold"><?php echo $total_non_corriges; ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if ($total_retours > 0): ?>
                <div class="mt-4">
                    <h6>Taux de correction global: <?php echo round($taux_correction, 1); ?>%</h6>
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: <?php echo $taux_correction; ?>%;" 
                             aria-valuenow="<?php echo $taux_correction; ?>" 
                             aria-valuemin="0" aria-valuemax="100">
                            <?php echo round($taux_correction, 1); ?>% Corrigés
                        </div>
                        <div class="progress-bar bg-danger" role="progressbar" 
                             style="width: <?php echo 100 - $taux_correction; ?>%;" 
                             aria-valuenow="<?php echo 100 - $taux_correction; ?>" 
                             aria-valuemin="0" aria-valuemax="100">
                            <?php echo round(100 - $taux_correction, 1); ?>% Non corrigés
                        </div>
                    </div>
                </div>
                
                <!-- Graphique d'évolution mensuelle -->
                <?php if (count($evolution_mensuelle) > 0): ?>
                    <div class="mt-4">
                        <h6>Évolution mensuelle des retours</h6>
                        <canvas id="evolutionChart" height="250"></canvas>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i> Aucun dossier retourné pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Retours par CDM -->
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-hospital"></i> Retours par CDM</h5>
        </div>
        <div class="card-body">
            <?php if (count($cdm_retours) > 0 && $total_retours > 0): ?>
                <div class="row">
                    <div class="col-md-8">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>CDM</th>
                                        <th class="text-end">Retours</th>
                                        <th class="text-end">Corrigés</th>
                                        <th class="text-end">Non corrigés</th>
                                        <th>Taux de correction</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cdm_retours as $cdm): ?>
                                        <?php if ($cdm['nb_retours'] > 0): ?>
                                            <tr>
                                                <td><?php echo $cdm['nom']; ?></td>
                                                <td class="text-end"><?php echo $cdm['nb_retours']; ?></td>
                                                <td class="text-end"><?php echo $cdm['nb_corriges']; ?></td>
                                                <td class="text-end"><?php echo $cdm['nb_non_corriges']; ?></td>
                                                <td>
                                                    <?php 
                                                        $taux_correction = ($cdm['nb_retours'] > 0) ? 
                                                                        ($cdm['nb_corriges'] / $cdm['nb_retours']) * 100 : 0;
                                                    ?>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar bg-success" role="progressbar" 
                                                             style="width: <?php echo $taux_correction; ?>%;" 
                                                             aria-valuenow="<?php echo $taux_correction; ?>" 
                                                             aria-valuemin="0" aria-valuemax="100">
                                                            <?php echo round($taux_correction, 1); ?>%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <canvas id="cdmRetoursChart" height="300"></canvas>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun dossier retourné pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Raisons de retour -->
    <div class="card mb-4">
        <div class="card-header bg-warning">
            <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Raisons de retour</h5>
        </div>
        <div class="card-body">
            <?php if (count($raisons_retour) > 0 && array_sum(array_column($raisons_retour, 'nb_retours')) > 0): ?>
                <div class="row">
                    <div class="col-md-8">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Raison</th>
                                        <th class="text-end">Retours</th>
                                        <th class="text-end">Corrigés</th>
                                        <th class="text-end">Non corrigés</th>
                                        <th>Pourcentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                        $total_raisons = array_sum(array_column($raisons_retour, 'nb_retours'));
                                    ?>
                                    <?php foreach ($raisons_retour as $raison): ?>
                                        <?php if ($raison['nb_retours'] > 0): ?>
                                            <tr>
                                                <td><?php echo $raison['raison']; ?></td>
                                                <td class="text-end"><?php echo $raison['nb_retours']; ?></td>
                                                <td class="text-end"><?php echo $raison['nb_corriges']; ?></td>
                                                <td class="text-end"><?php echo $raison['nb_non_corriges']; ?></td>
                                                <td>
                                                    <?php 
                                                        $pourcentage = ($total_raisons > 0) ? 
                                                                    ($raison['nb_retours'] / $total_raisons) * 100 : 0;
                                                    ?>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar bg-warning" role="progressbar" 
                                                             style="width: <?php echo $pourcentage; ?>%;" 
                                                             aria-valuenow="<?php echo $pourcentage; ?>" 
                                                             aria-valuemin="0" aria-valuemax="100">
                                                            <?php echo round($pourcentage, 1); ?>%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <canvas id="raisonsChart" height="300"></canvas>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun dossier retourné pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Raisons de retour par CDM -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-list"></i> Raisons de retour par CDM</h5>
        </div>
        <div class="card-body">
            <?php if (count($raisons_by_cdm) > 0): ?>
                <div class="accordion" id="accordionRaisons">
                    <?php foreach ($raisons_by_cdm as $cdm_id => $cdm_data): ?>
                        <?php if (count($cdm_data['raisons']) > 0): ?>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading<?php echo $cdm_id; ?>">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $cdm_id; ?>" aria-expanded="false" aria-controls="collapse<?php echo $cdm_id; ?>">
                                        <?php echo $cdm_data['nom']; ?> - <?php echo array_sum(array_column($cdm_data['raisons'], 'nb_retours')); ?> retour(s)
                                    </button>
                                </h2>
                                <div id="collapse<?php echo $cdm_id; ?>" class="accordion-collapse collapse" aria-labelledby="heading<?php echo $cdm_id; ?>" data-bs-parent="#accordionRaisons">
                                    <div class="accordion-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Raison</th>
                                                        <th class="text-end">Nombre</th>
                                                        <th>Pourcentage</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php 
                                                        $cdm_total_retours = array_sum(array_column($cdm_data['raisons'], 'nb_retours'));
                                                    ?>
                                                    <?php foreach ($cdm_data['raisons'] as $raison): ?>
                                                        <tr>
                                                            <td><?php echo $raison['raison']; ?></td>
                                                            <td class="text-end"><?php echo $raison['nb_retours']; ?></td>
                                                            <td>
                                                                <?php 
                                                                    $pourcentage = ($cdm_total_retours > 0) ? 
                                                                                ($raison['nb_retours'] / $cdm_total_retours) * 100 : 0;
                                                                ?>
                                                                <div class="progress" style="height: 15px;">
                                                                    <div class="progress-bar bg-info" role="progressbar" 
                                                                         style="width: <?php echo $pourcentage; ?>%;" 
                                                                         aria-valuenow="<?php echo $pourcentage; ?>" 
                                                                         aria-valuemin="0" aria-valuemax="100">
                                                                        <?php echo round($pourcentage, 1); ?>%
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune donnée disponible pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Afficher/masquer les champs de date personnalisée
    document.getElementById('periode').addEventListener('change', function() {
        var dateRangeFields = document.querySelectorAll('.date-range');
        if (this.value === 'custom') {
            dateRangeFields.forEach(function(field) {
                field.style.display = '';
            });
        } else {
            dateRangeFields.forEach(function(field) {
                field.style.display = 'none';
            });
        }
    });
    
    // Graphique d'évolution mensuelle
    <?php if (count($evolution_mensuelle) > 0): ?>
        var evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
        var evolutionChart = new Chart(evolutionCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($formatted_months); ?>,
                datasets: [{
                    label: 'Retours',
                    data: <?php echo json_encode($retours_data); ?>,
                    backgroundColor: 'rgba(220, 53, 69, 0.2)',
                    borderColor: 'rgba(220, 53, 69, 1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }, {
                    label: 'Corrigés',
                    data: <?php echo json_encode($corriges_data); ?>,
                    backgroundColor: 'rgba(25, 135, 84, 0.2)',
                    borderColor: 'rgba(25, 135, 84, 1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    <?php endif; ?>
    
    // Graphique des retours par CDM
    <?php if (count($cdm_retours) > 0 && $total_retours > 0): ?>
        var cdmRetoursCtx = document.getElementById('cdmRetoursChart').getContext('2d');
        var cdmRetoursChart = new Chart(cdmRetoursCtx, {
            type: 'pie',
            data: {
                labels: [
                    <?php 
                        $cdm_names = [];
                        foreach ($cdm_retours as $cdm) {
                            if ($cdm['nb_retours'] > 0) {
                                $cdm_names[] = $cdm['nom'];
                            }
                        }
                        echo "'" . implode("', '", $cdm_names) . "'"; 
                    ?>
                ],
                datasets: [{
                    data: [
                        <?php 
                            $cdm_data = [];
                            foreach ($cdm_retours as $cdm) {
                                if ($cdm['nb_retours'] > 0) {
                                    $cdm_data[] = $cdm['nb_retours'];
                                }
                            }
                            echo implode(', ', $cdm_data); 
                        ?>
                    ],
                    backgroundColor: [
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(25, 135, 84, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(13, 202, 240, 0.7)',
                        'rgba(108, 117, 125, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(214, 51, 132, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    <?php endif; ?>
    
    // Graphique des raisons de retour
    <?php if (count($raisons_retour) > 0 && array_sum(array_column($raisons_retour, 'nb_retours')) > 0): ?>
        var raisonsCtx = document.getElementById('raisonsChart').getContext('2d');
        var raisonsChart = new Chart(raisonsCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    <?php 
                        $raison_names = [];
                        foreach ($raisons_retour as $raison) {
                            if ($raison['nb_retours'] > 0) {
                                $raison_names[] = $raison['raison'];
                            }
                        }
                        echo "'" . implode("', '", $raison_names) . "'"; 
                    ?>
                ],
                datasets: [{
                    data: [
                        <?php 
                            $raison_data = [];
                            foreach ($raisons_retour as $raison) {
                                if ($raison['nb_retours'] > 0) {
                                    $raison_data[] = $raison['nb_retours'];
                                }
                            }
                            echo implode(', ', $raison_data); 
                        ?>
                    ],
                    backgroundColor: [
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(25, 135, 84, 0.7)',
                        'rgba(13, 202, 240, 0.7)',
                        'rgba(108, 117, 125, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(214, 51, 132, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    <?php endif; ?>
});
</script>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
