<?php
// Vue pour le rapport d'analyse des dossiers retournés
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-undo-alt"></i> Analyse des retours</h1>
        <div>
            <button id="printPage" class="btn btn-outline-secondary">
                <i class="fas fa-print"></i> Imprimer
            </button>
            <a href="index.php?page=reports/export&type=retours&periode=<?php echo $periode; ?>&cdm_id=<?php echo $cdm_id; ?>&raison_id=<?php echo $raison_id; ?>&date_debut=<?php echo $date_debut; ?>&date_fin=<?php echo $date_fin; ?>" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Exporter
            </a>
        </div>
    </div>
    
    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter"></i> Filtres</h5>
        </div>
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="reports/retours">
                
                <div class="col-md-3">
                    <label for="periode" class="form-label">Période prédéfinie</label>
                    <select class="form-select" id="periode" name="periode">
                        <option value="30" <?php echo $periode === '30' ? 'selected' : ''; ?>>30 derniers jours</option>
                        <option value="60" <?php echo $periode === '60' ? 'selected' : ''; ?>>60 derniers jours</option>
                        <option value="90" <?php echo $periode === '90' ? 'selected' : ''; ?>>90 derniers jours</option>
                        <option value="365" <?php echo $periode === '365' ? 'selected' : ''; ?>>365 derniers jours</option>
                        <option value="custom" <?php echo (!empty($date_debut) && !empty($date_fin)) ? 'selected' : ''; ?>>Personnalisée</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="cdm_id" class="form-label">CDM</label>
                    <select class="form-select" id="cdm_id" name="cdm_id">
                        <option value="0">Tous les CDM</option>
                        <?php foreach ($cdm_list as $cdm): ?>
                            <option value="<?php echo $cdm['id']; ?>" <?php echo $cdm_id == $cdm['id'] ? 'selected' : ''; ?>>
                                <?php echo $cdm['nom']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="raison_id" class="form-label">Raison de retour</label>
                    <select class="form-select" id="raison_id" name="raison_id">
                        <option value="0">Toutes les raisons</option>
                        <?php foreach ($raisons_list as $raison): ?>
                            <option value="<?php echo $raison['id']; ?>" <?php echo $raison_id == $raison['id'] ? 'selected' : ''; ?>>
                                <?php echo $raison['raison']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2 date-range" style="<?php echo (!empty($date_debut) && !empty($date_fin)) ? '' : 'display: none;'; ?>">
                    <label for="date_debut" class="form-label">Date début</label>
                    <input type="text" class="form-control datepicker" id="date_debut" name="date_debut" value="<?php echo $date_debut; ?>" placeholder="jj/mm/aaaa">
                </div>
                
                <div class="col-md-2 date-range" style="<?php echo (!empty($date_debut) && !empty($date_fin)) ? '' : 'display: none;'; ?>">
                    <label for="date_fin" class="form-label">Date fin</label>
                    <input type="text" class="form-control datepicker" id="date_fin" name="date_fin" value="<?php echo $date_fin; ?>" placeholder="jj/mm/aaaa">
                </div>
                
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Appliquer les filtres
                    </button>
                    <a href="index.php?page=reports/retours" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Période analysée -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle"></i> Période analysée: <strong>
            <?php 
                echo formatDate($date_debut_formatted) . ' au ' . formatDate($date_fin_formatted);
                if ($cdm_id > 0) {
                    foreach ($cdm_list as $cdm) {
                        if ($cdm['id'] == $cdm_id) {
                            echo ' - CDM: ' . $cdm['nom'];
                            break;
                        }
                    }
                }
                if ($raison_id > 0) {
                    foreach ($raisons_list as $raison) {
                        if ($raison['id'] == $raison_id) {
                            echo ' - Raison: ' . $raison['raison'];
                            break;
                        }
                    }
                }
            ?>
        </strong>
    </div>
    
    <!-- Résumé des retours -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Résumé des retours</h5>
        </div>
        <div class="card-body">
            <?php 
                $total_retours = array_sum(array_column($cdm_retours, 'nb_retours'));
                $total_corriges = array_sum(array_column($cdm_retours, 'nb_corriges'));
                $total_non_corriges = array_sum(array_column($cdm_retours, 'nb_non_corriges'));
                $taux_correction = ($total_retours > 0) ? ($total_corriges / $total_retours) * 100 : 0;
            ?>
            
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">Total des retours</h6>
                            <p class="card-text fs-4 fw-bold"><?php echo $total_retours; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h6 class="card-title">Dossiers corrigés</h6>
                            <p class="card-text fs-4 fw-bold"><?php echo $total_corriges; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h6 class="card-title">Dossiers non corrigés</h6>
                            <p class="card-text fs-4 fw-bold"><?php echo $total_non_corriges; ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if ($total_retours > 0): ?>
                <div class="mt-4">
                    <h6>Taux de correction global: <?php echo round($taux_correction, 1); ?>%</h6>
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: <?php echo $taux_correction; ?>%;" 
                             aria-valuenow="<?php echo $taux_correction; ?>" 
                             aria-valuemin="0" aria-valuemax="100">
                            <?php echo round($taux_correction, 1); ?>% Corrigés
                        </div>
                        <div class="progress-bar bg-danger" role="progressbar" 
                             style="width: <?php echo 100 - $taux_correction; ?>%;" 
                             aria-valuenow="<?php echo 100 - $taux_correction; ?>" 
                             aria-valuemin="0" aria-valuemax="100">
                            <?php echo round(100 - $taux_correction, 1); ?>% Non corrigés
                        </div>
                    </div>
                </div>
                
                <!-- Graphique d'évolution mensuelle amélioré -->
                <?php if (count($evolution_mensuelle) > 0): ?>
                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Évolution mensuelle des retours</h6>
                            <div class="btn-group btn-group-sm" role="group">
                                <input type="radio" class="btn-check" name="chartType" id="lineChart" autocomplete="off" checked>
                                <label class="btn btn-outline-primary" for="lineChart">Courbes</label>

                                <input type="radio" class="btn-check" name="chartType" id="barChart" autocomplete="off">
                                <label class="btn btn-outline-primary" for="barChart">Barres</label>

                                <input type="radio" class="btn-check" name="chartType" id="areaChart" autocomplete="off">
                                <label class="btn btn-outline-primary" for="areaChart">Aires</label>
                            </div>
                        </div>

                        <!-- Statistiques rapides -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="card border-danger">
                                    <div class="card-body text-center py-2">
                                        <small class="text-muted">Pic de retours</small>
                                        <div class="fw-bold text-danger" id="maxRetours">-</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-success">
                                    <div class="card-body text-center py-2">
                                        <small class="text-muted">Meilleur taux correction</small>
                                        <div class="fw-bold text-success" id="maxCorrection">-</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-info">
                                    <div class="card-body text-center py-2">
                                        <small class="text-muted">Moyenne mensuelle</small>
                                        <div class="fw-bold text-info" id="avgRetours">-</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-warning">
                                    <div class="card-body text-center py-2">
                                        <small class="text-muted">Tendance</small>
                                        <div class="fw-bold text-warning" id="tendance">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="position-relative">
                            <canvas id="evolutionChart" height="300"></canvas>
                        </div>

                        <!-- Légende détaillée -->
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="me-2" style="width: 20px; height: 3px; background: linear-gradient(90deg, rgba(220, 53, 69, 0.8), rgba(220, 53, 69, 1));"></div>
                                        <span class="small">Total des retours par mois</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="me-2" style="width: 20px; height: 3px; background: linear-gradient(90deg, rgba(25, 135, 84, 0.8), rgba(25, 135, 84, 1));"></div>
                                        <span class="small">Dossiers corrigés par mois</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="me-2" style="width: 20px; height: 3px; background: linear-gradient(90deg, rgba(255, 193, 7, 0.8), rgba(255, 193, 7, 1));"></div>
                                        <span class="small">Dossiers non corrigés par mois</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="me-2" style="width: 20px; height: 3px; background: linear-gradient(90deg, rgba(13, 202, 240, 0.8), rgba(13, 202, 240, 1));"></div>
                                        <span class="small">Taux de correction (%)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i> Aucun dossier retourné pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Retours par CDM -->
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-hospital"></i> Retours par CDM</h5>
        </div>
        <div class="card-body">
            <?php if (count($cdm_retours) > 0 && $total_retours > 0): ?>
                <div class="row">
                    <div class="col-md-8">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>CDM</th>
                                        <th class="text-end">Retours</th>
                                        <th class="text-end">Corrigés</th>
                                        <th class="text-end">Non corrigés</th>
                                        <th>Taux de correction</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cdm_retours as $cdm): ?>
                                        <?php if ($cdm['nb_retours'] > 0): ?>
                                            <tr>
                                                <td><?php echo $cdm['nom']; ?></td>
                                                <td class="text-end"><?php echo $cdm['nb_retours']; ?></td>
                                                <td class="text-end"><?php echo $cdm['nb_corriges']; ?></td>
                                                <td class="text-end"><?php echo $cdm['nb_non_corriges']; ?></td>
                                                <td>
                                                    <?php 
                                                        $taux_correction = ($cdm['nb_retours'] > 0) ? 
                                                                        ($cdm['nb_corriges'] / $cdm['nb_retours']) * 100 : 0;
                                                    ?>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar bg-success" role="progressbar" 
                                                             style="width: <?php echo $taux_correction; ?>%;" 
                                                             aria-valuenow="<?php echo $taux_correction; ?>" 
                                                             aria-valuemin="0" aria-valuemax="100">
                                                            <?php echo round($taux_correction, 1); ?>%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <canvas id="cdmRetoursChart" height="300"></canvas>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun dossier retourné pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Raisons de retour -->
    <div class="card mb-4">
        <div class="card-header bg-warning">
            <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Raisons de retour</h5>
        </div>
        <div class="card-body">
            <?php if (count($raisons_retour) > 0 && array_sum(array_column($raisons_retour, 'nb_retours')) > 0): ?>
                <div class="row">
                    <div class="col-md-8">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Raison</th>
                                        <th class="text-end">Retours</th>
                                        <th class="text-end">Corrigés</th>
                                        <th class="text-end">Non corrigés</th>
                                        <th>Pourcentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                        $total_raisons = array_sum(array_column($raisons_retour, 'nb_retours'));
                                    ?>
                                    <?php foreach ($raisons_retour as $raison): ?>
                                        <?php if ($raison['nb_retours'] > 0): ?>
                                            <tr>
                                                <td><?php echo $raison['raison']; ?></td>
                                                <td class="text-end"><?php echo $raison['nb_retours']; ?></td>
                                                <td class="text-end"><?php echo $raison['nb_corriges']; ?></td>
                                                <td class="text-end"><?php echo $raison['nb_non_corriges']; ?></td>
                                                <td>
                                                    <?php 
                                                        $pourcentage = ($total_raisons > 0) ? 
                                                                    ($raison['nb_retours'] / $total_raisons) * 100 : 0;
                                                    ?>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar bg-warning" role="progressbar" 
                                                             style="width: <?php echo $pourcentage; ?>%;" 
                                                             aria-valuenow="<?php echo $pourcentage; ?>" 
                                                             aria-valuemin="0" aria-valuemax="100">
                                                            <?php echo round($pourcentage, 1); ?>%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <canvas id="raisonsChart" height="300"></canvas>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun dossier retourné pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Raisons de retour par CDM -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-list"></i> Raisons de retour par CDM</h5>
        </div>
        <div class="card-body">
            <?php if (count($raisons_by_cdm) > 0): ?>
                <div class="accordion" id="accordionRaisons">
                    <?php foreach ($raisons_by_cdm as $cdm_id => $cdm_data): ?>
                        <?php if (count($cdm_data['raisons']) > 0): ?>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading<?php echo $cdm_id; ?>">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $cdm_id; ?>" aria-expanded="false" aria-controls="collapse<?php echo $cdm_id; ?>">
                                        <?php echo $cdm_data['nom']; ?> - <?php echo array_sum(array_column($cdm_data['raisons'], 'nb_retours')); ?> retour(s)
                                    </button>
                                </h2>
                                <div id="collapse<?php echo $cdm_id; ?>" class="accordion-collapse collapse" aria-labelledby="heading<?php echo $cdm_id; ?>" data-bs-parent="#accordionRaisons">
                                    <div class="accordion-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Raison</th>
                                                        <th class="text-end">Nombre</th>
                                                        <th>Pourcentage</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php 
                                                        $cdm_total_retours = array_sum(array_column($cdm_data['raisons'], 'nb_retours'));
                                                    ?>
                                                    <?php foreach ($cdm_data['raisons'] as $raison): ?>
                                                        <tr>
                                                            <td><?php echo $raison['raison']; ?></td>
                                                            <td class="text-end"><?php echo $raison['nb_retours']; ?></td>
                                                            <td>
                                                                <?php 
                                                                    $pourcentage = ($cdm_total_retours > 0) ? 
                                                                                ($raison['nb_retours'] / $cdm_total_retours) * 100 : 0;
                                                                ?>
                                                                <div class="progress" style="height: 15px;">
                                                                    <div class="progress-bar bg-info" role="progressbar" 
                                                                         style="width: <?php echo $pourcentage; ?>%;" 
                                                                         aria-valuenow="<?php echo $pourcentage; ?>" 
                                                                         aria-valuemin="0" aria-valuemax="100">
                                                                        <?php echo round($pourcentage, 1); ?>%
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune donnée disponible pour cette période.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Afficher/masquer les champs de date personnalisée
    document.getElementById('periode').addEventListener('change', function() {
        var dateRangeFields = document.querySelectorAll('.date-range');
        if (this.value === 'custom') {
            dateRangeFields.forEach(function(field) {
                field.style.display = '';
            });
        } else {
            dateRangeFields.forEach(function(field) {
                field.style.display = 'none';
            });
        }
    });
    
    // Graphique d'évolution mensuelle amélioré
    <?php if (count($evolution_mensuelle) > 0): ?>
        var evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
        var evolutionChart;

        // Données pour le graphique
        var chartData = {
            labels: <?php echo json_encode($formatted_months); ?>,
            retours: <?php echo json_encode($retours_data); ?>,
            corriges: <?php echo json_encode($corriges_data); ?>,
            non_corriges: <?php
                $non_corriges_data = [];
                foreach ($retours_data as $i => $retours) {
                    $non_corriges_data[] = $retours - $corriges_data[$i];
                }
                echo json_encode($non_corriges_data);
            ?>,
            taux_correction: <?php
                $taux_data = [];
                foreach ($retours_data as $i => $retours) {
                    $taux_data[] = $retours > 0 ? round(($corriges_data[$i] / $retours) * 100, 1) : 0;
                }
                echo json_encode($taux_data);
            ?>
        };

        // Fonction pour créer le graphique
        function createEvolutionChart(type) {
            if (evolutionChart) {
                evolutionChart.destroy();
            }

            var datasets = [];
            var options = {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Évolution mensuelle détaillée des retours',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            title: function(context) {
                                return 'Mois: ' + context[0].label;
                            },
                            afterBody: function(context) {
                                var index = context[0].dataIndex;
                                var retours = chartData.retours[index];
                                var corriges = chartData.corriges[index];
                                var taux = chartData.taux_correction[index];
                                return [
                                    '',
                                    'Résumé du mois:',
                                    '• Total retours: ' + retours,
                                    '• Corrigés: ' + corriges,
                                    '• Non corrigés: ' + (retours - corriges),
                                    '• Taux correction: ' + taux + '%'
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Période',
                            font: {
                                weight: 'bold'
                            }
                        },
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Nombre de dossiers',
                            font: {
                                weight: 'bold'
                            }
                        },
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Taux de correction (%)',
                            font: {
                                weight: 'bold'
                            }
                        },
                        min: 0,
                        max: 100,
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            };

            // Configuration selon le type de graphique
            if (type === 'bar') {
                datasets = [
                    {
                        label: 'Total retours',
                        data: chartData.retours,
                        backgroundColor: 'rgba(220, 53, 69, 0.7)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 2,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Corrigés',
                        data: chartData.corriges,
                        backgroundColor: 'rgba(25, 135, 84, 0.7)',
                        borderColor: 'rgba(25, 135, 84, 1)',
                        borderWidth: 2,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Non corrigés',
                        data: chartData.non_corriges,
                        backgroundColor: 'rgba(255, 193, 7, 0.7)',
                        borderColor: 'rgba(255, 193, 7, 1)',
                        borderWidth: 2,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Taux correction (%)',
                        data: chartData.taux_correction,
                        type: 'line',
                        backgroundColor: 'rgba(13, 202, 240, 0.2)',
                        borderColor: 'rgba(13, 202, 240, 1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y1',
                        pointBackgroundColor: 'rgba(13, 202, 240, 1)',
                        pointBorderColor: 'white',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }
                ];
                evolutionChart = new Chart(evolutionCtx, {
                    type: 'bar',
                    data: { labels: chartData.labels, datasets: datasets },
                    options: options
                });
            } else if (type === 'area') {
                datasets = [
                    {
                        label: 'Non corrigés',
                        data: chartData.non_corriges,
                        backgroundColor: 'rgba(255, 193, 7, 0.6)',
                        borderColor: 'rgba(255, 193, 7, 1)',
                        borderWidth: 2,
                        fill: 'origin',
                        tension: 0.4,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Corrigés',
                        data: chartData.corriges,
                        backgroundColor: 'rgba(25, 135, 84, 0.6)',
                        borderColor: 'rgba(25, 135, 84, 1)',
                        borderWidth: 2,
                        fill: '-1',
                        tension: 0.4,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Taux correction (%)',
                        data: chartData.taux_correction,
                        backgroundColor: 'rgba(13, 202, 240, 0.2)',
                        borderColor: 'rgba(13, 202, 240, 1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y1',
                        pointBackgroundColor: 'rgba(13, 202, 240, 1)',
                        pointBorderColor: 'white',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }
                ];
                evolutionChart = new Chart(evolutionCtx, {
                    type: 'line',
                    data: { labels: chartData.labels, datasets: datasets },
                    options: options
                });
            } else { // line (default)
                datasets = [
                    {
                        label: 'Total retours',
                        data: chartData.retours,
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y',
                        pointBackgroundColor: 'rgba(220, 53, 69, 1)',
                        pointBorderColor: 'white',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    },
                    {
                        label: 'Corrigés',
                        data: chartData.corriges,
                        backgroundColor: 'rgba(25, 135, 84, 0.1)',
                        borderColor: 'rgba(25, 135, 84, 1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y',
                        pointBackgroundColor: 'rgba(25, 135, 84, 1)',
                        pointBorderColor: 'white',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    },
                    {
                        label: 'Non corrigés',
                        data: chartData.non_corriges,
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        borderColor: 'rgba(255, 193, 7, 1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y',
                        pointBackgroundColor: 'rgba(255, 193, 7, 1)',
                        pointBorderColor: 'white',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    },
                    {
                        label: 'Taux correction (%)',
                        data: chartData.taux_correction,
                        backgroundColor: 'rgba(13, 202, 240, 0.1)',
                        borderColor: 'rgba(13, 202, 240, 1)',
                        borderWidth: 3,
                        fill: false,
                        tension: 0.4,
                        yAxisID: 'y1',
                        pointBackgroundColor: 'rgba(13, 202, 240, 1)',
                        pointBorderColor: 'white',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8,
                        borderDash: [5, 5]
                    }
                ];
                evolutionChart = new Chart(evolutionCtx, {
                    type: 'line',
                    data: { labels: chartData.labels, datasets: datasets },
                    options: options
                });
            }
        }

        // Créer le graphique initial
        createEvolutionChart('line');

        // Calculer et afficher les statistiques
        var maxRetours = Math.max(...chartData.retours);
        var maxRetoursIndex = chartData.retours.indexOf(maxRetours);
        var maxCorrection = Math.max(...chartData.taux_correction);
        var avgRetours = Math.round(chartData.retours.reduce((a, b) => a + b, 0) / chartData.retours.length);

        // Calculer la tendance
        var firstHalf = chartData.retours.slice(0, Math.ceil(chartData.retours.length / 2));
        var secondHalf = chartData.retours.slice(Math.ceil(chartData.retours.length / 2));
        var avgFirst = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
        var avgSecond = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
        var tendance = avgSecond > avgFirst ? '↗ Hausse' : (avgSecond < avgFirst ? '↘ Baisse' : '→ Stable');

        document.getElementById('maxRetours').textContent = maxRetours + ' (' + chartData.labels[maxRetoursIndex] + ')';
        document.getElementById('maxCorrection').textContent = maxCorrection + '%';
        document.getElementById('avgRetours').textContent = avgRetours + ' retours';
        document.getElementById('tendance').textContent = tendance;

        // Gestionnaires d'événements pour changer le type de graphique
        document.getElementById('lineChart').addEventListener('change', function() {
            if (this.checked) createEvolutionChart('line');
        });
        document.getElementById('barChart').addEventListener('change', function() {
            if (this.checked) createEvolutionChart('bar');
        });
        document.getElementById('areaChart').addEventListener('change', function() {
            if (this.checked) createEvolutionChart('area');
        });
    <?php endif; ?>
    
    // Graphique des retours par CDM
    <?php if (count($cdm_retours) > 0 && $total_retours > 0): ?>
        var cdmRetoursCtx = document.getElementById('cdmRetoursChart').getContext('2d');
        var cdmRetoursChart = new Chart(cdmRetoursCtx, {
            type: 'pie',
            data: {
                labels: [
                    <?php 
                        $cdm_names = [];
                        foreach ($cdm_retours as $cdm) {
                            if ($cdm['nb_retours'] > 0) {
                                $cdm_names[] = $cdm['nom'];
                            }
                        }
                        echo "'" . implode("', '", $cdm_names) . "'"; 
                    ?>
                ],
                datasets: [{
                    data: [
                        <?php 
                            $cdm_data = [];
                            foreach ($cdm_retours as $cdm) {
                                if ($cdm['nb_retours'] > 0) {
                                    $cdm_data[] = $cdm['nb_retours'];
                                }
                            }
                            echo implode(', ', $cdm_data); 
                        ?>
                    ],
                    backgroundColor: [
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(25, 135, 84, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(13, 202, 240, 0.7)',
                        'rgba(108, 117, 125, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(214, 51, 132, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    <?php endif; ?>
    
    // Graphique des raisons de retour
    <?php if (count($raisons_retour) > 0 && array_sum(array_column($raisons_retour, 'nb_retours')) > 0): ?>
        var raisonsCtx = document.getElementById('raisonsChart').getContext('2d');
        var raisonsChart = new Chart(raisonsCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    <?php 
                        $raison_names = [];
                        foreach ($raisons_retour as $raison) {
                            if ($raison['nb_retours'] > 0) {
                                $raison_names[] = $raison['raison'];
                            }
                        }
                        echo "'" . implode("', '", $raison_names) . "'"; 
                    ?>
                ],
                datasets: [{
                    data: [
                        <?php 
                            $raison_data = [];
                            foreach ($raisons_retour as $raison) {
                                if ($raison['nb_retours'] > 0) {
                                    $raison_data[] = $raison['nb_retours'];
                                }
                            }
                            echo implode(', ', $raison_data); 
                        ?>
                    ],
                    backgroundColor: [
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(25, 135, 84, 0.7)',
                        'rgba(13, 202, 240, 0.7)',
                        'rgba(108, 117, 125, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(214, 51, 132, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    <?php endif; ?>
});
</script>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
