<?php
/**
 * Fonctions utilitaires pour l'application
 *
 * Ce fichier contient des fonctions utilitaires utilisées
 * dans toute l'application.
 */

/**
 * Redirige vers une URL avec un message flash
 *
 * @param string $url URL de redirection
 * @param string $message Message à afficher
 * @param string $type Type de message (success, info, warning, danger)
 * @return void
 */
function redirect($url, $message = '', $type = '') {
    if (!empty($message)) {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }

    header('Location: ' . $url);
    exit;
}

/**
 * Affiche un message flash
 *
 * @return void
 */
function displayFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';

        echo '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">';
        echo $message;
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
        echo '</div>';

        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
    }
}

/**
 * Vérifie si l'utilisateur est authentifié
 *
 * @return bool
 */
function isAuthenticated() {
    return isset($_SESSION['user_id']);
}

/**
 * Vérifie si l'utilisateur est administrateur
 *
 * @return bool
 */
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Vérifie si l'application nécessite une installation
 *
 * @return bool
 */
function needsInstallation() {
    try {
        $db = Database::getInstance();
        $sql = "SHOW TABLES LIKE 'users'";
        $result = $db->query($sql);

        return $result->rowCount() === 0;
    } catch (Exception $e) {
        return true;
    }
}

/**
 * Génère un jeton CSRF
 *
 * @return string
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }

    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Vérifie un jeton CSRF
 *
 * @param string $token Jeton à vérifier
 * @return bool
 */
function verifyCSRFToken($token) {
    if (!isset($_SESSION[CSRF_TOKEN_NAME]) || empty($token)) {
        return false;
    }

    return hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Formate une date
 *
 * @param string $date Date à formater
 * @param string $format Format de sortie
 * @return string Date formatée
 */
function formatDate($date, $format = DATE_FORMAT) {
    if (empty($date) || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
        return '';
    }

    $datetime = new DateTime($date);
    return $datetime->format($format);
}

/**
 * Enregistre une activité dans le journal
 *
 * @param int $user_id ID de l'utilisateur
 * @param string $action Action effectuée
 * @param string $details Détails de l'action
 * @return void
 */
function logActivity($user_id, $action, $details = '') {
    try {
        $db = Database::getInstance();

        // Vérifier si l'utilisateur existe
        if ($user_id > 0) {
            $check_user = "SELECT COUNT(*) as count FROM users WHERE id = ?";
            $user_exists = $db->single($check_user, [$user_id]);

            if ($user_exists['count'] == 0) {
                // L'utilisateur n'existe pas, utiliser NULL pour user_id
                $user_id = null;
            }
        }

        $sql = "INSERT INTO activity_logs (user_id, action, details, ip_address, created_at)
                VALUES (?, ?, ?, ?, NOW())";
        $db->query($sql, [$user_id, $action, $details, $_SERVER['REMOTE_ADDR']]);
    } catch (Exception $e) {
        // Ignorer les erreurs de journalisation
    }
}

/**
 * Génère un numéro de lot unique
 *
 * @return string
 */
function generateLotNumber() {
    $prefix = 'LOT';
    $year = date('Y');
    $month = date('m');

    try {
        $db = Database::getInstance();
        $sql = "SELECT MAX(CAST(SUBSTRING(numero_lot, 10) AS UNSIGNED)) as max_num
                FROM lots
                WHERE numero_lot LIKE ?";
        $result = $db->single($sql, [$prefix . $year . $month . '%']);

        $max_num = $result['max_num'] ?? 0;
        $next_num = $max_num + 1;

        return $prefix . $year . $month . str_pad($next_num, 4, '0', STR_PAD_LEFT);
    } catch (Exception $e) {
        // En cas d'erreur, générer un numéro basé sur le timestamp
        return $prefix . $year . $month . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
}

/**
 * Génère un numéro de bordereau unique
 *
 * @return string
 */
function generateBordereauNumber() {
    $prefix = 'BR';
    $year = date('Y');
    $month = date('m');

    try {
        $db = Database::getInstance();
        $sql = "SELECT MAX(CAST(SUBSTRING(numero_bordereau, 9) AS UNSIGNED)) as max_num
                FROM dossiers_retournes
                WHERE numero_bordereau LIKE ?";
        $result = $db->single($sql, [$prefix . $year . $month . '%']);

        $max_num = $result['max_num'] ?? 0;
        $next_num = $max_num + 1;

        return $prefix . $year . $month . str_pad($next_num, 4, '0', STR_PAD_LEFT);
    } catch (Exception $e) {
        // En cas d'erreur, générer un numéro basé sur le timestamp
        return $prefix . $year . $month . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
}

/**
 * Récupère la liste des CDM
 *
 * @param bool $active_only Ne récupérer que les CDM actifs
 * @return array
 */
function getCDMList($active_only = true) {
    try {
        $db = Database::getInstance();
        $sql = "SELECT id, nom FROM cdm";

        if ($active_only) {
            $sql .= " WHERE active = 1";
        }

        $sql .= " ORDER BY nom";

        return $db->all($sql);
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Récupère la liste des actes
 *
 * @param bool $active_only Ne récupérer que les actes actifs
 * @param bool $include_prix Inclure le prix des actes
 * @return array
 */
function getActesList($active_only = true, $include_prix = true) {
    try {
        $db = Database::getInstance();

        // Vérifier si la colonne 'active' existe
        $check_column = "SHOW COLUMNS FROM actes LIKE 'active'";
        $column_exists = $db->query($check_column)->rowCount() > 0;

        // Vérifier si la colonne 'prix' existe
        $check_prix_column = "SHOW COLUMNS FROM actes LIKE 'prix'";
        $prix_column_exists = $db->query($check_prix_column)->rowCount() > 0;

        if ($include_prix && $prix_column_exists) {
            $sql = "SELECT id, nom, prix FROM actes";
        } else {
            $sql = "SELECT id, nom FROM actes";
        }

        if ($active_only && $column_exists) {
            $sql .= " WHERE active = 1";
        }

        $sql .= " ORDER BY nom";

        return $db->all($sql);
    } catch (Exception $e) {
        // En cas d'erreur, retourner tous les actes sans condition
        try {
            $db = Database::getInstance();
            if ($include_prix) {
                $sql = "SELECT id, nom, prix FROM actes ORDER BY nom";
            } else {
                $sql = "SELECT id, nom FROM actes ORDER BY nom";
            }
            return $db->all($sql);
        } catch (Exception $e) {
            return [];
        }
    }
}

/**
 * Récupère la liste des motifs de retour
 *
 * @param bool $active_only Ne récupérer que les motifs actifs
 * @return array
 */
function getRaisonsRetourList($active_only = true) {
    try {
        $db = Database::getInstance();
        $sql = "SELECT id, raison FROM raisons_retour";

        if ($active_only) {
            $sql .= " WHERE active = 1";
        }

        $sql .= " ORDER BY raison";

        return $db->all($sql);
    } catch (Exception $e) {
        return [];
    }
}
?>
