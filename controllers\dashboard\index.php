<?php
// Contrôleur pour le tableau de bord principal

// Titre de la page
$page_title = 'Tableau de bord';
$page = 'dashboard';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Statistiques des 30 derniers jours
$date_30_days_ago = date('Y-m-d', strtotime('-30 days'));

// Nombre total de lots
$sql = "SELECT COUNT(*) as total FROM lots";
$result = $db->single($sql);
$total_lots = $result['total'] ?? 0;

// Nombre de lots des 30 derniers jours
$sql = "SELECT COUNT(*) as total FROM lots WHERE created_at >= ?";
$result = $db->single($sql, [$date_30_days_ago]);
$recent_lots = $result['total'] ?? 0;

// Nombre total de dossiers
$sql = "SELECT COUNT(*) as total FROM dossiers";
$result = $db->single($sql);
$total_dossiers = $result['total'] ?? 0;

// Nombre de dossiers des 30 derniers jours
$sql = "SELECT COUNT(*) as total FROM dossiers WHERE created_at >= ?";
$result = $db->single($sql, [$date_30_days_ago]);
$recent_dossiers = $result['total'] ?? 0;

// Nombre de dossiers reçus
$sql = "SELECT COUNT(*) as total FROM dossiers WHERE recu = 1";
$result = $db->single($sql);
$dossiers_recus = $result['total'] ?? 0;

// Nombre de dossiers en attente
$sql = "SELECT COUNT(*) as total FROM dossiers WHERE recu = 0";
$result = $db->single($sql);
$dossiers_en_attente = $result['total'] ?? 0;

// Nombre de dossiers retournés
$sql = "SELECT COUNT(*) as total FROM dossiers_retournes";
$result = $db->single($sql);
$total_retours = $result['total'] ?? 0;

// Nombre de dossiers retournés des 30 derniers jours
$sql = "SELECT COUNT(*) as total FROM dossiers_retournes WHERE created_at >= ?";
$result = $db->single($sql, [$date_30_days_ago]);
$recent_retours = $result['total'] ?? 0;

// Nombre de dossiers retournés corrigés
$sql = "SELECT COUNT(*) as total FROM dossiers_retournes WHERE corrige = 1";
$result = $db->single($sql);
$retours_corriges = $result['total'] ?? 0;

// Nombre de dossiers retournés non corrigés
$sql = "SELECT COUNT(*) as total FROM dossiers_retournes WHERE corrige = 0";
$result = $db->single($sql);
$retours_non_corriges = $result['total'] ?? 0;

// Nombre total de CDM
$sql = "SELECT COUNT(*) as total FROM cdm WHERE active = 1";
$result = $db->single($sql);
$total_cdm = $result['total'] ?? 0;

// Nombre total d'utilisateurs
$sql = "SELECT COUNT(*) as total FROM users WHERE active = 1";
$result = $db->single($sql);
$total_users = $result['total'] ?? 0;

// Activité par CDM (30 derniers jours)
$sql = "SELECT c.nom, COUNT(d.id) as total_dossiers
        FROM cdm c
        LEFT JOIN lots l ON c.id = l.cdm_id
        LEFT JOIN dossiers d ON l.id = d.lot_id
        WHERE l.created_at >= ?
        GROUP BY c.id
        ORDER BY total_dossiers DESC
        LIMIT 5";
$cdm_activity = $db->all($sql, [$date_30_days_ago]);

// Actes les plus fréquents (30 derniers jours)
$sql = "SELECT a.nom, COUNT(d.id) as total
        FROM actes a
        JOIN dossiers d ON a.id = d.acte_id
        WHERE d.created_at >= ?
        GROUP BY a.id
        ORDER BY total DESC
        LIMIT 5";
$top_actes = $db->all($sql, [$date_30_days_ago]);

// Raisons de retour les plus fréquentes (30 derniers jours)
$sql = "SELECT r.raison, COUNT(dr.id) as total
        FROM raisons_retour r
        JOIN dossiers_retournes dr ON r.id = dr.raison_retour_id
        WHERE dr.created_at >= ?
        GROUP BY r.id
        ORDER BY total DESC
        LIMIT 5";
$top_raisons = $db->all($sql, [$date_30_days_ago]);

// CDM avec le plus de retours (60 derniers jours)
$date_60_days_ago = date('Y-m-d', strtotime('-60 days'));
$sql = "SELECT c.nom, COUNT(dr.id) as total_retours
        FROM cdm c
        JOIN dossiers_retournes dr ON c.id = dr.cdm_id
        WHERE dr.created_at >= ?
        GROUP BY c.id
        ORDER BY total_retours DESC
        LIMIT 5";
$cdm_retours = $db->all($sql, [$date_60_days_ago]);

// Derniers lots créés
$sql = "SELECT l.id, l.numero_lot, c.nom as cdm_nom, l.date_demande, l.statut, 
               (SELECT COUNT(*) FROM dossiers WHERE lot_id = l.id) as nb_dossiers
        FROM lots l
        JOIN cdm c ON l.cdm_id = c.id
        ORDER BY l.created_at DESC
        LIMIT 5";
$recent_lots_list = $db->all($sql);

// Derniers dossiers retournés
$sql = "SELECT dr.id, dr.numero_bordereau, c.nom as cdm_nom, dr.numero_dossier, 
               dr.nom, dr.date_retour, dr.corrige
        FROM dossiers_retournes dr
        JOIN cdm c ON dr.cdm_id = c.id
        ORDER BY dr.created_at DESC
        LIMIT 5";
$recent_retours_list = $db->all($sql);

// Charger la vue
require_once VIEWS_PATH . 'dashboard/index.php';
?>
