<?php
// Vue pour le tableau de bord principal
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-tachometer-alt"></i> Tableau de bord</h1>
        <div>
            <button id="printPage" class="btn btn-outline-secondary">
                <i class="fas fa-print"></i> Imprimer
            </button>
        </div>
    </div>
    
    <!-- Statistiques générales -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stat-card primary">
                <div class="card-body">
                    <div class="stat-icon">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="stat-value"><?php echo number_format($recent_dossiers, 0, ',', ' '); ?></div>
                    <div class="stat-label">Dossiers (30j)</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stat-card success">
                <div class="card-body">
                    <div class="stat-icon">
                        <i class="fas fa-hospital"></i>
                    </div>
                    <div class="stat-value"><?php echo number_format($total_cdm, 0, ',', ' '); ?></div>
                    <div class="stat-label">CDM actifs</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stat-card warning">
                <div class="card-body">
                    <div class="stat-icon">
                        <i class="fas fa-procedures"></i>
                    </div>
                    <div class="stat-value"><?php echo count($top_actes) > 0 ? count($top_actes) : 0; ?></div>
                    <div class="stat-label">Actes fréquents</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stat-card danger">
                <div class="card-body">
                    <div class="stat-icon">
                        <i class="fas fa-undo-alt"></i>
                    </div>
                    <div class="stat-value"><?php echo number_format($recent_retours, 0, ',', ' '); ?></div>
                    <div class="stat-label">Retours (30j)</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Graphiques -->
    <div class="row mb-4">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Activité par CDM (30 derniers jours)</h5>
                </div>
                <div class="card-body">
                    <?php if (count($cdm_activity) > 0): ?>
                        <canvas id="cdmActivityChart" class="dashboard-chart" 
                                data-labels='<?php echo json_encode(array_column($cdm_activity, 'nom')); ?>' 
                                data-values='<?php echo json_encode(array_column($cdm_activity, 'total_dossiers')); ?>'>
                        </canvas>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Aucune donnée disponible pour cette période.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Actes les plus fréquents (30 derniers jours)</h5>
                </div>
                <div class="card-body">
                    <?php if (count($top_actes) > 0): ?>
                        <canvas id="actesDistributionChart" class="dashboard-chart" 
                                data-labels='<?php echo json_encode(array_column($top_actes, 'nom')); ?>' 
                                data-values='<?php echo json_encode(array_column($top_actes, 'total')); ?>'>
                        </canvas>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Aucune donnée disponible pour cette période.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Raisons de retour (30 derniers jours)</h5>
                </div>
                <div class="card-body">
                    <?php if (count($top_raisons) > 0): ?>
                        <canvas id="raisonsRetourChart" class="dashboard-chart" 
                                data-labels='<?php echo json_encode(array_column($top_raisons, 'raison')); ?>' 
                                data-values='<?php echo json_encode(array_column($top_raisons, 'total')); ?>'>
                        </canvas>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Aucune donnée disponible pour cette période.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-hospital"></i> CDM avec le plus de retours (60 derniers jours)</h5>
                </div>
                <div class="card-body">
                    <?php if (count($cdm_retours) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>CDM</th>
                                        <th class="text-end">Nombre de retours</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cdm_retours as $cdm): ?>
                                        <tr>
                                            <td><?php echo $cdm['nom']; ?></td>
                                            <td class="text-end"><?php echo $cdm['total_retours']; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Aucune donnée disponible pour cette période.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Dernières activités -->
    <div class="row mb-4">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-layer-group"></i> Derniers lots créés</h5>
                </div>
                <div class="card-body">
                    <?php if (count($recent_lots_list) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>N° Lot</th>
                                        <th>CDM</th>
                                        <th>Date demande</th>
                                        <th>Dossiers</th>
                                        <th>Statut</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_lots_list as $lot): ?>
                                        <tr>
                                            <td><?php echo $lot['numero_lot']; ?></td>
                                            <td><?php echo $lot['cdm_nom']; ?></td>
                                            <td><?php echo formatDate($lot['date_demande']); ?></td>
                                            <td><?php echo $lot['nb_dossiers']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $lot['statut'] === 'en_attente' ? 'warning' : 
                                                        ($lot['statut'] === 'recu' ? 'info' : 
                                                        ($lot['statut'] === 'traite' ? 'success' : 'secondary')); 
                                                ?>">
                                                    <?php 
                                                        echo $lot['statut'] === 'en_attente' ? 'En attente' : 
                                                            ($lot['statut'] === 'recu' ? 'Reçu' : 
                                                            ($lot['statut'] === 'traite' ? 'Traité' : 'Archivé')); 
                                                    ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <a href="index.php?page=lots/view&id=<?php echo $lot['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-end mt-3">
                            <a href="index.php?page=lots" class="btn btn-outline-primary btn-sm">
                                Voir tous les lots <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Aucun lot n'a été créé récemment.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-undo-alt"></i> Derniers dossiers retournés</h5>
                </div>
                <div class="card-body">
                    <?php if (count($recent_retours_list) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>N° Bordereau</th>
                                        <th>CDM</th>
                                        <th>N° Dossier</th>
                                        <th>Nom</th>
                                        <th>Date retour</th>
                                        <th>Statut</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_retours_list as $retour): ?>
                                        <tr>
                                            <td><?php echo $retour['numero_bordereau']; ?></td>
                                            <td><?php echo $retour['cdm_nom']; ?></td>
                                            <td><?php echo $retour['numero_dossier']; ?></td>
                                            <td><?php echo $retour['nom']; ?></td>
                                            <td><?php echo formatDate($retour['date_retour']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $retour['corrige'] ? 'success' : 'danger'; ?>">
                                                    <?php echo $retour['corrige'] ? 'Corrigé' : 'Non corrigé'; ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <a href="index.php?page=retours/view&id=<?php echo $retour['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-end mt-3">
                            <a href="index.php?page=retours" class="btn btn-outline-primary btn-sm">
                                Voir tous les retours <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Aucun dossier n'a été retourné récemment.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Résumé des statistiques -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Résumé des statistiques</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total des lots</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($total_lots, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total des dossiers</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($total_dossiers, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Dossiers reçus</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($dossiers_recus, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Dossiers en attente</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($dossiers_en_attente, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total des retours</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($total_retours, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Retours corrigés</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($retours_corriges, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Retours non corrigés</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($retours_non_corriges, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Utilisateurs actifs</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($total_users, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
