<?php
// Vue pour le tableau de bord principal
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container-fluid">
    <!-- En-tête moderne -->
    <div class="card mb-4 border-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px;">
        <div class="card-body text-white p-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2" style="font-weight: 700;">
                        <i class="fas fa-chart-pie me-3"></i>Tableau de Bord
                    </h1>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-calendar me-2"></i>Vue d'ensemble des activités et statistiques
                    </p>
                </div>
                <div class="text-end">
                    <button id="printPage" class="btn btn-outline-light btn-lg rounded-pill">
                        <i class="fas fa-print me-2"></i>Imprimer
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistiques générales modernes -->
    <div class="row mb-5">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 hover-lift" style="border-radius: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="p-3 rounded-circle" style="background: rgba(255,255,255,0.2);">
                                <i class="fas fa-folder-open fa-2x"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="h3 mb-1 fw-bold"><?php echo number_format($recent_dossiers, 0, ',', ' '); ?></div>
                            <div class="small opacity-75">Dossiers (30j)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 hover-lift" style="border-radius: 15px; background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="card-body text-white p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="p-3 rounded-circle" style="background: rgba(255,255,255,0.2);">
                                <i class="fas fa-hospital fa-2x"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="h3 mb-1 fw-bold"><?php echo number_format($total_cdm, 0, ',', ' '); ?></div>
                            <div class="small opacity-75">CDM Actifs</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 hover-lift" style="border-radius: 15px; background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                <div class="card-body text-white p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="p-3 rounded-circle" style="background: rgba(255,255,255,0.2);">
                                <i class="fas fa-procedures fa-2x"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="h3 mb-1 fw-bold"><?php echo count($top_actes) > 0 ? count($top_actes) : 0; ?></div>
                            <div class="small opacity-75">Actes Fréquents</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 hover-lift" style="border-radius: 15px; background: linear-gradient(135deg, #fc4a1a 0%, #f7b733 100%);">
                <div class="card-body text-white p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="p-3 rounded-circle" style="background: rgba(255,255,255,0.2);">
                                <i class="fas fa-undo-alt fa-2x"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="h3 mb-1 fw-bold"><?php echo number_format($recent_retours, 0, ',', ' '); ?></div>
                            <div class="small opacity-75">Retours (30j)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Graphiques modernes et compacts -->
    <div class="row mb-5">
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px;">
                <div class="card-header border-0 text-white" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 15px 15px 0 0;">
                    <h6 class="mb-0 fw-bold">
                        <i class="fas fa-chart-bar me-2"></i>Activité par CDM (30 derniers jours)
                    </h6>
                </div>
                <div class="card-body p-3">
                    <?php if (count($cdm_activity) > 0): ?>
                        <div style="height: 250px; position: relative;">
                            <canvas id="cdmActivityChart"
                                    data-labels='<?php echo json_encode(array_column($cdm_activity, 'nom')); ?>'
                                    data-values='<?php echo json_encode(array_column($cdm_activity, 'total_dossiers')); ?>'
                                    style="max-height: 250px;">
                            </canvas>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune donnée disponible pour cette période.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px;">
                <div class="card-header border-0 text-white" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); border-radius: 15px 15px 0 0;">
                    <h6 class="mb-0 fw-bold">
                        <i class="fas fa-chart-pie me-2"></i>Actes les plus fréquents (30 derniers jours)
                    </h6>
                </div>
                <div class="card-body p-3">
                    <?php if (count($top_actes) > 0): ?>
                        <div style="height: 250px; position: relative;">
                            <canvas id="actesDistributionChart"
                                    data-labels='<?php echo json_encode(array_column($top_actes, 'nom')); ?>'
                                    data-values='<?php echo json_encode(array_column($top_actes, 'total')); ?>'
                                    style="max-height: 250px;">
                            </canvas>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune donnée disponible pour cette période.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-5">
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px;">
                <div class="card-header border-0 text-white" style="background: linear-gradient(135deg, #fc4a1a 0%, #f7b733 100%); border-radius: 15px 15px 0 0;">
                    <h6 class="mb-0 fw-bold">
                        <i class="fas fa-exclamation-triangle me-2"></i>Raisons de retour (30 derniers jours)
                    </h6>
                </div>
                <div class="card-body p-3">
                    <?php if (count($top_raisons) > 0): ?>
                        <div style="height: 250px; position: relative;">
                            <canvas id="raisonsRetourChart"
                                    data-labels='<?php echo json_encode(array_column($top_raisons, 'raison')); ?>'
                                    data-values='<?php echo json_encode(array_column($top_raisons, 'total')); ?>'
                                    style="max-height: 250px;">
                            </canvas>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-triangle fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune donnée disponible pour cette période.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px;">
                <div class="card-header border-0 text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px 15px 0 0;">
                    <h6 class="mb-0 fw-bold">
                        <i class="fas fa-hospital me-2"></i>CDM avec le plus de retours (60 derniers jours)
                    </h6>
                </div>
                <div class="card-body p-3">
                    <?php if (count($cdm_retours) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0 fw-bold">CDM</th>
                                        <th class="border-0 fw-bold text-end">Retours</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cdm_retours as $index => $cdm): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-primary me-2"><?php echo $index + 1; ?></span>
                                                    <?php echo htmlspecialchars($cdm['nom']); ?>
                                                </div>
                                            </td>
                                            <td class="text-end">
                                                <span class="badge bg-danger"><?php echo $cdm['total_retours']; ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-hospital fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune donnée disponible pour cette période.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Dernières activités -->
    <div class="row mb-4">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-layer-group"></i> Derniers lots créés</h5>
                </div>
                <div class="card-body">
                    <?php if (count($recent_lots_list) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>N° Lot</th>
                                        <th>CDM</th>
                                        <th>Date demande</th>
                                        <th>Dossiers</th>
                                        <th>Statut</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_lots_list as $lot): ?>
                                        <tr>
                                            <td><?php echo $lot['numero_lot']; ?></td>
                                            <td><?php echo $lot['cdm_nom']; ?></td>
                                            <td><?php echo formatDate($lot['date_demande']); ?></td>
                                            <td><?php echo $lot['nb_dossiers']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $lot['statut'] === 'en_attente' ? 'warning' : 
                                                        ($lot['statut'] === 'recu' ? 'info' : 
                                                        ($lot['statut'] === 'traite' ? 'success' : 'secondary')); 
                                                ?>">
                                                    <?php 
                                                        echo $lot['statut'] === 'en_attente' ? 'En attente' : 
                                                            ($lot['statut'] === 'recu' ? 'Reçu' : 
                                                            ($lot['statut'] === 'traite' ? 'Traité' : 'Archivé')); 
                                                    ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <a href="index.php?page=lots/view&id=<?php echo $lot['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-end mt-3">
                            <a href="index.php?page=lots" class="btn btn-outline-primary btn-sm">
                                Voir tous les lots <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Aucun lot n'a été créé récemment.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-undo-alt"></i> Derniers dossiers retournés</h5>
                </div>
                <div class="card-body">
                    <?php if (count($recent_retours_list) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>N° Bordereau</th>
                                        <th>CDM</th>
                                        <th>N° Dossier</th>
                                        <th>Nom</th>
                                        <th>Date retour</th>
                                        <th>Statut</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_retours_list as $retour): ?>
                                        <tr>
                                            <td><?php echo $retour['numero_bordereau']; ?></td>
                                            <td><?php echo $retour['cdm_nom']; ?></td>
                                            <td><?php echo $retour['numero_dossier']; ?></td>
                                            <td><?php echo $retour['nom']; ?></td>
                                            <td><?php echo formatDate($retour['date_retour']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $retour['corrige'] ? 'success' : 'danger'; ?>">
                                                    <?php echo $retour['corrige'] ? 'Corrigé' : 'Non corrigé'; ?>
                                                </span>
                                            </td>
                                            <td class="text-end">
                                                <a href="index.php?page=retours/view&id=<?php echo $retour['id']; ?>" class="btn btn-sm btn-primary btn-action" data-bs-toggle="tooltip" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-end mt-3">
                            <a href="index.php?page=retours" class="btn btn-outline-primary btn-sm">
                                Voir tous les retours <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Aucun dossier n'a été retourné récemment.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Résumé des statistiques -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Résumé des statistiques</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total des lots</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($total_lots, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total des dossiers</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($total_dossiers, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Dossiers reçus</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($dossiers_recus, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Dossiers en attente</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($dossiers_en_attente, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Total des retours</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($total_retours, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Retours corrigés</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($retours_corriges, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Retours non corrigés</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($retours_non_corriges, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Utilisateurs actifs</h6>
                                    <p class="card-text fs-4 fw-bold"><?php echo number_format($total_users, 0, ',', ' '); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
