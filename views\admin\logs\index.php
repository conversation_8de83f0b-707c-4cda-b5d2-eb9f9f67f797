<?php
// Vue pour la gestion des journaux d'activité
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-history"></i> Journaux d'activité</h1>
        <div>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#clearLogsModal">
                <i class="fas fa-trash"></i> Vider les journaux
            </button>
        </div>
    </div>
    
    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
    <?php endif; ?>
    
    <!-- Formulaire de filtrage -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="admin/logs">
                
                <div class="col-md-3">
                    <label for="search" class="form-label">Recherche</label>
                    <input type="text" class="form-control" id="search" name="search" value="<?php echo $search; ?>" placeholder="Rechercher...">
                </div>
                
                <div class="col-md-3">
                    <label for="user_id" class="form-label">Utilisateur</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="0">Tous les utilisateurs</option>
                        <?php foreach ($users_list as $user): ?>
                            <option value="<?php echo $user['id']; ?>" <?php echo $user_id == $user['id'] ? 'selected' : ''; ?>>
                                <?php echo $user['username']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="action_type" class="form-label">Type d'action</label>
                    <select class="form-select" id="action_type" name="action_type">
                        <option value="">Tous les types</option>
                        <?php foreach ($action_types as $type): ?>
                            <option value="<?php echo $type['action_type']; ?>" <?php echo $action_type == $type['action_type'] ? 'selected' : ''; ?>>
                                <?php echo ucfirst($type['action_type']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="date_from" class="form-label">Date de début</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                </div>
                
                <div class="col-md-3">
                    <label for="date_to" class="form-label">Date de fin</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                </div>
                
                <div class="col-md-9 d-flex align-items-end">
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> Filtrer
                        </button>
                        <a href="index.php?page=admin/logs" class="btn btn-secondary ms-2">
                            <i class="fas fa-times"></i> Réinitialiser
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Liste des journaux d'activité -->
    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list"></i> Liste des activités</h5>
                <span class="badge bg-primary"><?php echo $total_items; ?> entrée(s)</span>
            </div>
        </div>
        <div class="card-body">
            <?php if (count($logs_list) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Utilisateur</th>
                                <th>Type</th>
                                <th>Action</th>
                                <th>Détails</th>
                                <th>IP</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs_list as $log): ?>
                                <tr>
                                    <td><?php echo formatDate($log['created_at'], DATETIME_FORMAT); ?></td>
                                    <td><?php echo $log['username'] ?? 'Système'; ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $log['action_type'] === 'login' ? 'success' : 
                                                ($log['action_type'] === 'error' ? 'danger' : 
                                                ($log['action_type'] === 'warning' ? 'warning' : 'info')); 
                                        ?>">
                                            <?php echo ucfirst($log['action_type']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo $log['action']; ?></td>
                                    <td><?php echo $log['details']; ?></td>
                                    <td><?php echo $log['ip_address']; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Pagination des journaux">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?php echo $page_num <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="index.php?page=admin/logs&page_num=<?php echo $page_num - 1; ?>&search=<?php echo $search; ?>&user_id=<?php echo $user_id; ?>&action_type=<?php echo $action_type; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>
                            
                            <?php for ($i = max(1, $page_num - 2); $i <= min($total_pages, $page_num + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page_num ? 'active' : ''; ?>">
                                    <a class="page-link" href="index.php?page=admin/logs&page_num=<?php echo $i; ?>&search=<?php echo $search; ?>&user_id=<?php echo $user_id; ?>&action_type=<?php echo $action_type; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <li class="page-item <?php echo $page_num >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="index.php?page=admin/logs&page_num=<?php echo $page_num + 1; ?>&search=<?php echo $search; ?>&user_id=<?php echo $user_id; ?>&action_type=<?php echo $action_type; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune activité trouvée.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal pour vider les journaux -->
<div class="modal fade" id="clearLogsModal" tabindex="-1" aria-labelledby="clearLogsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="index.php?page=admin/logs">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                <input type="hidden" name="action" value="clear">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="clearLogsModalLabel">Vider les journaux d'activité</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
                </div>
                <div class="modal-body">
                    <p>Vous êtes sur le point de supprimer des entrées du journal d'activité. Cette action est irréversible.</p>
                    
                    <div class="mb-3">
                        <label for="days" class="form-label">Supprimer les journaux plus anciens que :</label>
                        <select class="form-select" id="days" name="days">
                            <option value="0">Tous les journaux</option>
                            <option value="7">7 jours</option>
                            <option value="30" selected>30 jours</option>
                            <option value="90">90 jours</option>
                            <option value="180">180 jours</option>
                            <option value="365">365 jours</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">Vider les journaux</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
