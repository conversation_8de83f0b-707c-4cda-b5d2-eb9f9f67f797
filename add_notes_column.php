<?php
// Script pour ajouter la colonne notes à la table bordereaux_retour
require_once 'config/config.php';
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    
    // Vérifier si la colonne notes existe déjà
    $sql = "SHOW COLUMNS FROM bordereaux_retour LIKE 'notes'";
    $result = $db->query($sql);
    
    if ($result->rowCount() === 0) {
        // La colonne n'existe pas, on l'ajoute
        $sql = "ALTER TABLE bordereaux_retour ADD COLUMN notes TEXT NULL AFTER date_bordereau";
        $db->query($sql);
        echo "<p>La colonne 'notes' a été ajoutée avec succès à la table 'bordereaux_retour'.</p>";
    } else {
        echo "<p>La colonne 'notes' existe déjà dans la table 'bordereaux_retour'.</p>";
    }
    
    // Vérifier la structure mise à jour
    $sql = "DESCRIBE bordereaux_retour";
    $columns = $db->all($sql);
    
    echo "<h3>Structure mise à jour de la table bordereaux_retour</h3>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage();
}
?>
