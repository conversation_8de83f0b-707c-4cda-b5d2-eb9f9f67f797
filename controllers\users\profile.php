<?php
// Contrôleur pour la page de profil utilisateur

// Titre de la page
$page_title = 'Mon profil';
$page = 'profile';

// Vérifier si l'utilisateur est connecté
if (!isAuthenticated()) {
    redirect('index.php?page=login', 'Veuillez vous connecter pour accéder à cette page.', 'warning');
}

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Récupérer les informations de l'utilisateur
$user_id = $_SESSION['user_id'];
$sql = "SELECT * FROM users WHERE id = ?";
$user = $db->single($sql, [$user_id]);

if (!$user) {
    redirect('index.php?page=dashboard', 'Utilisateur non trouvé.', 'danger');
}

// Traitement du formulaire de mise à jour du profil
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier le jeton CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        redirect('index.php?page=profile', 'Erreur de sécurité. Veuillez réessayer.', 'danger');
    }
    
    // Récupérer et nettoyer les données du formulaire
    $email = clean($_POST['email'] ?? '');
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Valider les données
    $errors = [];
    
    if (empty($email)) {
        $errors[] = 'L\'adresse e-mail est requise.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'L\'adresse e-mail n\'est pas valide.';
    }
    
    // Vérifier si l'utilisateur souhaite changer son mot de passe
    $change_password = !empty($current_password) || !empty($new_password) || !empty($confirm_password);
    
    if ($change_password) {
        if (empty($current_password)) {
            $errors[] = 'Le mot de passe actuel est requis pour changer de mot de passe.';
        } elseif (!password_verify($current_password, $user['password'])) {
            $errors[] = 'Le mot de passe actuel est incorrect.';
        }
        
        if (empty($new_password)) {
            $errors[] = 'Le nouveau mot de passe est requis.';
        } elseif (strlen($new_password) < 8) {
            $errors[] = 'Le nouveau mot de passe doit contenir au moins 8 caractères.';
        }
        
        if ($new_password !== $confirm_password) {
            $errors[] = 'Les mots de passe ne correspondent pas.';
        }
    }
    
    // S'il y a des erreurs, rediriger vers le formulaire avec les erreurs
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = ['email' => $email];
        redirect('index.php?page=profile', 'Veuillez corriger les erreurs.', 'danger');
    }
    
    try {
        // Mettre à jour les informations de l'utilisateur
        if ($change_password) {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET email = ?, password = ?, updated_at = NOW() WHERE id = ?";
            $params = [$email, $hashed_password, $user_id];
        } else {
            $sql = "UPDATE users SET email = ?, updated_at = NOW() WHERE id = ?";
            $params = [$email, $user_id];
        }
        
        $db->query($sql, $params);
        
        // Journaliser l'action
        logActivity($user_id, 'Mise à jour du profil', 'Profil mis à jour avec succès');
        
        // Rediriger vers la page de profil avec un message de succès
        redirect('index.php?page=profile', 'Votre profil a été mis à jour avec succès.', 'success');
        
    } catch (Exception $e) {
        redirect('index.php?page=profile', 'Erreur lors de la mise à jour du profil: ' . $e->getMessage(), 'danger');
    }
}

// Récupérer les données du formulaire en cas d'erreur
$form_data = $_SESSION['form_data'] ?? [
    'email' => $user['email']
];

// Récupérer les erreurs du formulaire
$form_errors = $_SESSION['form_errors'] ?? [];

// Nettoyer les données de session
unset($_SESSION['form_data']);
unset($_SESSION['form_errors']);

// Charger la vue
require_once VIEWS_PATH . 'users/profile.php';
?>
