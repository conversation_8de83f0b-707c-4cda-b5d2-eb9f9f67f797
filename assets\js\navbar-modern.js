/**
 * Script pour améliorer l'interactivité de la navbar moderne
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Animation pour les liens de navigation
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        // Effet de survol avec animation
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.15)';
        });
        
        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            }
        });
    });
    
    // Animation pour les éléments dropdown
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    
    dropdownItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(8px)';
            this.style.paddingLeft = '20px';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
            this.style.paddingLeft = '12px';
        });
    });
    
    // Effet de pulsation pour les notifications (si présentes)
    const badges = document.querySelectorAll('.badge');
    badges.forEach(badge => {
        if (badge.textContent.trim() !== '0' && badge.textContent.trim() !== '') {
            badge.style.animation = 'pulse 2s infinite';
        }
    });
    
    // Animation pour le logo
    const logo = document.querySelector('.navbar-brand');
    if (logo) {
        logo.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05) rotate(2deg)';
        });
        
        logo.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    }
    
    // Effet de scroll pour la navbar
    let lastScrollTop = 0;
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', function() {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scroll vers le bas - cacher la navbar
            navbar.style.transform = 'translateY(-100%)';
            navbar.style.transition = 'transform 0.3s ease-in-out';
        } else {
            // Scroll vers le haut - montrer la navbar
            navbar.style.transform = 'translateY(0)';
            navbar.style.transition = 'transform 0.3s ease-in-out';
        }
        
        // Changer l'opacité selon le scroll
        if (scrollTop > 50) {
            navbar.style.background = 'linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 100%)';
            navbar.style.backdropFilter = 'blur(15px)';
        } else {
            navbar.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            navbar.style.backdropFilter = 'blur(10px)';
        }
        
        lastScrollTop = scrollTop;
    });
    
    // Animation pour les dropdowns
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (toggle && menu) {
            toggle.addEventListener('click', function() {
                setTimeout(() => {
                    if (menu.classList.contains('show')) {
                        menu.style.animation = 'slideDown 0.3s ease-out';
                    }
                }, 10);
            });
        }
    });
    
    // Effet de typing pour le sous-titre (optionnel)
    const subtitle = document.querySelector('.navbar-brand small');
    if (subtitle) {
        const text = subtitle.textContent;
        subtitle.textContent = '';
        let i = 0;
        
        function typeWriter() {
            if (i < text.length) {
                subtitle.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        }
        
        // Démarrer l'animation après 1 seconde
        setTimeout(typeWriter, 1000);
    }
    
    // Amélioration de l'accessibilité
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
    
    // Indicateur de page active amélioré
    const currentPage = window.location.search;
    const navItems = document.querySelectorAll('.nav-link');
    
    navItems.forEach(item => {
        if (item.href.includes(currentPage) && currentPage !== '') {
            item.classList.add('active');
            item.style.background = 'rgba(255,255,255,0.2)';
            item.style.backdropFilter = 'blur(10px)';
            item.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.15)';
        }
    });
});

// Fonction pour ajouter des effets de particules (optionnel)
function addParticleEffect() {
    const navbar = document.querySelector('.navbar');
    
    for (let i = 0; i < 5; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'absolute';
        particle.style.width = '4px';
        particle.style.height = '4px';
        particle.style.background = 'rgba(255,255,255,0.3)';
        particle.style.borderRadius = '50%';
        particle.style.pointerEvents = 'none';
        particle.style.animation = `float ${3 + Math.random() * 2}s infinite ease-in-out`;
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        
        navbar.appendChild(particle);
        
        // Supprimer la particule après l'animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 5000);
    }
}

// CSS pour l'animation float
const style = document.createElement('style');
style.textContent = `
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
        50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);

// Ajouter l'effet de particules toutes les 10 secondes (optionnel)
// setInterval(addParticleEffect, 10000);
