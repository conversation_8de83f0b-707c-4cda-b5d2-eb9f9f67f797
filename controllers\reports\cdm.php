<?php
// Contrôleur pour le rapport d'analyse des CDM

// Titre de la page
$page_title = 'Analyse par CDM';
$page = 'reports/cdm';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de filtrage
$periode = isset($_GET['periode']) ? clean($_GET['periode']) : '30';
$cdm_id = isset($_GET['cdm_id']) ? intval($_GET['cdm_id']) : 0;
$date_debut = isset($_GET['date_debut']) ? clean($_GET['date_debut']) : '';
$date_fin = isset($_GET['date_fin']) ? clean($_GET['date_fin']) : '';

// Déterminer les dates de début et de fin
if (!empty($date_debut) && !empty($date_fin)) {
    $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
    $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
} else {
    switch ($periode) {
        case '7':
            $date_debut_formatted = date('Y-m-d', strtotime('-7 days'));
            break;
        case '30':
            $date_debut_formatted = date('Y-m-d', strtotime('-30 days'));
            break;
        case '90':
            $date_debut_formatted = date('Y-m-d', strtotime('-90 days'));
            break;
        case '365':
            $date_debut_formatted = date('Y-m-d', strtotime('-365 days'));
            break;
        default:
            $date_debut_formatted = date('Y-m-d', strtotime('-30 days'));
    }
    $date_fin_formatted = date('Y-m-d');
}

// Obtenir la liste des CDM pour le filtre
$cdm_list = getCDMList();

// Activité par CDM (nombre de lots et dossiers)
$sql = "SELECT c.id, c.nom, 
               COUNT(DISTINCT l.id) as nb_lots,
               COUNT(d.id) as nb_dossiers,
               SUM(CASE WHEN d.recu = 1 THEN 1 ELSE 0 END) as nb_recus,
               SUM(CASE WHEN d.recu = 0 THEN 1 ELSE 0 END) as nb_en_attente
        FROM cdm c
        LEFT JOIN lots l ON c.id = l.cdm_id AND l.date_demande BETWEEN ? AND ?
        LEFT JOIN dossiers d ON l.id = d.lot_id
        WHERE c.active = 1";

$params = [$date_debut_formatted, $date_fin_formatted];

if ($cdm_id > 0) {
    $sql .= " AND c.id = ?";
    $params[] = $cdm_id;
}

$sql .= " GROUP BY c.id, c.nom
          ORDER BY nb_dossiers DESC";

$cdm_activity = $db->all($sql, $params);

// Dossiers retournés par CDM
$sql = "SELECT c.id, c.nom, 
               COUNT(dr.id) as nb_retours,
               SUM(CASE WHEN dr.corrige = 1 THEN 1 ELSE 0 END) as nb_corriges,
               SUM(CASE WHEN dr.corrige = 0 THEN 1 ELSE 0 END) as nb_non_corriges
        FROM cdm c
        LEFT JOIN dossiers_retournes dr ON c.id = dr.cdm_id AND dr.date_retour BETWEEN ? AND ?
        WHERE c.active = 1";

$params = [$date_debut_formatted, $date_fin_formatted];

if ($cdm_id > 0) {
    $sql .= " AND c.id = ?";
    $params[] = $cdm_id;
}

$sql .= " GROUP BY c.id, c.nom
          ORDER BY nb_retours DESC";

$cdm_retours = $db->all($sql, $params);

// Raisons de retour par CDM
$sql = "SELECT c.id, c.nom, r.raison, COUNT(dr.id) as nb_retours
        FROM cdm c
        JOIN dossiers_retournes dr ON c.id = dr.cdm_id
        JOIN raisons_retour r ON dr.raison_retour_id = r.id
        WHERE dr.date_retour BETWEEN ? AND ?";

$params = [$date_debut_formatted, $date_fin_formatted];

if ($cdm_id > 0) {
    $sql .= " AND c.id = ?";
    $params[] = $cdm_id;
}

$sql .= " GROUP BY c.id, c.nom, r.raison
          ORDER BY c.nom, nb_retours DESC";

$cdm_raisons = $db->all($sql, $params);

// Regrouper les raisons par CDM
$raisons_by_cdm = [];
foreach ($cdm_raisons as $row) {
    if (!isset($raisons_by_cdm[$row['id']])) {
        $raisons_by_cdm[$row['id']] = [
            'nom' => $row['nom'],
            'raisons' => []
        ];
    }
    $raisons_by_cdm[$row['id']]['raisons'][] = [
        'raison' => $row['raison'],
        'nb_retours' => $row['nb_retours']
    ];
}

// Évolution mensuelle des dossiers par CDM
$sql = "SELECT c.id, c.nom, 
               DATE_FORMAT(l.date_demande, '%Y-%m') as mois,
               COUNT(d.id) as nb_dossiers
        FROM cdm c
        JOIN lots l ON c.id = l.cdm_id
        JOIN dossiers d ON l.id = d.lot_id
        WHERE l.date_demande BETWEEN ? AND ?";

$params = [$date_debut_formatted, $date_fin_formatted];

if ($cdm_id > 0) {
    $sql .= " AND c.id = ?";
    $params[] = $cdm_id;
}

$sql .= " GROUP BY c.id, c.nom, DATE_FORMAT(l.date_demande, '%Y-%m')
          ORDER BY c.nom, mois";

$evolution_mensuelle = $db->all($sql, $params);

// Regrouper l'évolution par CDM
$evolution_by_cdm = [];
$all_months = [];

foreach ($evolution_mensuelle as $row) {
    if (!isset($evolution_by_cdm[$row['id']])) {
        $evolution_by_cdm[$row['id']] = [
            'nom' => $row['nom'],
            'data' => []
        ];
    }
    $evolution_by_cdm[$row['id']]['data'][$row['mois']] = $row['nb_dossiers'];
    if (!in_array($row['mois'], $all_months)) {
        $all_months[] = $row['mois'];
    }
}

// Trier les mois
sort($all_months);

// Compléter les mois manquants
foreach ($evolution_by_cdm as $cdm_id => $cdm_data) {
    foreach ($all_months as $month) {
        if (!isset($cdm_data['data'][$month])) {
            $evolution_by_cdm[$cdm_id]['data'][$month] = 0;
        }
    }
    // Trier par mois
    ksort($evolution_by_cdm[$cdm_id]['data']);
}

// Formater les mois pour l'affichage
$formatted_months = [];
foreach ($all_months as $month) {
    $date = DateTime::createFromFormat('Y-m', $month);
    $formatted_months[$month] = $date->format('M Y');
}

// Charger la vue
require_once VIEWS_PATH . 'reports/cdm.php';
?>
