<?php
// Script pour créer la table bordereaux_retour et migrer les données existantes
require_once 'config/config.php';
require_once 'config/database.php';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

try {
    // Vérifier si la table bordereaux_retour existe
    $sql = "SHOW TABLES LIKE 'bordereaux_retour'";
    $result = $db->query($sql);
    $table_exists = $result->rowCount() > 0;
    
    if ($table_exists) {
        echo "<h2>La table 'bordereaux_retour' existe déjà.</h2>";
    } else {
        echo "<h2>Création de la table 'bordereaux_retour'...</h2>";
        
        // Créer la table bordereaux_retour
        $sql = "CREATE TABLE bordereaux_retour (
            id INT AUTO_INCREMENT PRIMARY KEY,
            numero INT NOT NULL,
            cdm_id INT NOT NULL,
            date_bordereau DATE NOT NULL,
            created_by INT NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NULL,
            UNIQUE KEY unique_bordereau (numero, cdm_id),
            FOREIGN KEY (cdm_id) REFERENCES cdm(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )";
        
        $db->query($sql);
        echo "<p>Table 'bordereaux_retour' créée avec succès.</p>";
        
        // Migrer les données existantes
        echo "<h2>Migration des données existantes...</h2>";
        
        $sql = "INSERT IGNORE INTO bordereaux_retour (numero, cdm_id, date_bordereau, created_by, created_at)
                SELECT DISTINCT numero_bordereau, cdm_id, date_bordereau, created_by, created_at
                FROM dossiers_retournes";
        
        $result = $db->query($sql);
        $count = $result->rowCount();
        
        echo "<p>$count bordereaux migrés avec succès.</p>";
    }
    
    // Afficher les bordereaux existants
    $sql = "SELECT br.numero, c.nom as cdm_nom, br.date_bordereau, 
                  (SELECT COUNT(*) FROM dossiers_retournes dr WHERE dr.numero_bordereau = br.numero AND dr.cdm_id = br.cdm_id) as nb_dossiers
           FROM bordereaux_retour br
           JOIN cdm c ON br.cdm_id = c.id
           ORDER BY br.date_bordereau DESC
           LIMIT 10";
    $bordereaux = $db->all($sql);
    
    echo "<h2>Derniers bordereaux dans la table 'bordereaux_retour':</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Numéro</th><th>CDM</th><th>Date</th><th>Nombre de dossiers</th></tr>";
    
    foreach ($bordereaux as $bordereau) {
        echo "<tr>";
        echo "<td>" . $bordereau['numero'] . "</td>";
        echo "<td>" . $bordereau['cdm_nom'] . "</td>";
        echo "<td>" . $bordereau['date_bordereau'] . "</td>";
        echo "<td>" . $bordereau['nb_dossiers'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<p>Tout est prêt ! Vous pouvez maintenant <a href='index.php?page=retours'>retourner à la liste des bordereaux</a>.</p>";
    
} catch (Exception $e) {
    echo "<h2>Erreur:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
