<?php
// Contrôleur pour le rapport d'analyse des dossiers retournés

// Titre de la page
$page_title = 'Analyse des retours';
$page = 'reports/retours';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Paramètres de filtrage
$periode = isset($_GET['periode']) ? clean($_GET['periode']) : '60';
$cdm_id = isset($_GET['cdm_id']) ? intval($_GET['cdm_id']) : 0;
$raison_id = isset($_GET['raison_id']) ? intval($_GET['raison_id']) : 0;
$date_debut = isset($_GET['date_debut']) ? clean($_GET['date_debut']) : '';
$date_fin = isset($_GET['date_fin']) ? clean($_GET['date_fin']) : '';

// Déterminer les dates de début et de fin
if (!empty($date_debut) && !empty($date_fin)) {
    $date_debut_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_debut)));
    $date_fin_formatted = date('Y-m-d', strtotime(str_replace('/', '-', $date_fin)));
} else {
    switch ($periode) {
        case '30':
            $date_debut_formatted = date('Y-m-d', strtotime('-30 days'));
            break;
        case '60':
            $date_debut_formatted = date('Y-m-d', strtotime('-60 days'));
            break;
        case '90':
            $date_debut_formatted = date('Y-m-d', strtotime('-90 days'));
            break;
        case '365':
            $date_debut_formatted = date('Y-m-d', strtotime('-365 days'));
            break;
        default:
            $date_debut_formatted = date('Y-m-d', strtotime('-60 days'));
    }
    $date_fin_formatted = date('Y-m-d');
}

// Obtenir la liste des CDM pour le filtre
$cdm_list = getCDMList();

// Obtenir la liste des raisons de retour pour le filtre
$raisons_list = getRaisonsRetourList();

// Dossiers retournés par CDM
$sql = "SELECT c.id, c.nom, 
               COUNT(dr.id) as nb_retours,
               SUM(CASE WHEN dr.corrige = 1 THEN 1 ELSE 0 END) as nb_corriges,
               SUM(CASE WHEN dr.corrige = 0 THEN 1 ELSE 0 END) as nb_non_corriges
        FROM cdm c
        LEFT JOIN dossiers_retournes dr ON c.id = dr.cdm_id AND dr.date_retour BETWEEN ? AND ?
        WHERE c.active = 1";

$params = [$date_debut_formatted, $date_fin_formatted];

if ($cdm_id > 0) {
    $sql .= " AND c.id = ?";
    $params[] = $cdm_id;
}

if ($raison_id > 0) {
    $sql .= " AND dr.raison_retour_id = ?";
    $params[] = $raison_id;
}

$sql .= " GROUP BY c.id, c.nom
          ORDER BY nb_retours DESC";

$cdm_retours = $db->all($sql, $params);

// Raisons de retour
$sql = "SELECT r.id, r.raison, 
               COUNT(dr.id) as nb_retours,
               SUM(CASE WHEN dr.corrige = 1 THEN 1 ELSE 0 END) as nb_corriges,
               SUM(CASE WHEN dr.corrige = 0 THEN 1 ELSE 0 END) as nb_non_corriges
        FROM raisons_retour r
        LEFT JOIN dossiers_retournes dr ON r.id = dr.raison_retour_id AND dr.date_retour BETWEEN ? AND ?";

$params = [$date_debut_formatted, $date_fin_formatted];

if ($cdm_id > 0) {
    $sql .= " AND dr.cdm_id = ?";
    $params[] = $cdm_id;
}

if ($raison_id > 0) {
    $sql .= " AND r.id = ?";
    $params[] = $raison_id;
}

$sql .= " GROUP BY r.id, r.raison
          ORDER BY nb_retours DESC";

$raisons_retour = $db->all($sql, $params);

// Raisons de retour par CDM
$sql = "SELECT c.id, c.nom, r.id as raison_id, r.raison, COUNT(dr.id) as nb_retours
        FROM cdm c
        JOIN dossiers_retournes dr ON c.id = dr.cdm_id
        JOIN raisons_retour r ON dr.raison_retour_id = r.id
        WHERE dr.date_retour BETWEEN ? AND ?";

$params = [$date_debut_formatted, $date_fin_formatted];

if ($cdm_id > 0) {
    $sql .= " AND c.id = ?";
    $params[] = $cdm_id;
}

if ($raison_id > 0) {
    $sql .= " AND r.id = ?";
    $params[] = $raison_id;
}

$sql .= " GROUP BY c.id, c.nom, r.id, r.raison
          ORDER BY c.nom, nb_retours DESC";

$cdm_raisons = $db->all($sql, $params);

// Regrouper les raisons par CDM
$raisons_by_cdm = [];
foreach ($cdm_raisons as $row) {
    if (!isset($raisons_by_cdm[$row['id']])) {
        $raisons_by_cdm[$row['id']] = [
            'nom' => $row['nom'],
            'raisons' => []
        ];
    }
    $raisons_by_cdm[$row['id']]['raisons'][] = [
        'id' => $row['raison_id'],
        'raison' => $row['raison'],
        'nb_retours' => $row['nb_retours']
    ];
}

// Évolution mensuelle des retours
$sql = "SELECT DATE_FORMAT(dr.date_retour, '%Y-%m') as mois,
               COUNT(dr.id) as nb_retours,
               SUM(CASE WHEN dr.corrige = 1 THEN 1 ELSE 0 END) as nb_corriges
        FROM dossiers_retournes dr
        WHERE dr.date_retour BETWEEN ? AND ?";

$params = [$date_debut_formatted, $date_fin_formatted];

if ($cdm_id > 0) {
    $sql .= " AND dr.cdm_id = ?";
    $params[] = $cdm_id;
}

if ($raison_id > 0) {
    $sql .= " AND dr.raison_retour_id = ?";
    $params[] = $raison_id;
}

$sql .= " GROUP BY DATE_FORMAT(dr.date_retour, '%Y-%m')
          ORDER BY mois";

$evolution_mensuelle = $db->all($sql, $params);

// Formater les mois pour l'affichage
$formatted_months = [];
$retours_data = [];
$corriges_data = [];

foreach ($evolution_mensuelle as $row) {
    $date = DateTime::createFromFormat('Y-m', $row['mois']);
    $formatted_months[] = $date->format('M Y');
    $retours_data[] = $row['nb_retours'];
    $corriges_data[] = $row['nb_corriges'];
}

// Charger la vue
require_once VIEWS_PATH . 'reports/retours.php';
?>
