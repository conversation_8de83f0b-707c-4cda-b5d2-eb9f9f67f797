<?php
// Vue pour la gestion des raisons de retour
require_once VIEWS_PATH . 'layouts/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-undo-alt"></i> Gestion des raisons de retour</h1>
        <div>
            <a href="index.php?page=admin/raisons/create" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouvelle raison
            </a>
        </div>
    </div>

    <!-- Formulaire de recherche -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="admin/raisons">

                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" value="<?php echo $search; ?>" placeholder="Rechercher une raison de retour...">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>

                <div class="col-md-4 text-end">
                    <?php if (!empty($search)): ?>
                        <a href="index.php?page=admin/raisons" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Réinitialiser
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des raisons de retour -->
    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list"></i> Liste des raisons de retour</h5>
                <span class="badge bg-primary"><?php echo $total_items; ?> raison(s)</span>
            </div>
        </div>
        <div class="card-body">
            <?php if (count($raisons_list) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th>Raison</th>
                                <th>Description</th>
                                <th>Statut</th>
                                <th>Date de création</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($raisons_list as $raison): ?>
                                <tr>
                                    <td><?php echo $raison['raison']; ?></td>
                                    <td><?php echo $raison['description']; ?></td>
                                    <td>
                                        <?php if (isset($raison['active'])): ?>
                                            <span class="badge bg-<?php echo $raison['active'] ? 'success' : 'danger'; ?>">
                                                <?php echo $raison['active'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo formatDate($raison['created_at'], DATETIME_FORMAT); ?></td>
                                    <td class="text-end">
                                        <a href="index.php?page=admin/raisons/edit&id=<?php echo $raison['id']; ?>" class="btn btn-sm btn-warning btn-action" data-bs-toggle="tooltip" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="index.php?page=admin/raisons/delete&id=<?php echo $raison['id']; ?>" class="btn btn-sm btn-danger btn-action confirm-delete" data-bs-toggle="tooltip" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Pagination des raisons">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?php echo $page_num <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="index.php?page=admin/raisons&page_num=<?php echo $page_num - 1; ?>&search=<?php echo $search; ?>">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>

                            <?php for ($i = max(1, $page_num - 2); $i <= min($total_pages, $page_num + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page_num ? 'active' : ''; ?>">
                                    <a class="page-link" href="index.php?page=admin/raisons&page_num=<?php echo $i; ?>&search=<?php echo $search; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <li class="page-item <?php echo $page_num >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="index.php?page=admin/raisons&page_num=<?php echo $page_num + 1; ?>&search=<?php echo $search; ?>">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>

            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune raison de retour trouvée.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once VIEWS_PATH . 'layouts/footer.php'; ?>
