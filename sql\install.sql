-- Script d'installation de la base de données pour l'application de gestion des lots

-- Suppression des tables si elles existent déjà
DROP TABLE IF EXISTS logs;
DROP TABLE IF EXISTS dossiers_retournes;
DROP TABLE IF EXISTS dossiers;
DROP TABLE IF EXISTS lots;
DROP TABLE IF EXISTS raisons_retour;
DROP TABLE IF EXISTS actes;
DROP TABLE IF EXISTS cdm;
DROP TABLE IF EXISTS users;

-- Table des utilisateurs
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL,
    nom VARCHAR(100),
    prenom VARCHAR(100),
    role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
    active TINYINT(1) NOT NULL DEFAULT 1,
    last_login DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des centres médicaux (CDM)
CREATE TABLE cdm (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    adresse TEXT,
    telephone VARCHAR(20),
    email VARCHAR(100),
    contact_nom VARCHAR(100),
    active TINYINT(1) NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des actes médicaux
CREATE TABLE actes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20),
    nom VARCHAR(100) NOT NULL,
    description TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des raisons de retour
CREATE TABLE raisons_retour (
    id INT AUTO_INCREMENT PRIMARY KEY,
    raison VARCHAR(100) NOT NULL,
    description TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des lots
CREATE TABLE lots (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero_lot VARCHAR(20) NOT NULL UNIQUE,
    cdm_id INT NOT NULL,
    date_demande DATE NOT NULL,
    date_reception DATE,
    statut ENUM('en_attente', 'recu', 'traite', 'archive') NOT NULL DEFAULT 'en_attente',
    notes TEXT,
    created_by INT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (cdm_id) REFERENCES cdm(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des dossiers
CREATE TABLE dossiers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lot_id INT NOT NULL,
    numero_dossier VARCHAR(50) NOT NULL,
    numero_adherent VARCHAR(50) NOT NULL,
    nom VARCHAR(100) NOT NULL,
    beneficiaire VARCHAR(100),
    acte_id INT,
    montant DECIMAL(10,2),
    numero_bon VARCHAR(50),
    recu TINYINT(1) NOT NULL DEFAULT 0,
    notes TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (lot_id) REFERENCES lots(id) ON DELETE CASCADE,
    FOREIGN KEY (acte_id) REFERENCES actes(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des dossiers retournés
CREATE TABLE dossiers_retournes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero_bordereau INT NOT NULL,
    cdm_id INT NOT NULL,
    date_bordereau DATE NOT NULL,
    numero_dossier VARCHAR(50) NOT NULL,
    numero_adherent VARCHAR(50) NOT NULL,
    nom VARCHAR(100) NOT NULL,
    beneficiaire VARCHAR(100),
    acte_id INT,
    montant DECIMAL(10,2),
    raison_retour_id INT,
    date_retour DATE NOT NULL,
    date_correction DATE,
    corrige TINYINT(1) NOT NULL DEFAULT 0,
    notes TEXT,
    created_by INT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (cdm_id) REFERENCES cdm(id) ON DELETE RESTRICT,
    FOREIGN KEY (acte_id) REFERENCES actes(id) ON DELETE SET NULL,
    FOREIGN KEY (raison_retour_id) REFERENCES raisons_retour(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des logs d'activité
CREATE TABLE logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    created_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertion des données par défaut

-- Utilisateur administrateur par défaut (mot de passe: admin123)
INSERT INTO users (username, password, email, nom, prenom, role, active, created_at) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Administrateur', 'Système', 'admin', 1, NOW());

-- Motifs de retour par défaut
INSERT INTO raisons_retour (raison, description) VALUES
('Document manquant', 'Un ou plusieurs documents requis sont manquants dans le dossier'),
('Signature manquante', 'La signature du patient ou du médecin est manquante'),
('Informations incomplètes', 'Les informations fournies sont incomplètes'),
('Erreur d\'identification', 'Erreur dans le numéro d\'adhérent ou les informations d\'identification'),
('Document illisible', 'Un ou plusieurs documents sont illisibles'),
('Acte non couvert', 'L\'acte médical n\'est pas couvert par l\'assurance'),
('Date expirée', 'La date de validité du document est expirée'),
('Doublon', 'Le dossier est un doublon d\'un dossier déjà traité');

-- Actes médicaux par défaut
INSERT INTO actes (code, nom, description) VALUES
('CONS', 'Consultation', 'Consultation médicale générale'),
('SPEC', 'Consultation spécialiste', 'Consultation avec un médecin spécialiste'),
('RADIO', 'Radiographie', 'Examen radiographique'),
('ECHO', 'Échographie', 'Examen échographique'),
('LABO', 'Analyses de laboratoire', 'Analyses biologiques et tests de laboratoire'),
('DENT', 'Soins dentaires', 'Soins dentaires généraux'),
('KINE', 'Kinésithérapie', 'Séance de kinésithérapie'),
('OPHTA', 'Ophtalmologie', 'Consultation et soins ophtalmologiques'),
('HOSP', 'Hospitalisation', 'Frais d\'hospitalisation'),
('CHIR', 'Chirurgie', 'Intervention chirurgicale');
