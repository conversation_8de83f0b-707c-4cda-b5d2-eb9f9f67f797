<?php
// Contrôleur pour le tableau de bord

// Titre de la page
$page_title = 'Tableau de bord';
$page = 'dashboard';

// Obtenir la connexion à la base de données
$db = Database::getInstance();

// Statistiques générales
$stats = [];

// Nombre total de lots
$sql = "SELECT COUNT(*) as total FROM lots";
$result = $db->single($sql);
$stats['total_lots'] = $result['total'];

// Nombre de lots en attente
$sql = "SELECT COUNT(*) as total FROM lots WHERE statut = 'en_attente'";
$result = $db->single($sql);
$stats['lots_en_attente'] = $result['total'];

// Nombre de lots reçus
$sql = "SELECT COUNT(*) as total FROM lots WHERE statut = 'recu'";
$result = $db->single($sql);
$stats['lots_recus'] = $result['total'];

// Nombre de lots traités
$sql = "SELECT COUNT(*) as total FROM lots WHERE statut = 'traite'";
$result = $db->single($sql);
$stats['lots_traites'] = $result['total'];

// Nombre total de dossiers
$sql = "SELECT COUNT(*) as total FROM dossiers";
$result = $db->single($sql);
$stats['total_dossiers'] = $result['total'];

// Nombre de dossiers reçus
$sql = "SELECT COUNT(*) as total FROM dossiers WHERE recu = 1";
$result = $db->single($sql);
$stats['dossiers_recus'] = $result['total'];

// Nombre de dossiers en attente
$sql = "SELECT COUNT(*) as total FROM dossiers WHERE recu = 0";
$result = $db->single($sql);
$stats['dossiers_en_attente'] = $result['total'];

// Nombre total de dossiers retournés
$sql = "SELECT COUNT(*) as total FROM dossiers_retournes";
$result = $db->single($sql);
$stats['total_retours'] = $result['total'];

// Nombre de dossiers retournés corrigés
$sql = "SELECT COUNT(*) as total FROM dossiers_retournes WHERE corrige = 1";
$result = $db->single($sql);
$stats['retours_corriges'] = $result['total'];

// Nombre de dossiers retournés non corrigés
$sql = "SELECT COUNT(*) as total FROM dossiers_retournes WHERE corrige = 0";
$result = $db->single($sql);
$stats['retours_non_corriges'] = $result['total'];

// Lots récents
$sql = "SELECT l.*, c.nom as cdm_nom, 
               (SELECT COUNT(*) FROM dossiers WHERE lot_id = l.id) as nb_dossiers,
               (SELECT COUNT(*) FROM dossiers WHERE lot_id = l.id AND recu = 1) as nb_recus
        FROM lots l
        JOIN cdm c ON l.cdm_id = c.id
        ORDER BY l.created_at DESC
        LIMIT 5";
$lots_recents = $db->all($sql);

// Dossiers retournés récents
$sql = "SELECT dr.*, c.nom as cdm_nom, r.raison as raison_retour_nom
        FROM dossiers_retournes dr
        JOIN cdm c ON dr.cdm_id = c.id
        JOIN raisons_retour r ON dr.raison_retour_id = r.id
        ORDER BY dr.created_at DESC
        LIMIT 5";
$retours_recents = $db->all($sql);

// Activité par CDM (pour le graphique)
$sql = "SELECT c.nom, COUNT(l.id) as nb_lots
        FROM cdm c
        LEFT JOIN lots l ON c.id = l.cdm_id
        WHERE c.active = 1
        GROUP BY c.id, c.nom
        ORDER BY nb_lots DESC
        LIMIT 10";
$cdm_activity = $db->all($sql);

// Évolution mensuelle des lots (pour le graphique)
$sql = "SELECT DATE_FORMAT(date_demande, '%Y-%m') as mois, COUNT(*) as nb_lots
        FROM lots
        WHERE date_demande >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(date_demande, '%Y-%m')
        ORDER BY mois";
$evolution_lots = $db->all($sql);

// Formater les données pour les graphiques
$cdm_labels = [];
$cdm_data = [];

foreach ($cdm_activity as $cdm) {
    $cdm_labels[] = $cdm['nom'];
    $cdm_data[] = $cdm['nb_lots'];
}

$evolution_labels = [];
$evolution_data = [];

foreach ($evolution_lots as $month) {
    $date = DateTime::createFromFormat('Y-m', $month['mois']);
    $evolution_labels[] = $date->format('M Y');
    $evolution_data[] = $month['nb_lots'];
}

// Charger la vue
require_once VIEWS_PATH . 'dashboard.php';
?>
